# Development-only Dockerfile for faster builds
FROM node:20-alpine

# Set working directory
WORKDIR /app

# Install dependencies for better file watching
RUN apk add --no-cache git

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --silent

# Copy source code
COPY . .

# Expose Vite dev server port
EXPOSE 5173

# Set environment to development
ENV NODE_ENV=development
ENV CHOKIDAR_USEPOLLING=true
ENV WATCHPACK_POLLING=true

# Start development server with host binding for Docker
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0", "--port", "5173"]
