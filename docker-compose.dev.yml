# version: "3.8"

services:
  kb-tracker-frontend-dev:
    image: kb-tracker-frontend-dev:latest
    container_name: kb-tracker-frontend-dev
    ports:
      - "5173:5173" # Vite dev server port
    volumes:
      # Mount source code for hot reloading
      - ./src:/app/src
      - ./public:/app/public
      - ./index.html:/app/index.html
      - ./vite.config.js:/app/vite.config.js
      - ./tailwind.config.js:/app/tailwind.config.js
      - ./postcss.config.js:/app/postcss.config.js
      - ./postcss.config.mjs:/app/postcss.config.mjs
      - ./eslint.config.js:/app/eslint.config.js
      # Exclude node_modules to avoid conflicts
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - CHOKIDAR_USEPOLLING=true # Enable file watching in Docker
      - WATCHPACK_POLLING=true
    restart: unless-stopped
    stdin_open: true # Keep STDIN open for interactive mode
    tty: true # Allocate a pseudo-TTY
    networks:
      - kb-tracker-dev-network

networks:
  kb-tracker-dev-network:
    driver: bridge
