#!/bin/bash

# Docker production script for KB Tracker Frontend
# Usage: ./docker-prod.sh [command] [options]
# Commands: start, stop, restart, logs, shell, build, clean, deploy

set -e

COMPOSE_FILE="docker-compose.yml"
SERVICE_NAME="kb-tracker-frontend"
DEFAULT_PORT=3000

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}🚀 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check if Dock<PERSON> is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
}

# Function to check if Docker buildx is available
check_buildx() {
    if ! docker buildx version > /dev/null 2>&1; then
        print_error "Docker buildx is not installed. Please install Docker Buildx first."
        exit 1
    fi
}

# Function to start production environment
start_prod() {
    local port=${2:-$DEFAULT_PORT}
    print_status "Starting production environment on port $port..."

    # Build the image first if it doesn't exist
    if ! docker image inspect kb-tracker-frontend:latest > /dev/null 2>&1; then
        print_status "Production image not found, building it first..."
        build_prod
    fi

    export HOST_PORT=$port
    docker compose -f $COMPOSE_FILE up -d

    print_success "Production environment started!"
    echo ""
    print_status "Application is available at: http://localhost:$port"
    print_status "Health check: http://localhost:$port/health"
    echo ""
    print_status "Useful commands:"
    echo "  View logs:    ./docker-prod.sh logs"
    echo "  Stop:         ./docker-prod.sh stop"
    echo "  Shell access: ./docker-prod.sh shell"
}

# Function to stop production environment
stop_prod() {
    print_status "Stopping production environment..."
    docker compose -f $COMPOSE_FILE down
    print_success "Production environment stopped!"
}

# Function to restart production environment
restart_prod() {
    print_status "Restarting production environment..."
    docker compose -f $COMPOSE_FILE restart
    print_success "Production environment restarted!"
}

# Function to show logs
show_logs() {
    print_status "Showing production logs (Ctrl+C to exit)..."
    docker compose -f $COMPOSE_FILE logs -f
}

# Function to access shell
access_shell() {
    print_status "Accessing production container shell..."
    docker compose -f $COMPOSE_FILE exec $SERVICE_NAME sh
}

# Function to build production image
build_prod() {
    print_status "Building production image..."
    check_buildx
    docker buildx build --no-cache --target production -t kb-tracker-frontend:latest .
    print_success "Production image built successfully!"
}

# Function to deploy (build and start)
deploy_prod() {
    local port=${2:-$DEFAULT_PORT}
    print_status "Deploying production environment..."

    # Build first
    build_prod

    # Then start
    export HOST_PORT=$port
    docker compose -f $COMPOSE_FILE up -d

    print_success "Production deployment completed!"
    echo ""
    print_status "Application is available at: http://localhost:$port"
    print_status "Health check: http://localhost:$port/health"
}

# Function to clean up
clean_prod() {
    print_warning "This will remove all containers, images, and volumes for production environment."
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Cleaning up production environment..."
        docker compose -f $COMPOSE_FILE down -v --rmi all
        print_success "Production environment cleaned up!"
    else
        print_status "Cleanup cancelled."
    fi
}

# Function to show status
show_status() {
    print_status "Production environment status:"
    docker compose -f $COMPOSE_FILE ps
    echo ""
    print_status "Resource usage:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}" 2>/dev/null || echo "No running containers"
}

# Function to show health
show_health() {
    print_status "Checking application health..."
    local port=${2:-$DEFAULT_PORT}
    
    if curl -f -s "http://localhost:$port/health" > /dev/null; then
        print_success "Application is healthy!"
    else
        print_error "Application health check failed!"
        exit 1
    fi
}

# Function to show help
show_help() {
    echo "KB Tracker Frontend - Production Docker Script"
    echo ""
    echo "Usage: ./docker-prod.sh [command] [port]"
    echo ""
    echo "Commands:"
    echo "  start [port]  Start the production environment (default port: $DEFAULT_PORT)"
    echo "  stop          Stop the production environment"
    echo "  restart       Restart the production environment"
    echo "  logs          Show and follow production logs"
    echo "  shell         Access the production container shell"
    echo "  build         Build the production image"
    echo "  deploy [port] Build and deploy production environment"
    echo "  clean         Clean up all production containers and images"
    echo "  status        Show production environment status and resource usage"
    echo "  health [port] Check application health"
    echo "  help          Show this help message"
    echo ""
    echo "Examples:"
    echo "  ./docker-prod.sh start           # Start on default port ($DEFAULT_PORT)"
    echo "  ./docker-prod.sh start 8080      # Start on port 8080"
    echo "  ./docker-prod.sh deploy 80       # Build and deploy on port 80"
    echo "  ./docker-prod.sh health 3000     # Check health on port 3000"
}

# Main script logic
check_docker

case "${1:-help}" in
    start)
        start_prod "$@"
        ;;
    stop)
        stop_prod
        ;;
    restart)
        restart_prod
        ;;
    logs)
        show_logs
        ;;
    shell)
        access_shell
        ;;
    build)
        build_prod
        ;;
    deploy)
        deploy_prod "$@"
        ;;
    clean)
        clean_prod
        ;;
    status)
        show_status
        ;;
    health)
        show_health "$@"
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
