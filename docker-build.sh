#!/bin/bash

# Docker build script for KB Tracker Frontend
# Usage: ./docker-build.sh [tag]

set -e

# Default values
IMAGE_NAME="kb-tracker-frontend"
TAG=${1:-"latest"}
FULL_IMAGE_NAME="${IMAGE_NAME}:${TAG}"

echo "🐳 Building Docker image: ${FULL_IMAGE_NAME}"
echo "================================================"

# Build the Docker image
docker build \
  --tag "${FULL_IMAGE_NAME}" \
  --build-arg NODE_ENV=production \
  .

echo ""
echo "✅ Build completed successfully!"
echo "📦 Image: ${FULL_IMAGE_NAME}"
echo ""

# Show image details
echo "📊 Image details:"
docker images "${IMAGE_NAME}" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"

echo ""
echo "🚀 To run the container:"
echo "   docker run -d -p 3000:80 --name kb-tracker-frontend ${FULL_IMAGE_NAME}"
echo ""
echo "🐙 To run with Docker Compose:"
echo "   docker-compose up -d"
