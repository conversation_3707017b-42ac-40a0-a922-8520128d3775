#!/bin/bash

# Docker run script for KB Tracker Frontend
# Usage: ./docker-run.sh [port]

set -e

# Default values
IMAGE_NAME="kb-tracker-frontend:latest"
CONTAINER_NAME="kb-tracker-frontend"
HOST_PORT=${1:-3000}
CONTAINER_PORT=80

echo "🚀 Starting KB Tracker Frontend container"
echo "=========================================="
echo "📦 Image: ${IMAGE_NAME}"
echo "🌐 Port mapping: ${HOST_PORT}:${CONTAINER_PORT}"
echo "📛 Container name: ${CONTAINER_NAME}"
echo ""

# Stop and remove existing container if it exists
if docker ps -a --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
    echo "🛑 Stopping existing container..."
    docker stop "${CONTAINER_NAME}" >/dev/null 2>&1 || true
    echo "🗑️  Removing existing container..."
    docker rm "${CONTAINER_NAME}" >/dev/null 2>&1 || true
fi

# Run the container
echo "🏃 Starting new container..."
docker run -d \
  --name "${CONTAINER_NAME}" \
  -p "${HOST_PORT}:${CONTAINER_PORT}" \
  --restart unless-stopped \
  -e NODE_ENV=production \
  "${IMAGE_NAME}"

echo ""
echo "✅ Container started successfully!"
echo "🌐 Application URL: http://localhost:${HOST_PORT}"
echo "🏥 Health check: http://localhost:${HOST_PORT}/health"
echo ""

# Wait a moment for container to start
sleep 2

# Check container status
echo "📊 Container status:"
docker ps --filter "name=${CONTAINER_NAME}" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

echo ""
echo "📝 Useful commands:"
echo "   View logs:    docker logs ${CONTAINER_NAME}"
echo "   Stop:         docker stop ${CONTAINER_NAME}"
echo "   Restart:      docker restart ${CONTAINER_NAME}"
echo "   Shell access: docker exec -it ${CONTAINER_NAME} sh"
