# Loan Activities API Integration

This document provides comprehensive guidance on using the new loan activities API integration in the frontend application.

## Overview

The loan activities integration provides a complete solution for creating loan activities with file attachments, proper validation, and error handling. It includes:

- **Loan Activities Service** (`src/services/loanActivitiesService.js`)
- **Validation Utilities** (`src/utils/validationUtils.js`)
- **Updated Loan Call Form** (`src/components/forms/LoanCallForm.jsx`)
- **Updated Loan Service** (`src/services/loanService.js`)

## API Endpoint

**Endpoint:** `POST /loan-activities`  
**Content-Type:** `multipart/form-data`  
**Purpose:** Create a new loan activity with optional file attachments

## Required Fields

- `rm_user_id` (string, UUID): The UUID of the RM user performing this activity

## Optional Fields

### Basic Loan Information
- `loan_client_id` (string, UUID): UUID of the loan client
- `loan_account_number` (string, max 50 chars): Loan account number
- `purpose_id` (string, UUID): UUID of the purpose of this activity
- `loan_balance` (string): Current loan balance as decimal string
- `arrears_days` (number): Number of days in arrears (integer ≥ 0)
- `comment` (string, max 1000 chars): Comments about the loan activity

### API Tracking
- `via_api` (boolean): Whether this activity was created via API
- `api_call_reference` (string, max 255 chars): API call reference for tracking

### Interaction Details
- `interaction_type` (string, max 50 chars): Type of interaction (e.g., "call", "visit", "email")
- `call_status` (string, max 50 chars): Status of the call
- `visit_status` (string, max 50 chars): Status of the visit
- `call_duration_minutes` (number): Duration of the call in minutes (integer ≥ 0)

### Follow-up Information
- `next_followup_date` (string): Date for next follow-up in ISO 8601 format
- `followup_status` (string, max 50 chars): Status of the follow-up

### File Attachments
- `attachments` (File[]): Array of files to upload

## Usage Examples

### 1. Using the Loan Activities Service Directly

```javascript
import { loanActivitiesService } from '../services/loanActivitiesService';

// Example: Create a loan activity with files
const createLoanActivity = async () => {
  try {
    const activityData = {
      rmUserId: "550e8400-e29b-41d4-a716-************", // Required
      loanClientId: "123e4567-e89b-12d3-a456-************",
      loanAccountNumber: "LA-2024-001",
      purposeId: "789e0123-e89b-12d3-a456-************",
      loanBalance: "50000.00",
      arrearsDays: 30,
      comment: "Customer contacted regarding overdue payment",
      interactionType: "call",
      callStatus: "answered",
      callDurationMinutes: 15,
      nextFollowupDate: "2024-02-15T10:00:00.000Z",
      followupStatus: "pending",
      viaApi: false,
    };

    const files = []; // Array of File objects

    const result = await loanActivitiesService.create(activityData, files);
    console.log('Activity created:', result);
  } catch (error) {
    console.error('Error creating activity:', error.message);
  }
};
```

### 2. Using the Updated Loan Service

```javascript
import { loanService } from '../services/loanService';

// Example: Create a loan activity using the new activities API
const createLoanCall = async () => {
  try {
    const activityData = {
      rmUserId: "550e8400-e29b-41d4-a716-************",
      loanClientId: "123e4567-e89b-12d3-a456-************",
      comment: "Follow-up call completed",
      interactionType: "call",
      callStatus: "answered",
      callDurationMinutes: 10,
    };

    const files = []; // Array of File objects

    const result = await loanService.activities.create(activityData, files);
    console.log('Loan activity created:', result);
  } catch (error) {
    console.error('Error:', error.message);
  }
};

// Example: Get all loan activities
const fetchLoanActivities = async () => {
  try {
    const activities = await loanService.activities.getAll();
    console.log('Activities:', activities);
  } catch (error) {
    console.error('Error fetching activities:', error.message);
  }
};
```

### 3. Using the Updated Loan Call Form

The `LoanCallForm` component has been updated to work with the new API. It now includes:

- **RM User Selection** (required field)
- **Call Duration** field
- **Followup Status** field
- **Proper validation** using the validation utilities
- **File upload** support
- **Error handling** with user-friendly messages

```jsx
import LoanCallForm from '../components/forms/LoanCallForm';

const MyComponent = () => {
  const handleSubmit = async (result, item) => {
    console.log('Loan activity created:', result);
    // Handle successful submission
  };

  const handleClose = () => {
    // Handle form close
  };

  return (
    <LoanCallForm
      item={selectedLoanItem} // Optional: for editing
      onSubmit={handleSubmit}
      onClose={handleClose}
    />
  );
};
```

## Validation

The integration includes comprehensive validation utilities:

### Client-Side Validation

```javascript
import { validateLoanActivityData } from '../utils/validationUtils';

const data = {
  rmUserId: "550e8400-e29b-41d4-a716-************",
  loanClientId: "invalid-uuid", // This will fail validation
  comment: "A".repeat(1001), // This will fail validation (too long)
};

const validation = validateLoanActivityData(data);
if (!validation.isValid) {
  console.log('Validation errors:', validation.errors);
  // ["Loan Client ID must be a valid UUID", "Comment must be 1000 characters or less"]
}
```

### File Upload Validation

```javascript
import { validateFileUpload } from '../utils/validationUtils';

const files = [file1, file2]; // Array of File objects

const validation = validateFileUpload(files, {
  maxFileSize: 10, // 10MB
  maxFiles: 5,
  allowedExtensions: ['.pdf', '.jpg', '.jpeg', '.png', '.doc', '.docx']
});

if (!validation.isValid) {
  console.log('File validation errors:', validation.errors);
}
```

## Error Handling

The service provides comprehensive error handling:

```javascript
try {
  const result = await loanActivitiesService.create(activityData, files);
} catch (error) {
  // Error messages are user-friendly and specific
  if (error.message.includes('Validation failed')) {
    // Handle validation errors
  } else if (error.message.includes('not found')) {
    // Handle 404 errors (RM user, loan client, or purpose not found)
  } else if (error.message.includes('Server error')) {
    // Handle 500+ errors
  } else {
    // Handle other errors
  }
}
```

## Response Format

### Success Response (201 Created)

```typescript
interface LoanActivityResponse {
  id: string;
  loan_client_id?: string;
  loan_account_number?: string;
  purpose_id?: string;
  loan_balance?: string;
  arrears_days?: number;
  comment?: string;
  rm_user_id: string;
  created_at: string;
  updated_at: string;
  via_api?: boolean;
  api_call_reference?: string;
  interaction_type?: string;
  call_status?: string;
  visit_status?: string;
  next_followup_date?: string;
  followup_status?: string;
  call_duration_minutes?: number;
  
  // Related data (populated if available)
  purpose?: {
    id: string;
    name: string;
    description?: string;
  };
  rm_user?: {
    id: string;
    name: string;
    email: string;
    rm_code: string;
  };
  loan_client?: {
    id: string;
    customer_name?: string;
    account_number?: string;
  };
  attachments?: Array<{
    id: string;
    file_url?: string;
    created_at: string;
  }>;
}
```

## Migration from Legacy API

If you're migrating from the old loan calls API, the new service provides backward compatibility:

```javascript
// Old way (still works)
const result = await loanService.calls.create(callData, files);

// New way (recommended)
const result = await loanService.activities.create(activityData, files);
```

The legacy `calls.create` method automatically transforms the data to the new format.

## Best Practices

1. **Always validate data** before submission using the validation utilities
2. **Handle errors gracefully** with user-friendly messages
3. **Use the new activities API** for new implementations
4. **Validate file uploads** before sending to the server
5. **Provide feedback** to users during form submission
6. **Use proper UUID format** for all ID fields
7. **Sanitize string inputs** to prevent issues

## Testing

To test the integration:

1. **Unit Tests**: Test validation functions and service methods
2. **Integration Tests**: Test the complete flow from form to API
3. **File Upload Tests**: Test various file types and sizes
4. **Error Handling Tests**: Test various error scenarios

```javascript
// Example test
import { validateLoanActivityData } from '../utils/validationUtils';

describe('Loan Activity Validation', () => {
  test('should validate required fields', () => {
    const data = { rmUserId: 'invalid' };
    const result = validateLoanActivityData(data);
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain('RM User ID must be a valid UUID');
  });
});
```

## Troubleshooting

### Common Issues

1. **"RM User ID is required"**: Ensure the `rmUserId` field is provided and is a valid UUID
2. **"Validation failed"**: Check all field formats and lengths using the validation utilities
3. **"File upload failed"**: Verify file size and type restrictions
4. **"Server error"**: Check network connectivity and server status

### Debug Mode

Enable debug logging by checking the browser console for detailed API request/response information.

## Support

For additional support or questions about the loan activities integration, refer to:

- API documentation
- Validation utility documentation
- Component documentation
- Service layer documentation
