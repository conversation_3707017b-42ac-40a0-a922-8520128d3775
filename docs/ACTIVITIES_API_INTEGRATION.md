# Activities API Integration Documentation

## Overview

This document describes the integration of real backend API data with the existing Calls and Visits tables, replacing mock data with live data from the `/activities-by-interaction-type` endpoint.

## Features Implemented

### 🚀 **Core Features**
- **Real API Integration**: Connected to backend `/activities-by-interaction-type` endpoint
- **Dynamic Data Loading**: Fetches calls and visits data on component mount
- **Loading States**: Proper loading indicators during data fetching
- **Error Handling**: Comprehensive error handling with user feedback
- **Data Formatting**: Transforms API response to table-compatible format
- **Status Color Coding**: Dynamic status colors based on activity type and status

### 🎯 **User Experience Features**
- **Automatic Loading**: Data loads automatically when pages are accessed
- **Loading Indicators**: Visual feedback during data fetching
- **Error Messages**: User-friendly error notifications
- **Refresh Capability**: Load more functionality refreshes data
- **Responsive Design**: Maintains existing responsive table design

## Implementation Details

### **Files Created/Modified**

#### **New Files:**
1. **`src/services/activitiesService.js`** - Activities API service with data formatting utilities
2. **`docs/ACTIVITIES_API_INTEGRATION.md`** - This documentation

#### **Modified Files:**
1. **`src/pages/Calls.jsx`** - Integrated real API data for calls
2. **`src/pages/Visits.jsx`** - Integrated real API data for visits

### **API Integration**

#### **Backend Endpoints:**
```
GET /api/v1/activities-by-interaction-type?type=call
GET /api/v1/activities-by-interaction-type?type=visit

Response Format:
{
  "data": [
    {
      "id": "activity-uuid",
      "lead_name": "Customer Name",
      "lead_phone": "+************",
      "lead_client_id": "CLI001",
      "interaction_type": "call", // or "visit"
      "activity_type": "Follow Up",
      "call_status": "Success", // for calls
      "visit_status": "Successful", // for visits
      "notes": "Activity notes",
      "call_duration_minutes": 15, // for calls only
      "next_followup_date": "2025-08-05T10:00:00.000Z",
      "performed_by": {
        "name": "John Smith",
        "email": "<EMAIL>",
        "rm_code": "RM001"
      },
      "purpose": {
        "name": "Product Demo",
        "description": "Demonstrate features"
      },
      "created_at": "2025-07-30T10:59:31.042Z"
    }
  ],
  "total": 25,
  "interaction_type": "call",
  "message": "Retrieved 25 call activities successfully"
}
```

#### **Frontend API Calls:**
```javascript
// In activitiesService.js
export const activitiesService = {
  // Get activities by type
  getByInteractionType: async (type) => {
    const response = await instance.get('/activities-by-interaction-type', {
      params: { type }
    });
    return response.data;
  },

  // Specific methods
  getCalls: async () => activitiesService.getByInteractionType('call'),
  getVisits: async () => activitiesService.getByInteractionType('visit'),
};
```

### **Data Transformation**

#### **API Response to Table Format:**
```javascript
export const formatActivitiesForTable = (apiResponse, type) => {
  return apiResponse.data.map((activity) => ({
    id: activity.id,
    name: activity.lead_name || "Unknown Lead",
    anchor: activity.performed_by?.name || "Unknown",
    mobile: activity.lead_phone || "No phone",
    madeBy: activity.performed_by?.name || "Unknown",
    status: type === 'call' ? activity.call_status : activity.visit_status,
    date: formatActivityDate(activity.created_at),
    // Additional fields for detailed view
    clientId: activity.lead_client_id,
    activityType: activity.activity_type,
    notes: activity.notes,
    duration: activity.call_duration_minutes,
    nextFollowup: activity.next_followup_date,
    performedBy: activity.performed_by,
    purpose: activity.purpose,
    createdAt: activity.created_at,
    interactionType: activity.interaction_type,
  }));
};
```

### **Component Architecture**

#### **State Management:**
```javascript
const [loading, setLoading] = useState(true);
const [loadingMore, setLoadingMore] = useState(false);
const [calls, setCalls] = useState([]); // or visits
const [error, setError] = useState(null);
```

#### **Data Fetching:**
```javascript
const fetchCalls = async () => {
  try {
    setLoading(true);
    setError(null);
    
    const response = await activitiesService.getCalls();
    const formattedCalls = formatActivitiesForTable(response, 'call');
    
    setCalls(formattedCalls);
  } catch (error) {
    setError(error.message);
    toast.error(error.message || "Failed to load calls data");
  } finally {
    setLoading(false);
  }
};

useEffect(() => {
  fetchCalls();
}, []);
```

### **Status Color Coding**

#### **Dynamic Status Colors:**
```javascript
export const getActivityStatusColor = (status, type) => {
  const statusLower = status?.toLowerCase() || '';
  
  const statusColors = {
    success: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
    successful: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
    completed: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
    failed: "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400",
    cancelled: "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400",
    pending: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",
    scheduled: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",
    'in-progress': "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400",
  };

  return statusColors[statusLower] || "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
};
```

#### **Status Column Rendering:**
```jsx
{
  key: "status",
  title: "STATUS",
  render: (value) => (
    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getActivityStatusColor(value, 'call')}`}>
      {value || 'Unknown'}
    </span>
  ),
}
```

### **Error Handling**

#### **API Error Types:**
```javascript
// 404 Not Found
"call activities endpoint not found."

// 403 Forbidden
"You do not have permission to view call activities."

// 500+ Server Error
"Server error while fetching call activities. Please try again later."

// Generic Error
"Failed to fetch call activities."
```

#### **User Feedback:**
- **Toast Notifications**: Error messages displayed via react-toastify
- **Loading States**: Visual indicators during data fetching
- **Error State**: Component handles error state gracefully
- **Console Logging**: Detailed logging for debugging

### **Utility Functions**

#### **Date Formatting:**
```javascript
const formatActivityDate = (dateString) => {
  if (!dateString) return "Unknown date";
  
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  });
};
```

#### **Duration Formatting:**
```javascript
export const formatDuration = (minutes) => {
  if (!minutes || minutes === 0) return "N/A";
  
  if (minutes < 60) {
    return `${minutes} min`;
  }
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  return remainingMinutes === 0 ? `${hours}h` : `${hours}h ${remainingMinutes}m`;
};
```

## Usage Instructions

### **For Users:**

#### **Viewing Calls:**
1. Navigate to Calls page
2. Data loads automatically from backend
3. View call details in table format
4. Use "Load More" to refresh data

#### **Viewing Visits:**
1. Navigate to Visits page
2. Data loads automatically from backend
3. View visit details in table format
4. Use "Load More" to refresh data

### **For Developers:**

#### **Adding New Activity Types:**
```javascript
// 1. Add new method to activitiesService
getNewActivityType: async () => {
  return await activitiesService.getByInteractionType('new-type');
},

// 2. Update formatActivitiesForTable to handle new type
export const formatActivitiesForTable = (apiResponse, type) => {
  return apiResponse.data.map((activity) => ({
    // ... existing fields
    status: type === 'new-type' ? activity.new_type_status : activity.call_status,
    // ... other type-specific fields
  }));
};

// 3. Add status colors for new type
export const getActivityStatusColor = (status, type) => {
  // ... existing logic
  if (type === 'new-type') {
    // Add new-type specific status colors
  }
};
```

#### **Customizing Data Display:**
```javascript
// Update column definitions in component
const columns = [
  {
    key: "customField",
    title: "CUSTOM FIELD",
    render: (value, row) => (
      <span className="custom-styling">
        {/* Custom rendering logic */}
      </span>
    ),
  },
  // ... other columns
];
```

## Testing

### **Manual Testing Checklist:**
- [ ] Calls page loads data from API
- [ ] Visits page loads data from API
- [ ] Loading indicators appear during data fetch
- [ ] Error messages display for API failures
- [ ] Status colors display correctly
- [ ] Date formatting works properly
- [ ] Load more functionality works
- [ ] Console logging shows API calls

### **Test Scenarios:**
1. **Successful Load**: Normal data loading flow
2. **Network Error**: Test with disconnected network
3. **Server Error**: Test with 500 response
4. **Empty Data**: Test with empty API response
5. **Invalid Data**: Test with malformed API response

## Performance Considerations

### **Optimizations:**
- **Single API Call**: One call per page load
- **Efficient Formatting**: Minimal data transformation
- **Error Recovery**: Graceful error handling
- **Loading States**: Clear user feedback

### **Limitations:**
- **No Pagination**: Currently loads all data at once
- **No Caching**: Fresh API call on each page load
- **No Real-time Updates**: Manual refresh required

## Future Enhancements

### **Planned Features:**
1. **Pagination**: Load data in chunks
2. **Real-time Updates**: WebSocket integration
3. **Caching**: Client-side data caching
4. **Search/Filter**: Server-side filtering
5. **Sorting**: Server-side sorting

### **Technical Improvements:**
1. **Infinite Scroll**: Replace "Load More" with infinite scroll
2. **Optimistic Updates**: Immediate UI updates for actions
3. **Background Refresh**: Periodic data refresh
4. **Offline Support**: Cache data for offline viewing

## Support

For technical support or feature requests regarding the activities API integration:
1. Check browser console for API call logs
2. Verify backend endpoint availability
3. Test with different data scenarios
4. Contact development team with specific error messages
