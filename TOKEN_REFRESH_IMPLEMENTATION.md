# Automatic Token Refresh Implementation

## Overview

This implementation provides automatic token refresh functionality for the React application using Axios interceptors. When a 401 (Unauthorized) response is received from the API, the system automatically attempts to refresh the access token using the refresh token stored in cookies, without interrupting the user's workflow.

## Key Features

1. **Automatic 401 Detection**: Axios response interceptor detects 401 responses
2. **Token Refresh**: Automatically calls `/auth/token/refresh` endpoint with refresh token
3. **Request Retry**: Retries the original failed request with the new access token
4. **Queue Management**: Queues multiple failed requests during token refresh to avoid race conditions
5. **Session Expiry Modal**: Shows user-friendly modal when refresh token is expired/invalid
6. **Clean Logout**: Clears all tokens and redirects to login when session expires

## Implementation Details

### Files Modified/Created

1. **`src/axios/instance.jsx`** - Enhanced with token refresh logic
2. **`src/components/modals/SessionExpiredModal.jsx`** - New modal component
3. **`src/contexts/AuthContext.jsx`** - Enhanced to handle session expiry events
4. **`src/services/authService.js`** - Added refresh token method

### How It Works

#### 1. Request Flow
```
User makes API request → Axios adds Bearer token → API responds
```

#### 2. 401 Response Handling
```
API returns 401 → Interceptor catches → Check if refresh in progress
├─ If refreshing: Queue request
└─ If not refreshing: Start refresh process
```

#### 3. Token Refresh Process
```
Get refresh token from cookies → Call /auth/token/refresh
├─ Success: Store new access token → Retry original request
└─ Failure: Clear tokens → Show session expired modal
```

#### 4. Queue Management
- Multiple simultaneous requests that fail with 401 are queued
- After successful refresh, all queued requests are retried with new token
- If refresh fails, all queued requests are rejected

### API Endpoint

**POST** `/auth/token/refresh`

**Request Body:**
```json
{
  "refreshToken": "refresh_token_from_cookie"
}
```

**Response:**
```json
{
  "accessToken": "new_access_token"
}
```

### Session Expiry Modal

When refresh token is expired or invalid:
1. All authentication tokens are cleared
2. User state is reset
3. Modal is displayed with options:
   - **Cancel**: Close modal (user stays on current page but will face 401s)
   - **Go to Login**: Redirect to login page

### Token Storage

- **Access Token**: Stored in `accessToken` cookie (15 minutes expiry)
- **Refresh Token**: Stored in `refreshToken` cookie (30 days expiry)
- Both tokens are cleared when session expires

### Event System

The implementation uses a custom event system:
- `sessionExpired` event is dispatched when refresh fails
- AuthContext listens for this event and shows the modal
- This decouples the axios instance from the React components

## Usage

The token refresh system works automatically. No changes are needed in existing components that use the axios instance from `src/axios/instance.jsx`.

### Example API Call
```javascript
import instance from '../axios/instance';

// This will automatically handle token refresh if needed
const response = await instance.get('/api/users');
```

## Testing the Implementation

### Manual Testing Steps

1. **Login to the application**
2. **Wait for access token to expire (15 minutes) or manually delete it from cookies**
3. **Make an API request** (navigate to any page that loads data)
4. **Verify**: Request should succeed after automatic token refresh
5. **Delete refresh token from cookies**
6. **Make another API request**
7. **Verify**: Session expired modal should appear

### Browser Developer Tools Testing

1. Open Network tab in browser dev tools
2. Make API requests and observe:
   - Initial 401 response
   - Automatic refresh token request
   - Retry of original request with new token

## Error Handling

### Scenarios Handled

1. **Access token expired, refresh token valid**: Automatic refresh + retry
2. **Both tokens expired**: Session expired modal
3. **Network error during refresh**: Session expired modal
4. **Invalid refresh token**: Session expired modal
5. **Multiple simultaneous 401s**: Queued and processed together

### Logging

The implementation includes comprehensive console logging:
- Request/response logging
- Token refresh success/failure
- Error details for debugging

## Security Considerations

1. **Secure Cookies**: Tokens stored with secure flags in production
2. **SameSite Protection**: Cookies use `strict` SameSite policy
3. **Automatic Cleanup**: Tokens cleared on session expiry
4. **No Token Exposure**: Tokens not exposed in localStorage or global variables

## Future Enhancements

1. **Token Refresh Warning**: Show warning before token expires
2. **Background Refresh**: Proactively refresh tokens before expiry
3. **Retry Logic**: Add exponential backoff for failed refresh attempts
4. **Analytics**: Track token refresh success/failure rates
