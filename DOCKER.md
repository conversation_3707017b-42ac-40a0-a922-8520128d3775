# Docker Setup for KB Tracker Frontend

This document provides comprehensive instructions for building and running the KB Tracker Frontend application using Docker in both **development** and **production** environments.

## 🐳 Docker Configuration Overview

### Multi-Stage Dockerfile

Our Dockerfile supports multiple environments with optimized stages:

- **Base Stage**: Common dependencies and source code setup
- **Development Stage**: Hot reloading with Vite dev server
- **Builder Stage**: Production build compilation
- **Production Stage**: Optimized Nginx serving

### Key Features

- ✅ **Development**: Hot reloading with volume mounts for instant code changes
- ✅ **Production**: Multi-stage build for smaller final image (~50MB)
- ✅ Nginx with custom configuration for React Router support
- ✅ Gzip compression and caching for optimal performance
- ✅ Security headers and non-root user execution
- ✅ Health check endpoints for monitoring
- ✅ Resource limits and logging for production
- ✅ Easy-to-use scripts for both environments

## 🚀 Quick Start

### 🔧 Development Environment (Recommended for Development)

**Features**: Hot reloading, instant code changes, debugging capabilities

```bash
# Start development environment with hot reloading
./docker-dev.sh start

# View development logs
./docker-dev.sh logs

# Access container shell for debugging
./docker-dev.sh shell

# Stop development environment
./docker-dev.sh stop
```

**Access**: http://localhost:5173 (with hot reloading)

### 🏭 Production Environment

**Features**: Optimized build, Nginx serving, production-ready

```bash
# Deploy production environment (build + start)
./docker-prod.sh deploy

# Or start on custom port
./docker-prod.sh start 8080

# Check application health
./docker-prod.sh health

# View production logs
./docker-prod.sh logs

# Stop production environment
./docker-prod.sh stop
```

**Access**: http://localhost:3000 (or your custom port)

## 📋 Detailed Usage

### 🔧 Development Environment

#### Using the Development Script (Recommended)

```bash
# Available commands
./docker-dev.sh help

# Start development with hot reloading
./docker-dev.sh start

# View real-time logs
./docker-dev.sh logs

# Access container shell
./docker-dev.sh shell

# Restart development environment
./docker-dev.sh restart

# Clean rebuild
./docker-dev.sh build

# Complete cleanup
./docker-dev.sh clean
```

#### Manual Development Setup

```bash
# Build development image
docker-compose -f docker-compose.dev.yml build

# Start development environment
docker-compose -f docker-compose.dev.yml up -d

# View logs
docker-compose -f docker-compose.dev.yml logs -f

# Stop development environment
docker-compose -f docker-compose.dev.yml down
```

#### Development Features

- **Hot Reloading**: Changes in `src/`, `public/`, and config files reflect instantly
- **Volume Mounts**: Source code is mounted for live editing
- **Port 5173**: Vite development server with HMR
- **File Watching**: Optimized for Docker with polling enabled
- **Interactive Mode**: Full debugging capabilities

### 🏭 Production Environment

#### Using the Production Script (Recommended)

```bash
# Available commands
./docker-prod.sh help

# Build and deploy production
./docker-prod.sh deploy

# Start on custom port
./docker-prod.sh start 8080

# Check application health
./docker-prod.sh health

# View production logs
./docker-prod.sh logs

# Show resource usage
./docker-prod.sh status

# Complete cleanup
./docker-prod.sh clean
```

#### Manual Production Setup

```bash
# Build production image
docker-compose build

# Start production environment
docker-compose up -d

# Start on custom port
HOST_PORT=8080 docker-compose up -d

# View logs
docker-compose logs -f

# Stop production environment
docker-compose down
```

#### Production Features

- **Optimized Build**: Multi-stage build with ~50MB final image
- **Nginx Serving**: High-performance static file serving
- **Resource Limits**: Memory and CPU limits for stability
- **Health Checks**: Built-in health monitoring
- **Security**: Read-only filesystem, non-root user
- **Logging**: Structured logging with rotation

## 📁 File Structure

```
.
├── Dockerfile              # Multi-stage Docker configuration
├── docker-compose.yml      # Production Docker Compose
├── docker-compose.dev.yml  # Development Docker Compose
├── docker-dev.sh           # Development script
├── docker-prod.sh          # Production script
├── docker-build.sh         # Legacy build script
├── docker-run.sh           # Legacy run script
├── .dockerignore           # Files to exclude from Docker build
├── vite.config.js          # Vite config with Docker support
└── DOCKER.md              # This documentation
```

## 🌐 Access Points

### Development Environment
- **Application**: http://localhost:5173
- **Hot Reloading**: Enabled automatically

### Production Environment
- **Application**: http://localhost:3000 (or custom port)
- **Health Check**: http://localhost:3000/health

## 🔒 Security Features

- **Non-root user execution** in production
- **Read-only filesystem** with specific writable tmpfs mounts
- **Security headers** configured in Nginx
- **No new privileges** security option
- **Minimal attack surface** with Alpine Linux base images

## 📊 Performance Optimizations

### Development
- **File watching** with polling for Docker compatibility
- **HMR (Hot Module Replacement)** for instant updates
- **Volume mounts** for zero-rebuild development

### Production
- **Gzip compression** enabled for all text assets
- **Static asset caching** (1 year for versioned assets)
- **HTML caching** (1 hour with revalidation)
- **Optimized Nginx configuration** for React SPA
- **Resource limits** to prevent resource exhaustion

## 🐛 Troubleshooting

### Development Issues

1. **Hot reloading not working**
   ```bash
   # Ensure polling is enabled (already configured)
   # Restart development environment
   ./docker-dev.sh restart
   ```

2. **Port 5173 already in use**
   ```bash
   # Check what's using the port
   lsof -i :5173

   # Stop conflicting processes or use different port
   # Modify docker-compose.dev.yml ports section
   ```

3. **File changes not detected**
   ```bash
   # Check volume mounts
   ./docker-dev.sh shell
   ls -la /app/src  # Should show your source files
   ```

### Production Issues

1. **Port already in use**
   ```bash
   # Use different port
   ./docker-prod.sh start 8080

   # Or check what's using the port
   lsof -i :3000
   ```

2. **Build fails**
   ```bash
   # Clean build
   ./docker-prod.sh build

   # Check build logs
   docker-compose logs
   ```

3. **Health check failing**
   ```bash
   # Test health endpoint
   ./docker-prod.sh health

   # Check container logs
   ./docker-prod.sh logs
   ```

4. **Container won't start**
   ```bash
   # Check status and logs
   ./docker-prod.sh status
   ./docker-prod.sh logs

   # Access shell for debugging
   ./docker-prod.sh shell
   ```

## 🚀 Production Deployment Best Practices

### Environment Variables

Create a `.env` file for production:

```bash
# .env
HOST_PORT=80
NODE_ENV=production
```

### Resource Management

The production setup includes:
- **Memory limit**: 512MB (configurable)
- **CPU limit**: 0.5 cores (configurable)
- **Automatic restart**: unless-stopped
- **Log rotation**: 10MB max, 3 files

### Monitoring

```bash
# Check resource usage
./docker-prod.sh status

# Monitor logs in real-time
./docker-prod.sh logs

# Health check endpoint
curl http://localhost:3000/health
```

### Scaling

For multiple instances:

```bash
# Scale to 3 instances
docker-compose up -d --scale kb-tracker-frontend=3

# Use a load balancer (nginx, traefik, etc.)
```

## 🔄 Migration from Legacy Setup

If you were using the old Docker setup:

1. **Stop old containers**:
   ```bash
   docker stop kb-tracker-frontend
   docker rm kb-tracker-frontend
   ```

2. **Use new scripts**:
   ```bash
   # For development
   ./docker-dev.sh start

   # For production
   ./docker-prod.sh deploy
   ```

## 📝 Important Notes

### Development Environment
- **Port**: 5173 (Vite dev server)
- **Hot reloading**: Enabled with volume mounts
- **File watching**: Uses polling for Docker compatibility
- **Debugging**: Full access with `./docker-dev.sh shell`

### Production Environment
- **Port**: 8080 internally, mapped to 3000 (or custom)
- **Serving**: Nginx with optimized configuration
- **Caching**: Aggressive caching for static assets
- **Security**: Read-only filesystem, non-root user
- **Health checks**: Built-in monitoring at `/health`

### Performance
- **Development**: Optimized for fast development cycles
- **Production**: Optimized for performance and security
- **Image size**: ~50MB for production (multi-stage build)
- **Build time**: ~2-3 minutes for production build
