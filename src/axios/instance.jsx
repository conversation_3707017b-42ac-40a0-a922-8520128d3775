import axios from "axios";
import {
  getAccessToken,
  getRefreshToken,
  setAccessToken,
} from "../utils/cookieUtils";

// Create axios instance with base configuration
const instance = axios.create({
  baseURL: "http://localhost:3000/api/v1",
  timeout: 30000, // 30 seconds timeout (increased for role creation)
  headers: {
    "Content-Type": "application/json",
  },
});

// Flag to prevent multiple refresh attempts
let isRefreshing = false;
let failedQueue = [];

// Process failed queue after refresh
const processQueue = (error, token = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token);
    }
  });

  failedQueue = [];
};

// Request interceptor
instance.interceptors.request.use(
  (config) => {
    // Get access token from cookies
    const accessToken = getAccessToken();

    // If token exists, add it to headers
    if (accessToken) {
      config.headers.Authorization = `Bearer ${accessToken}`;
    }

    console.log(
      `Making ${config.method?.toUpperCase()} request to: ${config.url}`
    );
    return config;
  },
  (error) => {
    console.error("Request error:", error);
    return Promise.reject(error);
  }
);

// Response interceptor with automatic token refresh
instance.interceptors.response.use(
  (response) => {
    console.log(`Response from ${response.config.url}:`, response.status);
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // console.error("Response error:", error.response?.data || error.message);
    // console.error("Full error response:", {
    //   status: error.response?.status,
    //   statusText: error.response?.statusText,
    //   data: error.response?.data,
    //   headers: error.response?.headers,
    // });

    // Handle 401 errors (unauthorized)
    if (error.response?.status === 401 && !originalRequest._retry) {
      if (isRefreshing) {
        // If already refreshing, queue this request
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        })
          .then((token) => {
            originalRequest.headers.Authorization = `Bearer ${token}`;
            return instance(originalRequest);
          })
          .catch((err) => {
            return Promise.reject(err);
          });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      try {
        const refreshToken = getRefreshToken();

        if (!refreshToken) {
          // No refresh token available, trigger session expiry
          processQueue(error, null);
          triggerSessionExpiry();
          return Promise.reject(error);
        }

        // Attempt to refresh the token
        const refreshResponse = await axios.post(
          `${instance.defaults.baseURL}/auth/token/refresh`,
          { refreshToken },
          {
            headers: { "Content-Type": "application/json" },
            timeout: 10000,
          }
        );

        const { accessToken } = refreshResponse.data;

        // Store the new access token
        setAccessToken(accessToken);

        // Update the authorization header for the original request
        originalRequest.headers.Authorization = `Bearer ${accessToken}`;

        // Process the failed queue with the new token
        processQueue(null, accessToken);

        console.log("Token refreshed successfully");

        // Retry the original request
        return instance(originalRequest);
      } catch (refreshError) {
        console.error("Token refresh failed:", refreshError);

        // Don't clear tokens immediately - let user decide via modal
        processQueue(refreshError, null);
        triggerSessionExpiry();

        return Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
      }
    }

    return Promise.reject(error);
  }
);

// Function to trigger session expiry modal
const triggerSessionExpiry = () => {
  // Don't show session expired modal on login page (401 from wrong credentials)
  const currentPath = window.location.pathname;
  if (currentPath === "/login" || currentPath === "/") {
    return;
  }

  // Dispatch a custom event that the app can listen to
  window.dispatchEvent(new CustomEvent("sessionExpired"));
};

export default instance;
