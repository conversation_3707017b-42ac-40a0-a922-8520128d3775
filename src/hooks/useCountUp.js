import { useState, useEffect } from 'react';

/**
 * Custom hook for animating numbers from 0 to target value
 * @param {number|string} targetValue - The final value to count up to
 * @param {number} duration - Animation duration in milliseconds (default: 2000)
 * @param {number} delay - Delay before starting animation in milliseconds (default: 0)
 * @returns {number} - Current animated value
 */
function useCountUp(targetValue, duration = 2000, delay = 0) {
  const [currentValue, setCurrentValue] = useState(0);

  useEffect(() => {
    // Handle undefined or null values
    if (targetValue === undefined || targetValue === null) {
      setCurrentValue(0);
      return;
    }
    
    // If targetValue is a string, try to extract the numeric part
    let numericTargetValue = targetValue;
    if (typeof targetValue === 'string') {
      // Extract numeric part from strings like "25%" or "25.5%"
      const match = targetValue.match(/(\d+\.?\d*)/);
      numericTargetValue = match ? parseFloat(match[1]) : 0;
    }
    
    if (numericTargetValue === 0) {
      setCurrentValue(0);
      return;
    }

    const startTime = Date.now() + delay;
    const endTime = startTime + duration;

    const timer = setInterval(() => {
      const now = Date.now();
      
      if (now < startTime) {
        return; // Still in delay period
      }
      
      if (now >= endTime) {
        setCurrentValue(numericTargetValue);
        clearInterval(timer);
        return;
      }

      // Calculate progress (0 to 1)
      const progress = (now - startTime) / duration;
      
      // Use easeOutCubic for smooth deceleration
      const easeOutCubic = 1 - Math.pow(1 - progress, 3);
      
      // Calculate current value
      const value = Math.floor(easeOutCubic * numericTargetValue);
      setCurrentValue(value);
    }, 16); // ~60fps

    return () => clearInterval(timer);
  }, [targetValue, duration, delay]);

  return currentValue;
}

export default useCountUp;
