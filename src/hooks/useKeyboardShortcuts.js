import { useEffect } from 'react';

/**
 * Custom hook for handling keyboard shortcuts
 * @param {Object} shortcuts - Object mapping key combinations to callback functions
 * @param {Array} dependencies - Dependencies array for useEffect
 */
const useKeyboardShortcuts = (shortcuts, dependencies = []) => {
  useEffect(() => {
    const handleKeyDown = (event) => {
      // Create a key combination string
      const keyCombo = [];
      
      if (event.ctrlKey || event.metaKey) keyCombo.push('ctrl');
      if (event.altKey) keyCombo.push('alt');
      if (event.shiftKey) keyCombo.push('shift');
      // Safely handle event.key to prevent undefined errors
      if (event.key) {
        keyCombo.push(event.key.toLowerCase());
      } else {
        // Fallback for events without a key value
        return;
      }
      
      const keyString = keyCombo.join('+');
      
      // Check if this key combination has a handler
      if (shortcuts[keyString]) {
        // Prevent default browser behavior
        event.preventDefault();
        event.stopPropagation();
        
        // Call the handler
        shortcuts[keyString](event);
      }
    };

    // Add event listener
    document.addEventListener('keydown', handleKeyDown);

    // Cleanup
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, dependencies);
};

/**
 * Hook specifically for export functionality
 * @param {Function} onExport - Function to call when export shortcut is pressed
 * @param {boolean} enabled - Whether the shortcut is enabled
 */
export const useExportShortcut = (onExport, enabled = true) => {
  useKeyboardShortcuts(
    {
      'ctrl+e': enabled ? onExport : null,
    },
    [onExport, enabled]
  );
};

/**
 * Hook for common data table shortcuts
 * @param {Object} handlers - Object with handler functions
 */
export const useDataTableShortcuts = (handlers) => {
  const shortcuts = {};
  
  if (handlers.onExport) shortcuts['ctrl+e'] = handlers.onExport;
  if (handlers.onImport) shortcuts['ctrl+i'] = handlers.onImport;
  if (handlers.onRefresh) shortcuts['ctrl+r'] = handlers.onRefresh;
  if (handlers.onSearch) shortcuts['ctrl+f'] = handlers.onSearch;
  if (handlers.onNew) shortcuts['ctrl+n'] = handlers.onNew;
  
  useKeyboardShortcuts(shortcuts, [handlers]);
};

export default useKeyboardShortcuts;
