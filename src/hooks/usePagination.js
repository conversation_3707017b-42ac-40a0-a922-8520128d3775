import { useState, useCallback, useEffect } from 'react';

/**
 * Custom hook for managing pagination state and logic
 * Integrates with backend pagination API system
 * 
 * @param {Object} options - Configuration options
 * @param {Function} options.fetchData - Function to fetch data from API
 * @param {number} options.initialPage - Initial page number (default: 1)
 * @param {number} options.initialPageSize - Initial page size (default: 10)
 * @param {Array} options.dependencies - Dependencies that trigger data refetch
 * @param {boolean} options.autoFetch - Whether to fetch data automatically on mount (default: true)
 * @returns {Object} Pagination state and methods
 */
export const usePagination = ({
  fetchData,
  initialPage = 1,
  initialPageSize = 10,
  dependencies = [],
  autoFetch = true,
}) => {
  // Pagination state
  const [pagination, setPagination] = useState({
    page: initialPage,
    limit: initialPageSize,
    total: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false,
  });

  // Loading states
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Data state
  const [data, setData] = useState([]);

  /**
   * Fetch data with pagination parameters
   * @param {number} page - Page number to fetch
   * @param {number} limit - Items per page
   * @param {Object} additionalParams - Additional query parameters
   */
  const fetchPage = useCallback(async (page = pagination.page, limit = pagination.limit, additionalParams = {}) => {
    if (!fetchData) {
      console.warn('usePagination: fetchData function is required');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      console.log(`Fetching page ${page} with limit ${limit}`, additionalParams);
      
      const params = {
        page,
        limit,
        ...additionalParams,
      };

      const response = await fetchData(params);
      
      // Handle different response formats
      let responseData, responseMeta;
      
      if (response.data && response.meta) {
        // Standard paginated response format
        responseData = response.data;
        responseMeta = response.meta;
      } else if (Array.isArray(response)) {
        // Direct array response (fallback)
        responseData = response;
        responseMeta = {
          total: response.length,
          page: page,
          limit: limit,
          totalPages: Math.ceil(response.length / limit),
          hasNextPage: false,
          hasPreviousPage: false,
        };
      } else {
        // Unknown format
        console.warn('Unexpected response format:', response);
        responseData = [];
        responseMeta = {
          total: 0,
          page: page,
          limit: limit,
          totalPages: 0,
          hasNextPage: false,
          hasPreviousPage: false,
        };
      }

      setData(responseData);
      setPagination({
        page: responseMeta.page || page,
        limit: responseMeta.limit || limit,
        total: responseMeta.total || 0,
        totalPages: responseMeta.totalPages || 0,
        hasNextPage: responseMeta.hasNextPage || false,
        hasPreviousPage: responseMeta.hasPreviousPage || false,
      });

      console.log(`Successfully fetched ${responseData.length} items for page ${page}`);
      
    } catch (err) {
      console.error('Error fetching paginated data:', err);
      setError(err.message || 'Failed to fetch data');
      setData([]);
      setPagination(prev => ({
        ...prev,
        total: 0,
        totalPages: 0,
        hasNextPage: false,
        hasPreviousPage: false,
      }));
    } finally {
      setLoading(false);
    }
  }, [fetchData, pagination.page, pagination.limit]);

  /**
   * Go to specific page
   * @param {number} page - Page number to navigate to
   */
  const goToPage = useCallback((page) => {
    if (page >= 1 && page <= pagination.totalPages && page !== pagination.page) {
      fetchPage(page, pagination.limit);
    }
  }, [fetchPage, pagination.limit, pagination.totalPages, pagination.page]);

  /**
   * Change page size and reset to first page
   * @param {number} newPageSize - New items per page
   */
  const changePageSize = useCallback((newPageSize) => {
    if (newPageSize !== pagination.limit && newPageSize > 0) {
      fetchPage(1, newPageSize);
    }
  }, [fetchPage, pagination.limit]);

  /**
   * Go to next page
   */
  const nextPage = useCallback(() => {
    if (pagination.hasNextPage) {
      goToPage(pagination.page + 1);
    }
  }, [goToPage, pagination.hasNextPage, pagination.page]);

  /**
   * Go to previous page
   */
  const previousPage = useCallback(() => {
    if (pagination.hasPreviousPage) {
      goToPage(pagination.page - 1);
    }
  }, [goToPage, pagination.hasPreviousPage, pagination.page]);

  /**
   * Go to first page
   */
  const firstPage = useCallback(() => {
    if (pagination.page !== 1) {
      goToPage(1);
    }
  }, [goToPage, pagination.page]);

  /**
   * Go to last page
   */
  const lastPage = useCallback(() => {
    if (pagination.page !== pagination.totalPages && pagination.totalPages > 0) {
      goToPage(pagination.totalPages);
    }
  }, [goToPage, pagination.page, pagination.totalPages]);

  /**
   * Refresh current page
   */
  const refresh = useCallback((additionalParams = {}) => {
    fetchPage(pagination.page, pagination.limit, additionalParams);
  }, [fetchPage, pagination.page, pagination.limit]);

  /**
   * Reset pagination to initial state
   */
  const reset = useCallback(() => {
    setPagination({
      page: initialPage,
      limit: initialPageSize,
      total: 0,
      totalPages: 0,
      hasNextPage: false,
      hasPreviousPage: false,
    });
    setData([]);
    setError(null);
  }, [initialPage, initialPageSize]);

  // Auto-fetch data on mount and when dependencies change
  useEffect(() => {
    if (autoFetch) {
      fetchPage(initialPage, initialPageSize);
    }
  }, [autoFetch, initialPage, initialPageSize, ...dependencies]);

  return {
    // Data
    data,
    
    // Pagination state
    pagination,
    currentPage: pagination.page,
    pageSize: pagination.limit,
    totalItems: pagination.total,
    totalPages: pagination.totalPages,
    hasNextPage: pagination.hasNextPage,
    hasPreviousPage: pagination.hasPreviousPage,
    
    // Loading and error states
    loading,
    error,
    
    // Actions
    goToPage,
    changePageSize,
    nextPage,
    previousPage,
    firstPage,
    lastPage,
    refresh,
    reset,
    fetchPage,
    
    // Computed values
    isFirstPage: pagination.page === 1,
    isLastPage: pagination.page === pagination.totalPages,
    startItem: pagination.total > 0 ? (pagination.page - 1) * pagination.limit + 1 : 0,
    endItem: Math.min(pagination.page * pagination.limit, pagination.total),
  };
};

/**
 * Hook for simple load-more pagination (infinite scroll style)
 * @param {Object} options - Configuration options
 * @param {Function} options.fetchData - Function to fetch data
 * @param {number} options.initialPageSize - Initial page size
 * @param {Array} options.dependencies - Dependencies that trigger reset
 * @returns {Object} Load-more pagination state and methods
 */
export const useLoadMorePagination = ({
  fetchData,
  initialPageSize = 10,
  dependencies = [],
}) => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState(null);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);

  const loadInitialData = useCallback(async () => {
    setLoading(true);
    setError(null);
    setPage(1);
    setData([]);
    setHasMore(true);

    try {
      const response = await fetchData({ page: 1, limit: initialPageSize });
      const responseData = response.data || response;
      const responseMeta = response.meta;

      setData(responseData);
      setHasMore(responseMeta ? responseMeta.hasNextPage : responseData.length === initialPageSize);
    } catch (err) {
      setError(err.message || 'Failed to fetch data');
      setData([]);
      setHasMore(false);
    } finally {
      setLoading(false);
    }
  }, [fetchData, initialPageSize]);

  const loadMore = useCallback(async () => {
    if (!hasMore || loadingMore) return;

    setLoadingMore(true);
    setError(null);

    try {
      const nextPage = page + 1;
      const response = await fetchData({ page: nextPage, limit: initialPageSize });
      const responseData = response.data || response;
      const responseMeta = response.meta;

      setData(prev => [...prev, ...responseData]);
      setPage(nextPage);
      setHasMore(responseMeta ? responseMeta.hasNextPage : responseData.length === initialPageSize);
    } catch (err) {
      setError(err.message || 'Failed to load more data');
    } finally {
      setLoadingMore(false);
    }
  }, [fetchData, initialPageSize, hasMore, loadingMore, page]);

  // Reset when dependencies change
  useEffect(() => {
    loadInitialData();
  }, [loadInitialData, ...dependencies]);

  return {
    data,
    loading,
    loadingMore,
    error,
    hasMore,
    loadMore,
    refresh: loadInitialData,
    reset: loadInitialData,
  };
};

export default usePagination;
