import { useState, useEffect, useCallback } from 'react';
import { setCookie, getCookie, removeCookie } from '../utils/cookieUtils';

/**
 * Custom hook that provides useState-like functionality with cookie persistence
 * @param {string} key - Cookie name
 * @param {*} defaultValue - Default value if cookie doesn't exist
 * @param {object} options - Cookie options (expires, secure, etc.)
 * @returns {[value, setValue, removeValue]} - Array with current value, setter function, and remove function
 */
export const useCookieStorage = (key, defaultValue = null, options = {}) => {
  // Initialize state with value from cookie or default value
  const [storedValue, setStoredValue] = useState(() => {
    try {
      const item = getCookie(key);
      if (item) {
        // Try to parse JSON, if it fails return the raw string
        try {
          return JSON.parse(item);
        } catch {
          return item;
        }
      }
      return defaultValue;
    } catch (error) {
      console.error(`Error reading cookie ${key}:`, error);
      return defaultValue;
    }
  });

  // Function to update both state and cookie
  const setValue = useCallback((value) => {
    try {
      // Allow value to be a function so we have the same API as useState
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      
      // Update state
      setStoredValue(valueToStore);
      
      // Update cookie
      if (valueToStore === null || valueToStore === undefined) {
        removeCookie(key);
      } else {
        // Stringify objects and arrays, store primitives as-is
        const cookieValue = typeof valueToStore === 'object' 
          ? JSON.stringify(valueToStore) 
          : String(valueToStore);
        
        setCookie(key, cookieValue, {
          expires: 7, // Default 7 days
          ...options
        });
      }
    } catch (error) {
      console.error(`Error setting cookie ${key}:`, error);
    }
  }, [key, storedValue, options]);

  // Function to remove the cookie and reset to default value
  const removeValue = useCallback(() => {
    try {
      removeCookie(key);
      setStoredValue(defaultValue);
    } catch (error) {
      console.error(`Error removing cookie ${key}:`, error);
    }
  }, [key, defaultValue]);

  // Sync with cookie changes (in case cookie is modified externally)
  useEffect(() => {
    const handleStorageChange = () => {
      try {
        const item = getCookie(key);
        if (item) {
          try {
            const parsedItem = JSON.parse(item);
            setStoredValue(parsedItem);
          } catch {
            setStoredValue(item);
          }
        } else {
          setStoredValue(defaultValue);
        }
      } catch (error) {
        console.error(`Error syncing cookie ${key}:`, error);
      }
    };

    // Listen for storage events (though cookies don't trigger these, 
    // this is for potential future localStorage integration)
    window.addEventListener('storage', handleStorageChange);
    
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [key, defaultValue]);

  return [storedValue, setValue, removeValue];
};

export default useCookieStorage;
