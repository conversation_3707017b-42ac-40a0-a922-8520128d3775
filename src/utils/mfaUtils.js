import { getMFAMethods, getUserData } from './cookieUtils';

/**
 * Check if MFA methods are available in storage
 * @returns {boolean} True if MFA methods are available, false otherwise
 */
export const hasMFAMethodsStored = () => {
  const mfaMethods = getMFAMethods();
  const userData = getUserData();
  return !!(mfaMethods && mfaMethods.length > 0 && userData);
};

/**
 * Get stored MFA methods and user data
 * @returns {Object|null} Object with mfaMethods and user, or null if not available
 */
export const getStoredMFAData = () => {
  const mfaMethods = getMFAMethods();
  const userData = getUserData();
  
  if (mfaMethods && userData) {
    return {
      mfaMethods,
      user: userData
    };
  }
  
  return null;
};

/**
 * Navigate to MFA method selection with stored data
 * @param {Function} navigate - React Router navigate function
 * @returns {boolean} True if navigation was successful, false if no data available
 */
export const navigateToMFASelection = (navigate) => {
  const mfaData = getStoredMFAData();
  
  if (mfaData) {
    navigate('/mfa/select-method', {
      state: {
        mfaMethods: mfaData.mfaMethods,
        user: mfaData.user
      }
    });
    return true;
  }
  
  return false;
};

/**
 * Get method description for display
 * @param {Object} method - MFA method object
 * @returns {string} Human-readable description
 */
export const getMethodDescription = (method) => {
  switch (method.method) {
    case 'EMAIL':
      return `Send code to ${method.contact}`;
    case 'SMS':
      return `Send code to ${method.contact}`;
    default:
      return 'Authentication method';
  }
};

/**
 * Get method display name
 * @param {Object} method - MFA method object
 * @returns {string} Display name
 */
export const getMethodDisplayName = (method) => {
  switch (method.method) {
    case 'EMAIL':
      return 'Email';
    case 'SMS':
      return 'SMS';
    default:
      return 'Unknown';
  }
};
