/**
 * Permission utility functions for checking user permissions and role
 * These functions work with the PermissionContext to provide reusable permission logic
 */

// Import the context hook - this will be used by the functions
let permissionContext = null;

/**
 * Initialize the permission utils with the context
 * This should be called from a component that has access to the context
 * @param {Object} context - The permission context object
 */
export const initializePermissionUtils = (context) => {
  permissionContext = context;
};

/**
 * Check if user has the specified permissions
 * @param {Array} requiredPermissions - Array of permission IDs to check
 *                                     If last element is "OR", uses OR logic
 *                                     Otherwise uses AND logic
 * @returns {boolean} True if user has required permissions, false otherwise
 * 
 * @example
 * // AND logic - user must have both permissions
 * userHasPermissions(["edit.customer", "delete.customer"]) // returns true/false
 * 
 * @example
 * // OR logic - user must have at least one permission
 * userHasPermissions(["edit.customer", "delete.customer", "OR"]) // returns true/false
 */
export const userHasPermissions = (requiredPermissions) => {
  if (!permissionContext) {
    console.warn('Permission context not initialized. Call initializePermissionUtils first.');
    return false;
  }

  if (!requiredPermissions || !Array.isArray(requiredPermissions) || requiredPermissions.length === 0) {
    return true; // No permissions required
  }

  // Use the hasPermissions function from context
  return permissionContext.hasPermissions(requiredPermissions);
};

/**
 * Get the current user's role
 * @returns {Object|null} User role object with id and name, or null if not available
 * 
 * @example
 * const role = getUserRole();
 * console.log(role); // { id: "uuid", name: "Service Delivery Supervisor" }
 */
export const getUserRole = () => {
  if (!permissionContext) {
    console.warn('Permission context not initialized. Call initializePermissionUtils first.');
    return null;
  }

  return permissionContext.role;
};

/**
 * Get the current user's permissions array
 * @returns {Array} Array of permission objects with id field
 * 
 * @example
 * const permissions = getUserPermissions();
 * console.log(permissions); // [{ id: "edit.customer" }, { id: "delete.customer" }]
 */
export const getUserPermissions = () => {
  if (!permissionContext) {
    console.warn('Permission context not initialized. Call initializePermissionUtils first.');
    return [];
  }

  return permissionContext.permissions;
};

/**
 * Check if user has a specific single permission
 * @param {string} permissionId - Single permission ID to check
 * @returns {boolean} True if user has the permission, false otherwise
 * 
 * @example
 * hasPermission("edit.customer") // returns true/false
 */
export const hasPermission = (permissionId) => {
  if (!permissionId) {
    return true; // No permission required
  }

  return userHasPermissions([permissionId]);
};

/**
 * Check if user has admin role (convenience function)
 * @returns {boolean} True if user has admin role, false otherwise
 */
export const isAdmin = () => {
  const role = getUserRole();
  return role && role.name && role.name.toLowerCase().includes('admin');
};

/**
 * Check if user has supervisor role (convenience function)
 * @returns {boolean} True if user has supervisor role, false otherwise
 */
export const isSupervisor = () => {
  const role = getUserRole();
  return role && role.name && role.name.toLowerCase().includes('supervisor');
};

/**
 * Get user role name as string
 * @returns {string} Role name or empty string if not available
 */
export const getUserRoleName = () => {
  const role = getUserRole();
  return role && role.name ? role.name : '';
};

/**
 * Check if user can perform CRUD operations on a specific resource
 * @param {string} resource - Resource name (e.g., "customer", "user")
 * @param {string} operation - Operation type ("create", "read", "edit", "delete")
 * @returns {boolean} True if user can perform the operation, false otherwise
 * 
 * @example
 * canPerformOperation("customer", "edit") // checks for "edit.customer" permission
 */
export const canPerformOperation = (resource, operation) => {
  if (!resource || !operation) {
    return false;
  }

  const permissionId = `${operation}.${resource}`;
  return hasPermission(permissionId);
};

/**
 * Filter an array of items based on user permissions
 * Each item should have a 'requiredPermissions' property
 * @param {Array} items - Array of items to filter
 * @returns {Array} Filtered array containing only items user has permissions for
 * 
 * @example
 * const menuItems = [
 *   { name: "Edit", requiredPermissions: ["edit.customer"] },
 *   { name: "Delete", requiredPermissions: ["delete.customer"] }
 * ];
 * const allowedItems = filterByPermissions(menuItems);
 */
export const filterByPermissions = (items) => {
  if (!Array.isArray(items)) {
    return [];
  }

  return items.filter(item => {
    if (!item.requiredPermissions) {
      return true; // No permissions required
    }
    return userHasPermissions(item.requiredPermissions);
  });
};
