/**
 * Formats today's date in the format "17 Jul 2025"
 * @returns {string} Formatted date string
 */
export const getTodaysDate = () => {
  const today = new Date();

  const day = today.getDate();
  const month = today.toLocaleDateString("en-US", { month: "short" });
  const year = today.getFullYear();

  return `${day} ${month} ${year}`;
};

/**
 * Formats any date string in the format "25 Jul 2025"
 * @param {string} dateString - ISO date string or any valid date string
 * @returns {string} Formatted date string
 */
export const formatDateDisplay = (dateString) => {
  if (!dateString) return "N/A";

  const date = new Date(dateString);
  const options = {
    day: 'numeric',
    month: 'short',
    year: 'numeric'
  };

  return date.toLocaleDateString('en-GB', options);
};

/**
 * Formats a date range in the format "Jul 29 – Aug 4" or "Jul 29 – 4" if same month
 * @param {string} startDate - ISO date string or any valid date string
 * @param {string} endDate - ISO date string or any valid date string
 * @param {boolean} includeYear - Whether to include the year in the output
 * @returns {string} Formatted date range string
 */
export const formatDateRange = (startDate, endDate, includeYear = false) => {
  if (!startDate || !endDate) return '';

  const start = new Date(startDate);
  const end = new Date(endDate);

  const startMonth = start.toLocaleDateString('en-US', { month: 'short' });
  const startDay = start.getDate();
  const endMonth = end.toLocaleDateString('en-US', { month: 'short' });
  const endDay = end.getDate();
  const year = end.getFullYear();

  // If same month, show "Jul 29 – 4" or "Jul 29 – 4 2025"
  if (startMonth === endMonth) {
    const baseFormat = `${startMonth} ${startDay} – ${endDay}`;
    return includeYear ? `${baseFormat} ${year}` : baseFormat;
  }

  // If different months, show "Jul 29 – Aug 4" or "Jul 29 – Aug 4 2025"
  const baseFormat = `${startMonth} ${startDay} – ${endMonth} ${endDay}`;
  return includeYear ? `${baseFormat} ${year}` : baseFormat;
};

export const formatTime = (dateString) => {
  if (!dateString) return "";
  try {
    const date = new Date(dateString);
    return date.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
    });
  } catch {
    return "";
  }
};

/**
 * Formats a datetime string in the format "15 Aug 2025, 2.30 am"
 * @param {string} dateString - ISO date string or any valid date string
 * @returns {string} Formatted datetime string
 */
export const formatDateTimeDisplay = (dateString) => {
  if (!dateString) return "N/A";

  try {
    const date = new Date(dateString);

    // Format date part: "15 Aug 2025"
    const day = date.getDate();
    const month = date.toLocaleDateString("en-US", { month: "short" });
    const year = date.getFullYear();

    // Format time part: "2.30 am"
    const time = date.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    }).replace(":", ".");

    return `${day} ${month} ${year}, ${time}`;
  } catch (error) {
    console.error('Error formatting datetime:', error);
    return 'Invalid Date';
  }
};