import Cookies from 'js-cookie';

/**
 * Cookie utility functions for managing authentication tokens and other data
 */

// Cookie configuration
const COOKIE_CONFIG = {
  // Secure cookies in production
  secure: process.env.NODE_ENV === 'production',
  // SameSite attribute for CSRF protection
  sameSite: 'strict',
  // Default expiration (7 days)
  expires: 7,
};

/**
 * Set a cookie with the given name and value
 * @param {string} name - Cookie name
 * @param {string} value - Cookie value
 * @param {object} options - Additional cookie options
 */
export const setCookie = (name, value, options = {}) => {
  const config = {
    ...COOKIE_CONFIG,
    ...options,
  };

  Cookies.set(name, value, config);
};

/**
 * Get a cookie value by name
 * @param {string} name - Cookie name
 * @returns {string|undefined} Cookie value or undefined if not found
 */
export const getCookie = (name) => {
  return Cookies.get(name);
};

/**
 * Remove a cookie by name
 * @param {string} name - Cookie name
 */
export const removeCookie = (name) => {
  Cookies.remove(name);
};

/**
 * Set access token in cookie
 * @param {string} token - Access token
 */
export const setAccessToken = (token) => {
  setCookie('accessToken', token, {
    expires: 1, // 1 day for access token
    // Note: HttpOnly cannot be set from client-side JavaScript
    // HttpOnly cookies can only be set by the server
  });
};

/**
 * Set refresh token in cookie
 * @param {string} token - Refresh token
 */
export const setRefreshToken = (token) => {
  setCookie('refreshToken', token, {
    expires: 30, // 30 days for refresh token
    // Note: HttpOnly cannot be set from client-side JavaScript
    // HttpOnly cookies can only be set by the server
  });
};

/**
 * Get access token from cookie
 * @returns {string|undefined} Access token or undefined if not found
 */
export const getAccessToken = () => {
  return getCookie('accessToken');
};

/**
 * Get refresh token from cookie
 * @returns {string|undefined} Refresh token or undefined if not found
 */
export const getRefreshToken = () => {
  return getCookie('refreshToken');
};

/**
 * Clear all authentication tokens
 */
export const clearAuthTokens = () => {
  removeCookie('accessToken');
  removeCookie('refreshToken');
};

/**
 * Clear all authentication data (tokens, user data, MFA methods)
 */
export const clearAllAuthData = () => {
  clearAuthTokens();
  removeUserEmail();
  removeMFAMethods();
  removeUserData();
  clearTempAuthTokens();
};

/**
 * Set temporary access token in cookie (for MFA flow)
 * @param {string} token - Temporary access token
 */
export const setTempAccessToken = (token) => {
  setCookie('tempAccessToken', token, {
    expires: 1, // 1 day for temporary access token
  });
};

/**
 * Set temporary refresh token in cookie (for MFA flow)
 * @param {string} token - Temporary refresh token
 */
export const setTempRefreshToken = (token) => {
  setCookie('tempRefreshToken', token, {
    expires: 30, // 30 days for temporary refresh token
  });
};

/**
 * Get temporary access token from cookie
 * @returns {string|undefined} Temporary access token or undefined if not found
 */
export const getTempAccessToken = () => {
  return getCookie('tempAccessToken');
};

/**
 * Get temporary refresh token from cookie
 * @returns {string|undefined} Temporary refresh token or undefined if not found
 */
export const getTempRefreshToken = () => {
  return getCookie('tempRefreshToken');
};

/**
 * Clear temporary authentication tokens
 */
export const clearTempAuthTokens = () => {
  removeCookie('tempAccessToken');
  removeCookie('tempRefreshToken');
};

/**
 * Transfer temporary tokens to actual tokens (after MFA verification)
 */
export const transferTempTokensToActual = () => {
  const tempAccessToken = getTempAccessToken();
  const tempRefreshToken = getTempRefreshToken();

  if (tempAccessToken && tempRefreshToken) {
    setAccessToken(tempAccessToken);
    setRefreshToken(tempRefreshToken);
    clearTempAuthTokens();
    return true;
  }
  return false;
};

/**
 * Check if user is authenticated (has access token)
 * @returns {boolean} True if authenticated, false otherwise
 */
export const isAuthenticated = () => {
  return !!getAccessToken();
};

/**
 * Check if user has temporary tokens (in MFA flow)
 * @returns {boolean} True if has temporary tokens, false otherwise
 */
export const hasTempTokens = () => {
  return !!(getTempAccessToken() && getTempRefreshToken());
};

/**
 * Set user email in cookie
 * @param {string} email - User email
 */
export const setUserEmail = (email) => {
  setCookie('userEmail', email, {
    expires: 7, // 7 days
  });
};

/**
 * Get user email from cookie
 * @returns {string|undefined} User email or undefined if not found
 */
export const getUserEmail = () => {
  return getCookie('userEmail');
};

/**
 * Remove user email from cookie
 */
export const removeUserEmail = () => {
  removeCookie('userEmail');
};

/**
 * Set MFA methods in cookie
 * @param {Array} mfaMethods - Array of MFA methods
 */
export const setMFAMethods = (mfaMethods) => {
  setCookie('mfaMethods', JSON.stringify(mfaMethods), {
    expires: 7, // 7 days
  });
};

/**
 * Get MFA methods from cookie
 * @returns {Array|null} MFA methods array or null if not found
 */
export const getMFAMethods = () => {
  const methods = getCookie('mfaMethods');
  try {
    return methods ? JSON.parse(methods) : null;
  } catch (error) {
    console.error('Error parsing MFA methods from cookie:', error);
    return null;
  }
};

/**
 * Remove MFA methods from cookie
 */
export const removeMFAMethods = () => {
  removeCookie('mfaMethods');
};

/**
 * Set user data in cookie
 * @param {Object} user - User object
 */
export const setUserData = (user) => {
  setCookie('userData', JSON.stringify(user), {
    expires: 7, // 7 days
  });
};

/**
 * Get user data from cookie
 * @returns {Object|null} User object or null if not found
 */
export const getUserData = () => {
  const userData = getCookie('userData');
  try {
    return userData ? JSON.parse(userData) : null;
  } catch (error) {
    console.error('Error parsing user data from cookie:', error);
    return null;
  }
};

/**
 * Remove user data from cookie
 */
export const removeUserData = () => {
  removeCookie('userData');
};
