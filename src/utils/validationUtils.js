/**
 * Validation Utilities
 * Comprehensive validation functions for various data types and formats
 */

/**
 * Validate UUID v4 format
 * @param {string} uuid - UUID string to validate
 * @returns {boolean} True if valid UUID v4
 */
export const isValidUUID = (uuid) => {
  if (!uuid || typeof uuid !== 'string') return false;
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
};

/**
 * Validate string length
 * @param {string} str - String to validate
 * @param {number} maxLength - Maximum allowed length
 * @param {number} minLength - Minimum required length (default: 0)
 * @returns {boolean} True if string length is valid
 */
export const isValidStringLength = (str, maxLength, minLength = 0) => {
  if (str === null || str === undefined) return minLength === 0;
  if (typeof str !== 'string') return false;
  return str.length >= minLength && str.length <= maxLength;
};

/**
 * Validate non-negative integer
 * @param {number|string} value - Value to validate
 * @returns {boolean} True if valid non-negative integer
 */
export const isValidNonNegativeInteger = (value) => {
  if (value === null || value === undefined) return true; // Allow null/undefined for optional fields
  const num = Number(value);
  return Number.isInteger(num) && num >= 0;
};

/**
 * Validate decimal format with up to specified decimal places
 * @param {string|number} value - Value to validate
 * @param {number} maxDecimalPlaces - Maximum decimal places allowed (default: 2)
 * @returns {boolean} True if valid decimal
 */
export const isValidDecimal = (value, maxDecimalPlaces = 2) => {
  if (value === null || value === undefined || value === '') return true; // Allow empty for optional fields
  const decimalRegex = new RegExp(`^\\d+(\\.\\d{1,${maxDecimalPlaces}})?$`);
  return decimalRegex.test(value.toString());
};

/**
 * Validate ISO 8601 date format
 * @param {string} dateString - Date string to validate
 * @returns {boolean} True if valid ISO 8601 date
 */
export const isValidISODate = (dateString) => {
  if (!dateString || typeof dateString !== 'string') return false;
  try {
    const date = new Date(dateString);
    // Check if the date is valid and the string matches ISO format
    return !isNaN(date.getTime()) && dateString.includes('T') && dateString.includes('Z');
  } catch {
    return false;
  }
};

/**
 * Validate email format
 * @param {string} email - Email string to validate
 * @returns {boolean} True if valid email format
 */
export const isValidEmail = (email) => {
  if (!email || typeof email !== 'string') return false;
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validate phone number format (basic validation)
 * @param {string} phone - Phone number to validate
 * @returns {boolean} True if valid phone format
 */
export const isValidPhoneNumber = (phone) => {
  if (!phone || typeof phone !== 'string') return false;
  // Basic phone validation - allows digits, spaces, hyphens, parentheses, and plus sign
  const phoneRegex = /^[\+]?[\d\s\-\(\)]{7,15}$/;
  return phoneRegex.test(phone);
};

/**
 * Validate file size
 * @param {File} file - File object to validate
 * @param {number} maxSizeInMB - Maximum file size in MB
 * @returns {boolean} True if file size is valid
 */
export const isValidFileSize = (file, maxSizeInMB) => {
  if (!file || !file.size) return false;
  const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
  return file.size <= maxSizeInBytes;
};

/**
 * Validate file type
 * @param {File} file - File object to validate
 * @param {string[]} allowedTypes - Array of allowed MIME types
 * @returns {boolean} True if file type is allowed
 */
export const isValidFileType = (file, allowedTypes) => {
  if (!file || !file.type) return false;
  return allowedTypes.includes(file.type);
};

/**
 * Validate file extension
 * @param {File} file - File object to validate
 * @param {string[]} allowedExtensions - Array of allowed extensions (e.g., ['.pdf', '.jpg'])
 * @returns {boolean} True if file extension is allowed
 */
export const isValidFileExtension = (file, allowedExtensions) => {
  if (!file || !file.name) return false;
  const extension = '.' + file.name.split('.').pop().toLowerCase();
  return allowedExtensions.includes(extension);
};

/**
 * Comprehensive loan activity data validation
 * @param {Object} data - Loan activity data to validate
 * @returns {Object} Validation result with isValid boolean and errors array
 */
export const validateLoanActivityData = (data) => {
  const errors = [];

  // Required field validation
  if (!data.rmUserId) {
    errors.push('RM User ID is required');
  } else if (!isValidUUID(data.rmUserId)) {
    errors.push('RM User ID must be a valid UUID');
  }

  // Optional UUID field validations
  if (data.loanClientId && !isValidUUID(data.loanClientId)) {
    errors.push('Loan Client ID must be a valid UUID');
  }
  if (data.purposeId && !isValidUUID(data.purposeId)) {
    errors.push('Purpose ID must be a valid UUID');
  }

  // String length validations
  if (!isValidStringLength(data.loanAccountNumber, 50)) {
    errors.push('Loan account number must be 50 characters or less');
  }
  if (!isValidStringLength(data.comment, 1000)) {
    errors.push('Comment must be 1000 characters or less');
  }
  if (!isValidStringLength(data.apiCallReference, 255)) {
    errors.push('API call reference must be 255 characters or less');
  }
  if (!isValidStringLength(data.interactionType, 50)) {
    errors.push('Interaction type must be 50 characters or less');
  }
  if (!isValidStringLength(data.callStatus, 50)) {
    errors.push('Call status must be 50 characters or less');
  }
  if (!isValidStringLength(data.visitStatus, 50)) {
    errors.push('Visit status must be 50 characters or less');
  }
  if (!isValidStringLength(data.followupStatus, 50)) {
    errors.push('Followup status must be 50 characters or less');
  }

  // Number validations
  if (!isValidNonNegativeInteger(data.arrearsDays)) {
    errors.push('Arrears days must be a non-negative integer');
  }
  if (!isValidNonNegativeInteger(data.callDurationMinutes)) {
    errors.push('Call duration minutes must be a non-negative integer');
  }

  // Decimal validation for loan balance
  if (data.loanBalance && !isValidDecimal(data.loanBalance, 2)) {
    errors.push('Loan balance must be a valid decimal with up to 2 decimal places');
  }

  // Date validation
  if (data.nextFollowupDate && !isValidISODate(data.nextFollowupDate)) {
    errors.push('Next followup date must be a valid ISO 8601 datetime string');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Validate user form data
 * @param {Object} formData - User form data to validate
 * @param {boolean} isUpdate - Whether this is an update operation
 * @returns {Object} Validation result with isValid boolean and errors object
 */
export const validateUserData = (formData, isUpdate = false) => {
  const errors = {};

  // Name validation
  if (!formData.name?.trim()) {
    errors.name = 'Name is required';
  } else if (!isValidStringLength(formData.name.trim(), 100, 1)) {
    errors.name = 'Name must be between 1 and 100 characters';
  }

  // Email validation
  if (!formData.email?.trim()) {
    errors.email = 'Email is required';
  } else if (!isValidEmail(formData.email.trim())) {
    errors.email = 'Email format is invalid';
  }

  // Phone validation
  const phoneValue = formData.phone_number?.trim() || formData.phone?.trim();
  if (!phoneValue) {
    errors.phone_number = 'Phone number is required';
  } else if (!isValidPhoneNumber(phoneValue)) {
    errors.phone_number = 'Phone number format is invalid';
  }

  // Role validation
  if (!formData.role) {
    errors.role = 'Role is required';
  } else if (!isValidUUID(formData.role)) {
    errors.role = 'Role must be a valid selection';
  }

  // Branch validation
  if (!formData.branch) {
    errors.branch = 'Branch is required';
  } else if (!isValidUUID(formData.branch)) {
    errors.branch = 'Branch must be a valid selection';
  }

  // Password validation
  if (!isUpdate) {
    // For new users, password is required
    if (!formData.password?.trim()) {
      errors.password = 'Password is required';
    } else if (formData.password.length < 8) {
      errors.password = 'Password must be at least 8 characters';
    }

    // Confirm password validation
    if (!formData.confirmPassword?.trim()) {
      errors.confirmPassword = 'Please confirm password';
    } else if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = 'Passwords do not match';
    }
  } else {
    // For updates, validate password only if provided
    if (formData.password?.trim()) {
      if (formData.password.length < 8) {
        errors.password = 'Password must be at least 8 characters';
      }

      // Confirm password validation
      if (formData.password !== formData.confirmPassword) {
        errors.confirmPassword = 'Passwords do not match';
      }
    }
  }

  // RM Code validation (optional)
  if (formData.rm_code && !isValidStringLength(formData.rm_code.trim(), 20)) {
    errors.rm_code = 'RM Code must be 20 characters or less';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

/**
 * Validate file upload
 * @param {File[]} files - Array of files to validate
 * @param {Object} options - Validation options
 * @param {number} options.maxFileSize - Maximum file size in MB (default: 10)
 * @param {number} options.maxFiles - Maximum number of files (default: 5)
 * @param {string[]} options.allowedTypes - Allowed MIME types
 * @param {string[]} options.allowedExtensions - Allowed file extensions
 * @returns {Object} Validation result with isValid boolean and errors array
 */
export const validateFileUpload = (files, options = {}) => {
  const {
    maxFileSize = 10,
    maxFiles = 5,
    allowedTypes = [],
    allowedExtensions = []
  } = options;

  const errors = [];

  if (!files || !Array.isArray(files)) {
    return { isValid: true, errors: [] }; // No files is valid for optional uploads
  }

  // Check number of files
  if (files.length > maxFiles) {
    errors.push(`Maximum ${maxFiles} files allowed`);
  }

  // Validate each file
  files.forEach((file, index) => {
    if (!file || !(file instanceof File)) {
      errors.push(`File ${index + 1} is not a valid file`);
      return;
    }

    // Check file size
    if (!isValidFileSize(file, maxFileSize)) {
      errors.push(`File "${file.name}" exceeds maximum size of ${maxFileSize}MB`);
    }

    // Check file type if specified
    if (allowedTypes.length > 0 && !isValidFileType(file, allowedTypes)) {
      errors.push(`File "${file.name}" has unsupported type. Allowed types: ${allowedTypes.join(', ')}`);
    }

    // Check file extension if specified
    if (allowedExtensions.length > 0 && !isValidFileExtension(file, allowedExtensions)) {
      errors.push(`File "${file.name}" has unsupported extension. Allowed extensions: ${allowedExtensions.join(', ')}`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Sanitize string input
 * @param {string} str - String to sanitize
 * @param {number} maxLength - Maximum length to truncate to
 * @returns {string} Sanitized string
 */
export const sanitizeString = (str, maxLength = null) => {
  if (!str || typeof str !== 'string') return '';
  
  // Trim whitespace
  let sanitized = str.trim();
  
  // Truncate if maxLength specified
  if (maxLength && sanitized.length > maxLength) {
    sanitized = sanitized.substring(0, maxLength);
  }
  
  return sanitized;
};

/**
 * Format validation errors for display
 * @param {Array|Object} errors - Validation errors
 * @returns {string} Formatted error message
 */
export const formatValidationErrors = (errors) => {
  if (Array.isArray(errors)) {
    return errors.join('; ');
  } else if (typeof errors === 'object' && errors !== null) {
    return Object.values(errors).join('; ');
  } else if (typeof errors === 'string') {
    return errors;
  }
  return 'Validation failed';
};

export default {
  isValidUUID,
  isValidStringLength,
  isValidNonNegativeInteger,
  isValidDecimal,
  isValidISODate,
  isValidEmail,
  isValidPhoneNumber,
  isValidFileSize,
  isValidFileType,
  isValidFileExtension,
  validateLoanActivityData,
  validateUserData,
  validateFileUpload,
  sanitizeString,
  formatValidationErrors
};
