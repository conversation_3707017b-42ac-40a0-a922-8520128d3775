// Activity type mappings for display names
export const ACTIVITY_TYPES = {
  LEADS_HITLIST: "Lead Calls",
  CUSTOMER_RELATIONSHIP: "Customer Relationship",
  TWO_BY_TWO_BY_TWO_HITLIST: "2×2×2 Calls",
  DORMANCY_HITLIST: "Dormancy Calls", 
  LOAN_ACTIVITIES: "Loan Activities"
};

// Color mappings for different activity types
export const ACTIVITY_COLORS = {
  LEADS_HITLIST: "green",
  CUSTOMER_RELATIONSHIP: "blue", 
  TWO_BY_TWO_BY_TWO_HITLIST: "orange",
  DORMANCY_HITLIST: "green",
  LOAN_ACTIVITIES: "purple"
};

// Map activity type to display name
export const getActivityDisplayName = (activityType) => {
  return ACTIVITY_TYPES[activityType] || activityType;
};

// Map activity type to color
export const getActivityColor = (activityType) => {
  return ACTIVITY_COLORS[activityType] || "gray";
};

// Calculate progress percentage (capped at 100%)
export const calculateProgress = (doneCount, targetValue) => {
  if (targetValue === 0) return 0;
  return Math.min(Math.round((doneCount / targetValue) * 100), 100);
};

// Calculate remaining count
export const calculateRemaining = (doneCount, targetValue) => {
  return Math.max(targetValue - doneCount, 0);
};

// Format interaction type for display
export const formatInteractionType = (interactionType, count = 1) => {
  const type = interactionType?.toLowerCase() || 'activity';
  const plural = count === 1 ? type : `${type}s`;
  return plural;
};

// Get status text based on progress
export const getStatusText = (progress) => {
  return `${progress}% complete`;
};

// Check if activity is completed (80% or more)
export const isActivityOnTrack = (progress) => {
  return progress >= 80;
};

// Check if activity is completed (100%)
export const isActivityCompleted = (progress) => {
  return progress >= 100;
};
