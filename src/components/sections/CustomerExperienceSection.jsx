import React from "react";
import Chart from "react-apexcharts";

function CustomerExperienceSection({ dashboardData }) {
  // Use passed dashboardData instead of fetching
  const loading = !dashboardData;

  // Extract personal data from API response
  const personalData = dashboardData?.personal || {
    calls_today: 0,
    overdue_calls: 0,
    upcoming_calls: [],
    my_calls_this_month: 0,
    monthly_calls_vs_targets: {
      last_month: { target: 0, calls: 0 },
      this_month: { target: 0, calls: 0 },
    },
  };

  const callsToMakeToday = personalData.calls_today;
  const overdueCalls = personalData.overdue_calls;
  const callsMadeThisMonth = personalData.my_calls_this_month;
  const upcomingCalls = personalData.upcoming_calls || [];

  // Function to calculate days remaining
  const calculateDaysRemaining = (dateString) => {
    const callDate = new Date(dateString);
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Reset time to start of day
    callDate.setHours(0, 0, 0, 0); // Reset time to start of day

    const diffTime = callDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return "Today";
    if (diffDays === 1) return "Tomorrow";
    if (diffDays > 1) return `${diffDays} days`;
    if (diffDays === -1) return "Yesterday";
    return `${Math.abs(diffDays)} days ago`;
  };

  // User targets data
  const userTargets = [
    {
      name: "Monthly Calls",
      completed: 45,
      target: 60,
      percentage: 75,
      endDate: "Jan 31, 2025",
    },
    {
      name: "Weekly Calls",
      completed: 12,
      target: 15,
      percentage: 80,
      endDate: "Jan 26, 2025",
    },
    {
      name: "Today's Calls",
      completed: 3,
      target: 8,
      percentage: 37.5,
      endDate: "Today 7pm",
    },
    {
      name: "3-day Calls",
      completed: 18,
      target: 25,
      percentage: 72,
      endDate: "Jan 26, 2025",
    },
    {
      name: "Customer Follow-ups",
      completed: 32,
      target: 40,
      percentage: 80,
      endDate: "Jan 31, 2025",
    },
  ];

  // Chart data for last 2 months using API data
  const months = ["Last Month", "This Month"];
  const completedCalls = [
    personalData.monthly_calls_vs_targets.last_month.calls,
    personalData.monthly_calls_vs_targets.this_month.calls,
  ];
  const targets = [
    personalData.monthly_calls_vs_targets.last_month.target,
    personalData.monthly_calls_vs_targets.this_month.target,
  ];

  const chartOptions = {
    chart: {
      type: "bar",
      height: 400,
      toolbar: {
        show: false,
      },
      animations: {
        enabled: true,
        easing: "easeinout",
        speed: 800,
      },
      background: "#ffffff",
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: "60%",
        grouped: true,
        endingShape: "flat",
        borderRadius: 0,
        dataLabels: {
          position: "top",
        },
      },
    },
    dataLabels: {
      enabled: true,
      formatter: function (val) {
        return val.toString();
      },
      offsetY: -20,
      style: {
        fontSize: "12px",
        fontWeight: 600,
        colors: ["#2D3748"],
      },
    },
    stroke: {
      show: false,
      width: 0,
    },
    xaxis: {
      categories: months,
      labels: {
        style: {
          fontSize: "14px",
          fontWeight: 600,
          colors: ["#2D3748"],
        },
      },
      axisBorder: {
        show: true,
        color: "#e5e7eb",
      },
      axisTicks: {
        show: true,
        color: "#e5e7eb",
      },
      title: {
        text: "Month",
        style: {
          fontSize: "14px",
          fontWeight: 600,
          color: "#2D3748",
        },
      },
    },
    yaxis: {
      labels: {
        style: {
          fontSize: "14px",
          fontWeight: 600,
          colors: ["#2D3748"],
        },
      },
      title: {
        text: "Number of Calls",
        style: {
          fontSize: "14px",
          fontWeight: 600,
          color: "#2D3748",
        },
      },
    },
    fill: {
      opacity: 1,
      type: "solid",
    },
    colors: ["#1C5B41", "#82C355"], // Dark green for completed, Light green for target
    legend: {
      show: true,
      position: "bottom",
      horizontalAlign: "center",
      fontSize: "14px",
      fontWeight: 600,
      fontFamily: "Inter, system-ui, sans-serif",
      markers: {
        width: 14,
        height: 14,
        radius: 2,
        offsetX: -2,
      },
      itemMargin: {
        horizontal: 20,
        vertical: 5,
      },
      labels: {
        colors: ["#374151"],
      },
    },
    tooltip: {
      enabled: true,
      shared: false,
      intersect: true,
      y: {
        formatter: function (val) {
          return `${val} calls`;
        },
      },
      style: {
        fontSize: "12px",
      },
    },
    grid: {
      borderColor: "#e5e7eb",
      strokeDashArray: 0,
      xaxis: {
        lines: {
          show: false,
        },
      },
      yaxis: {
        lines: {
          show: true,
        },
      },
      padding: {
        top: 0,
        right: 20,
        bottom: 0,
        left: 10,
      },
    },
  };

  const series = [
    {
      name: "Completed Calls",
      data: completedCalls,
    },
    {
      name: "Target",
      data: targets,
    },
  ];

  // Loading state
  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="mb-6">
          <h2 className="text-[20px] font-semibold text-gray-800 mb-2">
            My Calls
          </h2>
          <p className="text-sm text-gray-600">
            Personal dashboard for Second 2 call activities
          </p>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-600">Loading personal data...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <div className="mb-6">
        <h2 className="text-[20px] font-semibold text-gray-800 mb-2">
          My Calls
        </h2>
        <p className="text-sm text-gray-600">
          Personal dashboard for Second 2 call activities
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Left Side */}
        <div className="space-y-6">
          {/* KPI Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Calls to Make Today */}
            <div className="bg-[#f0f9f4] border border-[#82C355] rounded-lg p-4 shadow-sm">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-[#4a7c59]">Calls to Make Today</p>
                  <p className="text-2xl font-bold text-[#82C355]">
                    {callsToMakeToday}
                  </p>
                </div>
                <div className="w-12 h-12 rounded-full bg-[#82C355] flex items-center justify-center">
                  <svg
                    className="w-6 h-6 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                    />
                  </svg>
                </div>
              </div>
            </div>

            {/* Overdue Calls */}
            <div className="bg-[#ffeee9] border border-[#ff6d4c] rounded-lg p-4 shadow-sm">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-[#cc5a3d]">My Overdue Calls</p>
                  <p className="text-2xl font-bold text-[#ff6d4c]">
                    {overdueCalls}
                  </p>
                </div>
                <div className="w-12 h-12 rounded-full bg-[#ff6d4c] flex items-center justify-center">
                  <svg
                    className="w-6 h-6 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                    />
                  </svg>
                </div>
              </div>
            </div>
          </div>

          {/* Upcoming Calls */}
          <div className="bg-[#E6F0EC] rounded-lg p-4">
            <div className="flex justify-between items-center mb-3">
              <h3 className="text-lg font-semibold text-gray-800">
                Upcoming Calls
              </h3>
              {upcomingCalls.length > 4 && (
                <a
                  href="/customer-service/calls-to-do?type=upcoming"
                  className="text-sm text-[#1C5B41] hover:text-[#2D5A3D] font-medium"
                >
                  See All
                </a>
              )}
            </div>
            <div className="space-y-2">
              {upcomingCalls.length === 0 ? (
                <div className="text-center py-4 text-gray-500">
                  No upcoming calls scheduled
                </div>
              ) : (
                upcomingCalls.slice(0, 4).map((call, index) => (
                  <div
                    key={index}
                    className="flex justify-between items-center py-2 px-3 bg-white rounded border"
                  >
                    <div className="flex flex-col">
                      <span className="text-sm font-medium text-gray-800">
                        {call.name}
                      </span>
                      <span className="text-xs text-gray-500">
                        {new Date(call.date).toLocaleDateString()}
                      </span>
                    </div>
                    <span className="text-sm font-semibold text-[#1C5B41]">
                      {calculateDaysRemaining(call.date)}
                    </span>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Calls Made This Month */}
          <div className="bg-white border border-gray-300 rounded-lg p-4 ">
            <div className="mb-3">
              <p className="text-sm text-gray-600">My Calls This Month</p>
              <p className="text-2xl font-bold text-[#1C5B41]">
                {callsMadeThisMonth}
              </p>
            </div>
          </div>

          {/* My Targets Overview */}
          {/* <div className="bg-white border border-gray-300 rounded-lg p-4 ">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-800">
                My Targets Overview
              </h3>
              <button className="text-sm text-[#1C5B41] hover:text-[#2D5A3D] font-medium">
                See All
              </button>
            </div>
            <div className="space-y-3">
              {userTargets.slice(0, 3).map((target, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <div>
                      <span className="text-sm font-medium text-gray-800">
                        {target.name}
                      </span>
                      <p className="text-xs text-gray-500">
                        Due: {target.endDate}
                      </p>
                    </div>
                    <span className="text-sm text-gray-600">
                      {target.completed}/{target.target}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-[#1C5B41] h-2 rounded-full transition-all duration-300"
                      style={{ width: `${target.percentage}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-gray-600">
                    {target.percentage}% completed
                  </p>
                </div>
              ))}
            </div>
          </div> */}
        </div>

        {/* Right Side - Chart */}
        <div className="bg-white rounded-lg p-4">
          <div className="mb-4">
            <h3 className="text-[20px] font-semibold text-gray-800 text-center mb-2">
              My Monthly Calls vs Target
            </h3>
            <p className="text-sm text-gray-600 text-center">
              Last 2 Months Performance
            </p>
          </div>

          <Chart
            options={chartOptions}
            series={series}
            type="bar"
            height={400}
          />
        </div>
      </div>
    </div>
  );
}

export default CustomerExperienceSection;
