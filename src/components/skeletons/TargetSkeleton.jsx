import React from "react";

const TargetSkeleton = ({ count = 6 }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {Array.from({ length: count }).map((_, index) => (
        <div
          key={index}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 animate-pulse"
        >
          {/* Header - Category name, due date, and status */}
          <div className="flex justify-between items-start mb-4">
            <div>
              {/* Category name */}
              <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-32 mb-2"></div>
              {/* Due date */}
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
            </div>
            {/* Status */}
            <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
          </div>

          {/* Stats grid - Target, Done, Remaining */}
          <div className="grid grid-cols-3 gap-4 mb-4">
            <div>
              {/* Target label */}
              <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-10 mb-1"></div>
              {/* Target value */}
              <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-12"></div>
            </div>
            <div>
              {/* Done label */}
              <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-8 mb-1"></div>
              {/* Done value */}
              <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-10"></div>
            </div>
            <div>
              {/* Remaining label */}
              <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-14 mb-1"></div>
              {/* Remaining value */}
              <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-12"></div>
            </div>
          </div>

          {/* Progress bar */}
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className="h-2 bg-gray-300 dark:bg-gray-600 rounded-full"
              style={{ width: `${Math.random() * 80 + 10}%` }}
            ></div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default TargetSkeleton;
