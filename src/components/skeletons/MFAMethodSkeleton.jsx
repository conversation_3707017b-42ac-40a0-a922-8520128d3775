import React from 'react';

const MFAMethodSkeleton = ({ count = 2 }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-8">
      {Array.from({ length: count }).map((_, index) => (
        <div
          key={index}
          className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-8 border border-gray-200 dark:border-gray-600 animate-pulse"
        >
          {/* Method Header */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-gray-200 dark:bg-gray-600">
                <div className="h-5 w-5 bg-gray-300 dark:bg-gray-500 rounded"></div>
              </div>
              <div>
                <div className="h-5 bg-gray-200 dark:bg-gray-600 rounded mb-2 w-32"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-48"></div>
              </div>
            </div>
            <div className="px-2 py-1 bg-gray-200 dark:bg-gray-600 rounded-full">
              <div className="h-3 w-12 bg-gray-300 dark:bg-gray-500 rounded"></div>
            </div>
          </div>

          {/* Contact Info */}
          <div className="mb-4">
            <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-3/4"></div>
          </div>

          {/* Action Button */}
          <div className="w-full py-3 px-4 bg-gray-200 dark:bg-gray-600 rounded-lg mt-4">
            <div className="h-5 bg-gray-300 dark:bg-gray-500 rounded w-16 mx-auto"></div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default MFAMethodSkeleton;
