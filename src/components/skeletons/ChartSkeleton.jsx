import React from 'react';

const ChartSkeleton = ({ 
  title = "Chart Title", 
  subtitle = "Chart description",
  height = "h-64",
  showHeader = true,
  showLegend = false,
  chartType = "default" // "pie", "bar", "line", "default"
}) => {
  const renderChartContent = () => {
    switch (chartType) {
      case "pie":
        return (
          <div className="flex items-center justify-center h-full">
            <div className="relative">
              <div className="w-32 h-32 rounded-full border-8 border-gray-200 animate-pulse"></div>
              <div className="absolute inset-0 w-32 h-32 rounded-full border-8 border-transparent border-t-gray-300 animate-spin"></div>
            </div>
          </div>
        );
      
      case "bar":
        return (
          <div className="flex items-end justify-center space-x-2 h-full px-4">
            {[1, 2, 3, 4, 5, 6].map((item) => (
              <div
                key={item}
                className="bg-gray-200 animate-pulse rounded-t"
                style={{
                  height: `${Math.random() * 60 + 20}%`,
                  width: '12%'
                }}
              ></div>
            ))}
          </div>
        );
      
      case "line":
        return (
          <div className="relative h-full p-4">
            <svg className="w-full h-full" viewBox="0 0 400 200">
              <defs>
                <linearGradient id="skeleton-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" stopColor="#f3f4f6" stopOpacity="1" />
                  <stop offset="50%" stopColor="#e5e7eb" stopOpacity="1" />
                  <stop offset="100%" stopColor="#f3f4f6" stopOpacity="1" />
                </linearGradient>
              </defs>
              <path
                d="M 20 150 Q 100 100 180 120 T 380 80"
                stroke="url(#skeleton-gradient)"
                strokeWidth="3"
                fill="none"
                className="animate-pulse"
              />
              <path
                d="M 20 120 Q 100 80 180 100 T 380 60"
                stroke="url(#skeleton-gradient)"
                strokeWidth="3"
                fill="none"
                className="animate-pulse"
                style={{ animationDelay: '0.5s' }}
              />
            </svg>
          </div>
        );
      
      default:
        return (
          <div className="flex items-center justify-center h-full">
            <div className="space-y-3 w-full max-w-sm">
              <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
              <div className="h-4 bg-gray-200 rounded animate-pulse w-5/6"></div>
              <div className="h-4 bg-gray-200 rounded animate-pulse w-4/6"></div>
              <div className="h-4 bg-gray-200 rounded animate-pulse w-3/6"></div>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
      {showHeader && (
        <div className="mb-6">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2 w-3/4"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-1/2"></div>
        </div>
      )}
      
      <div className={`${height} relative`}>
        {renderChartContent()}
      </div>
      
      {showLegend && (
        <div className="mt-4 flex justify-center space-x-4">
          {[1, 2, 3].map((item) => (
            <div key={item} className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-gray-200 rounded animate-pulse"></div>
              <div className="h-3 bg-gray-200 rounded animate-pulse w-16"></div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ChartSkeleton;
