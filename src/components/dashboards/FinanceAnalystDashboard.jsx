import React, { useRef, useEffect, useState } from "react";
import Chart from 'react-apexcharts';
import DashboardCard from "../cards/DashboardCard";
import useCountUp from "../../hooks/useCountUp";
import {
    UserGroupIcon,
    ChartBarIcon,
    ClockIcon,
    BuildingOfficeIcon,
    PhoneIcon,
    CalendarIcon,
    ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { IoIosTrendingUp } from "react-icons/io";
import instance from "../../axios/instance";
import { financeAnalystService } from "../../services/financeAnalystService";


function FinanceAnalystDashboard() {
    // Animation state
    const [hasAnimated, setHasAnimated] = useState(false);
    const [shouldAnimate, setShouldAnimate] = useState(false);
    const dashboardRef = useRef(null);

    // Loading state
    const [isLoading, setIsLoading] = useState(true);
    const [topAnchorsLoading, setTopAnchorsLoading] = useState(true);
    const [statisticsLoading, setStatisticsLoading] = useState(true);
    const [chartsLoading, setChartsLoading] = useState(true);
    const [conversionChartLoading, setConversionChartLoading] = useState(true);
    const [regionalChartLoading, setRegionalChartLoading] = useState(true);
    const [branchChartLoading, setBranchChartLoading] = useState(true);
    const [callVisitStatsLoading, setCallVisitStatsLoading] = useState(true);

    // Year filter for conversion chart
    const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());

    // Generate year options starting from 2024 to current year + 1
    const generateYearOptions = () => {
        const currentYear = new Date().getFullYear();
        const startYear = 2024;
        const endYear = currentYear + 1;
        const years = [];

        for (let year = startYear; year <= endYear; year++) {
            years.push(year);
        }

        return years;
    };

    const [yearOptions] = useState(generateYearOptions());

    // Conversion chart data state
    const [conversionChartData, setConversionChartData] = useState({
        categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        conversionRates: [18.5, 21.2, 19.8, 23.5, 22.1, 24.3],
        year: new Date().getFullYear()
    });

    // Regional chart data state
    const [regionalChartData, setRegionalChartData] = useState({
        categories: ["Central", "Eastern", "Western", "Northern", "Southern", "Coast", "Rift Valley", "Nyanza"],
        totalLeadsData: [456, 298, 234, 189, 267, 198, 289, 225],
        convertedLeadsData: [89, 67, 54, 43, 62, 38, 71, 48]
    });

    // Branch chart data state
    const [branchChartData, setBranchChartData] = useState({
        regionName: "Nairobi Region",
        categories: ["Nairobi Main", "Westlands", "Karen", "Thika"],
        totalLeadsData: [114, 132, 89, 121],
        convertedLeadsData: [22, 26, 17, 24]
    });

    // Regions for filter options
    const [regions, setRegions] = useState([]);
    const [selectedRegion, setSelectedRegion] = useState("Nairobi Region");

    // Call visit statistics data state
    const [callVisitStats, setCallVisitStats] = useState({
        callsVsTarget: {
            made: 1847,
            target: 2100,
            achievementRate: 87.9
        },
        visitsVsTarget: {
            made: 567,
            target: 650,
            achievementRate: 87.2
        }
    });

    // Mock data - replace with API calls
    const [dashboardData, setDashboardData] = useState({
        // Lead Generation Summary
        totalLeads: {
            mtd: 1247,
            ytd: 8934,
            conversionRate: 23.5 + `%`,
            convertedLeadsMtd: 456,
            
        },
        leadsByCategory: {
            employed: 456,
            corporate: 234,
            trader: 557
        },
        topAnchors: [
            { name: "ABC Manufacturing", leads: 89 },
            { name: "Tech Solutions Ltd", leads: 67 },
            { name: "Global Enterprises", leads: 54 }
        ],

        // Leads by Status
        leadsByStatus: {
            warm: 2847,
            hot: 453,
            pending: 3300,
            cold: 3300,

        },

        

        activityBreakdown: {
            relationshipMgmt: 1234,
            customerService: 987,
            loanCollection: 626
        },
        successRates: {
            successful: 78.5,
            failed: 12.3,
            rescheduled: 9.2
        },

        // Hitlist and Targets
        hitlistSize: 3456,
        callsVsTarget: {
            made: 1847,
            target: 2100
        },
        visitsVsTarget: {
            made: 567,
            target: 650
        },
        overdueActivities: 89,
        upcomingActivities: 234,

        // Customer Engagement
        customerFeedback: {
            positive: 67,
            neutral: 23,
            negative: 10
        },
        dormancyRecovery: {
            contacted: 234,
            reactivated: 89
        },

        // Department Hitlists by Region
        departmentHitlists: {
            customerService: {
                total: 1234,
                regions: {
                    "Central": 234,
                    "Eastern": 189,
                    "Western": 156,
                    "Northern": 145,
                    "Southern": 178,
                    "Coast": 123,
                    "Rift Valley": 134,
                    "Nyanza": 75
                }
            },
            leadGeneration: {
                total: 2156,
                regions: {
                    "Central": 456,
                    "Eastern": 298,
                    "Western": 234,
                    "Northern": 189,
                    "Southern": 267,
                    "Coast": 198,
                    "Rift Valley": 289,
                    "Nyanza": 225
                }
            },
            loanActivities: {
                total: 987,
                regions: {
                    "Central": 178,
                    "Eastern": 134,
                    "Western": 123,
                    "Northern": 98,
                    "Southern": 145,
                    "Coast": 89,
                    "Rift Valley": 156,
                    "Nyanza": 64
                }
            }
        },

        // Converted Leads by Region and Department
            convertedLeads: {
                customerService: {
                    regions: {
                        "Central": 45,
                        "Eastern": 32,
                        "Western": 28,
                        "Northern": 21,
                        "Southern": 35,
                        "Coast": 19,
                        "Rift Valley": 26,
                        "Nyanza": 14
                    }
                },
                leadGeneration: {
                    regions: {
                        "Central": 89,
                        "Eastern": 67,
                        "Western": 54,
                        "Northern": 43,
                        "Southern": 62,
                        "Coast": 38,
                        "Rift Valley": 71,
                        "Nyanza": 48
                    }
                },
                loanActivities: {
                    regions: {
                        "Central": 34,
                        "Eastern": 28,
                        "Western": 22,
                        "Northern": 18,
                        "Southern": 31,
                        "Coast": 16,
                        "Rift Valley": 29,
                        "Nyanza": 12
                    }
                }
            },

            // Branches by Region
            branchesByRegion: {
                "Central": {
                    branches: ["Nairobi Main", "Westlands", "Karen", "Thika"],
                    hitlist: {
                        customerService: [58, 67, 45, 64],
                        leadGeneration: [114, 132, 89, 121],
                        loanActivities: [44, 51, 34, 49]
                    },
                    convertedLeads: {
                        customerService: [12, 14, 9, 10],
                        leadGeneration: [22, 26, 17, 24],
                        loanActivities: [8, 10, 6, 10]
                    }
                },
                "Eastern": {
                    branches: ["Meru", "Embu", "Machakos", "Kitui"],
                    hitlist: {
                        customerService: [47, 52, 48, 42],
                        leadGeneration: [74, 82, 76, 66],
                        loanActivities: [33, 37, 34, 30]
                    },
                    convertedLeads: {
                        customerService: [8, 9, 8, 7],
                        leadGeneration: [16, 18, 17, 16],
                        loanActivities: [7, 8, 7, 6]
                    }
                },
                "Western": {
                    branches: ["Kisumu", "Eldoret", "Kakamega", "Bungoma"],
                    hitlist: {
                        customerService: [39, 43, 37, 37],
                        leadGeneration: [58, 65, 56, 55],
                        loanActivities: [30, 34, 29, 30]
                    },
                    convertedLeads: {
                        customerService: [7, 8, 6, 7],
                        leadGeneration: [13, 15, 12, 14],
                        loanActivities: [5, 6, 5, 6]
                    }
                },
                "Northern": {
                    branches: ["Garissa", "Mandera", "Wajir", "Marsabit"],
                    hitlist: {
                        customerService: [36, 40, 34, 35],
                        leadGeneration: [47, 52, 45, 45],
                        loanActivities: [24, 27, 23, 24]
                    },
                    convertedLeads: {
                        customerService: [5, 6, 5, 5],
                        leadGeneration: [10, 12, 10, 11],
                        loanActivities: [4, 5, 4, 5]
                    }
                },
                "Southern": {
                    branches: ["Kajiado", "Namanga", "Loitokitok", "Magadi"],
                    hitlist: {
                        customerService: [44, 49, 42, 43],
                        leadGeneration: [66, 74, 63, 64],
                        loanActivities: [36, 40, 34, 35]
                    },
                    convertedLeads: {
                        customerService: [8, 10, 8, 9],
                        leadGeneration: [15, 17, 14, 16],
                        loanActivities: [7, 9, 7, 8]
                    }
                },
                "Coast": {
                    branches: ["Mombasa", "Malindi", "Kilifi", "Lamu"],
                    hitlist: {
                        customerService: [30, 34, 29, 30],
                        leadGeneration: [49, 55, 47, 47],
                        loanActivities: [22, 25, 21, 21]
                    },
                    convertedLeads: {
                        customerService: [4, 5, 5, 5],
                        leadGeneration: [9, 11, 9, 9],
                        loanActivities: [3, 4, 4, 5]
                    }
                },
                "Rift Valley": {
                    branches: ["Nakuru", "Naivasha", "Kericho", "Bomet"],
                    hitlist: {
                        customerService: [33, 37, 32, 32],
                        leadGeneration: [72, 80, 69, 68],
                        loanActivities: [39, 43, 37, 37]
                    },
                    convertedLeads: {
                        customerService: [6, 7, 6, 7],
                        leadGeneration: [17, 20, 16, 18],
                        loanActivities: [7, 8, 7, 7]
                    }
                },
                "Nyanza": {
                    branches: ["Kisii", "Homa Bay", "Migori", "Siaya"],
                    hitlist: {
                        customerService: [18, 21, 18, 18],
                        leadGeneration: [56, 62, 53, 54],
                        loanActivities: [16, 18, 15, 15]
                    },
                    convertedLeads: {
                        customerService: [3, 4, 3, 4],
                        leadGeneration: [12, 14, 11, 11],
                        loanActivities: [3, 3, 3, 3]
                    }
                }
            }
        
    });

    // State for regional hitlist filter and branches chart filters
    // Note: selectedRegion is now defined above with regions state
    // Removed department filter - always show Lead Generation data

    // Fetch dashboard data on component mount
    useEffect(() => {
        const fetchDashboardData = async () => {
            try {
                // Fetch lead statistics
                setStatisticsLoading(true);
                const statisticsData = await financeAnalystService.getLeadStatistics();

                // Fetch leads grouped data (for charts)
                setChartsLoading(true);
                const leadsGroupedData = await financeAnalystService.getLeadsGrouped();

                // Fetch monthly conversion rates
                setConversionChartLoading(true);
                const conversionData = await financeAnalystService.getMonthlyConversionRates(selectedYear);

                // Fetch regional leads data
                setRegionalChartLoading(true);
                const regionalData = await financeAnalystService.getLeadsByRegion();

                // Fetch regions for filter options
                const regionsData = await financeAnalystService.getRegions();

                // Fetch call visit statistics
                setCallVisitStatsLoading(true);
                const callVisitStatsData = await financeAnalystService.getCallVisitStatistics();

                // Fetch top anchors
                setTopAnchorsLoading(true);
                const topAnchorsData = await financeAnalystService.getTopAnchors();

                // Update dashboard data with fetched data
                setDashboardData(prevData => ({
                    ...prevData,
                    totalLeads: statisticsData.totalLeads,
                    hitlistSize: statisticsData.hitlistSize,
                    leadsByCategory: leadsGroupedData.leadsByCategory,
                    leadsByStatus: leadsGroupedData.leadsByStatus,
                    topAnchors: topAnchorsData.topAnchors
                }));

                // Update call visit statistics data
                setCallVisitStats({
                    callsVsTarget: callVisitStatsData.callsVsTarget,
                    visitsVsTarget: callVisitStatsData.visitsVsTarget
                });

                // Update conversion chart data
                setConversionChartData({
                    categories: conversionData.categories,
                    conversionRates: conversionData.conversionRates,
                    year: conversionData.year
                });

                // Update regional chart data
                setRegionalChartData({
                    categories: regionalData.categories,
                    totalLeadsData: regionalData.totalLeadsData,
                    convertedLeadsData: regionalData.convertedLeadsData
                });

                // Update regions for filter
                setRegions(regionsData.regions);

                console.log('Dashboard data loaded successfully:', {
                    statistics: statisticsData,
                    leadsGrouped: leadsGroupedData,
                    conversionData: conversionData,
                    regionalData: regionalData,
                    regionsData: regionsData,
                    callVisitStats: callVisitStatsData,
                    topAnchors: topAnchorsData
                });
            } catch (error) {
                console.error('Failed to fetch dashboard data:', error);
                // Keep existing mock data if API fails
            } finally {
                setStatisticsLoading(false);
                setChartsLoading(false);
                setConversionChartLoading(false);
                setRegionalChartLoading(false);
                setCallVisitStatsLoading(false);
                setTopAnchorsLoading(false);
                setIsLoading(false);
            }
        };

        fetchDashboardData();
    }, [selectedYear]); // Re-fetch when year changes

    // Fetch branch data when selected region changes
    useEffect(() => {
        const fetchBranchData = async () => {
            if (!selectedRegion) return;

            try {
                setBranchChartLoading(true);
                const branchData = await financeAnalystService.getLeadsByBranch(selectedRegion);

                // Update branch chart data
                setBranchChartData({
                    regionName: branchData.regionName,
                    categories: branchData.categories,
                    totalLeadsData: branchData.totalLeadsData,
                    convertedLeadsData: branchData.convertedLeadsData
                });

                console.log('Branch data loaded successfully:', branchData);
            } catch (error) {
                console.error('Failed to fetch branch data:', error);
                // Keep existing mock data if API fails
            } finally {
                setBranchChartLoading(false);
            }
        };

        fetchBranchData();
    }, [selectedRegion]); // Re-fetch when region changes

    // Debug logging
    useEffect(() => {
        console.log('=== FinanceAnalystDashboard Debug ===');
        console.log('dashboardData:', dashboardData);
        console.log('conversionChartData:', conversionChartData);
        console.log('selectedYear:', selectedYear);
        console.log('convertedLeads exists:', !!dashboardData.convertedLeads);
        console.log('branchesByRegion exists:', !!dashboardData.branchesByRegion);
        console.log('selectedRegion:', selectedRegion);
        console.log('statisticsLoading:', statisticsLoading);
        console.log('chartsLoading:', chartsLoading);
        console.log('conversionChartLoading:', conversionChartLoading);
        console.log('topAnchorsLoading:', topAnchorsLoading);
        console.log('=====================================');
    }, [dashboardData, conversionChartData, selectedYear, selectedRegion, statisticsLoading, chartsLoading, conversionChartLoading, topAnchorsLoading]);

    // Intersection Observer for animations
    useEffect(() => {
        const observer = new IntersectionObserver(
            (entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting && !hasAnimated) {
                        setShouldAnimate(true);
                        setHasAnimated(true);
                    }
                });
            },
            { threshold: 0.3 }
        );

        if (dashboardRef.current) {
            observer.observe(dashboardRef.current);
        }

        return () => observer.disconnect();
    }, [hasAnimated]);

    // API Integration Functions are now handled by financeAnalystService

    // Function to get regional hitlist data for Lead Generation department only
    const getRegionalHitlistData = () => {
        const regions = ["Central", "Eastern", "Western", "Northern", "Southern", "Coast", "Rift Valley", "Nyanza"];
        
        // Return data for Lead Generation department only
        const departmentData = dashboardData.departmentHitlists.leadGeneration;
        return regions.map(region => departmentData?.regions[region] || 0);
    };

    // Function to get regional converted leads data for Lead Generation department only
    const getRegionalConvertedLeadsData = () => {
        const regions = ["Central", "Eastern", "Western", "Northern", "Southern", "Coast", "Rift Valley", "Nyanza"];

        // Check if convertedLeads data exists
        if (!dashboardData.convertedLeads) {
            console.warn('convertedLeads data not found, returning zeros');
            return regions.map(() => 0);
        }

        // Return data for Lead Generation department only
        const departmentData = dashboardData.convertedLeads.leadGeneration;
        return regions.map(region => departmentData?.regions?.[region] || 0);
    };

    // Function to get branches hitlist data for Lead Generation department only
    const getBranchesHitlistData = () => {
        // Check if branchesByRegion data exists
        if (!dashboardData.branchesByRegion) {
            console.warn('branchesByRegion data not found, returning empty array');
            return [];
        }

        const regionData = dashboardData.branchesByRegion[selectedRegion];
        if (!regionData) {
            console.warn(`Region data for ${selectedRegion} not found, returning empty array`);
            return [];
        }

        // Return data for Lead Generation department only
        return regionData.hitlist?.leadGeneration || [];
    };

    // Function to get branches converted leads data for Lead Generation department only
    const getBranchesConvertedLeadsData = () => {
        // Check if branchesByRegion data exists
        if (!dashboardData.branchesByRegion) {
            console.warn('branchesByRegion data not found, returning empty array');
            return [];
        }

        const regionData = dashboardData.branchesByRegion[selectedRegion];
        if (!regionData) {
            console.warn(`Region data for ${selectedRegion} not found, returning empty array`);
            return [];
        }

        // Return data for Lead Generation department only
        return regionData.convertedLeads?.leadGeneration || [];
    };

    // Chart configurations
    const leadConversionChart = {
        series: [{
            name: 'Conversion Rate',
            data: conversionChartData.conversionRates
        }],
        options: {
            chart: { type: 'line', toolbar: { show: false } },
            stroke: { curve: 'smooth', width: 3 },
            colors: ['#1C5B41'],
            xaxis: {
                categories: conversionChartData.categories,
                labels: { style: { colors: '#6b7280' } }
            },
            yaxis: {
                title: { text: 'Conversion Rate (%)' },
                labels: { style: { colors: '#6b7280' } }
            },
            grid: { borderColor: '#e5e7eb' },
            tooltip: {
                theme: 'light',
                y: {
                    formatter: function (val) {
                        return val + '%';
                    }
                }
            }
        }
    };

    // Function to get leads by status chart data
    const getLeadsByStatusChartData = () => {
        const statuses = Object.keys(dashboardData.leadsByStatus || {});
        const data = Object.values(dashboardData.leadsByStatus || {});
        const labels = statuses.map(status =>
            status.charAt(0).toUpperCase() + status.slice(1)
        );

        // Define colors for different statuses
        const statusColors = {
            'Hot': '#FF0000',
            'Warm': '#369DC9',
            'Cold': '#1C5B41',
            'Pending': '#FFB800'
        };

        const colors = labels.map(label => statusColors[label] || '#6B7280');

        return { labels, data, colors };
    };

    const activityCompletionChart = {
        series: getLeadsByStatusChartData().data,
        options: {
            chart: { type: 'pie' },
            labels: getLeadsByStatusChartData().labels,
            colors: getLeadsByStatusChartData().colors,
            legend: { position: 'bottom' },
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: { width: 200 },
                    legend: { position: 'bottom' }
                }
            }]
        }
    };

    // Function to get leads by category chart data
    const getLeadsByCategoryChartData = () => {
        const categories = Object.keys(dashboardData.leadsByCategory || {});
        const data = Object.values(dashboardData.leadsByCategory || {});
        const labels = categories.map(cat =>
            cat.charAt(0).toUpperCase() + cat.slice(1)
        );

        return { categories: labels, data };
    };

    const leadsByCategoryChart = {
        series: [{
            name: 'Leads',
            data: getLeadsByCategoryChartData().data
        }],
        options: {
            chart: { type: 'bar', toolbar: { show: false } },
            plotOptions: { bar: { horizontal: false, columnWidth: '50%' } },
            xaxis: { categories: getLeadsByCategoryChartData().categories },
            colors: ['#1C5B41'],
            yaxis: { title: { text: 'Number of Leads' } }
        }
    };

    const customerFeedbackChart = {
        series: [
            dashboardData.customerFeedback.positive,
            dashboardData.customerFeedback.neutral,
            dashboardData.customerFeedback.negative
        ],
        options: {
            chart: { type: 'donut' },
            labels: ['Positive', 'Neutral', 'Negative'],
            colors: ['#10B981', '#F59E0B', '#EF4444'],
            legend: { position: 'bottom' }
        }
    };

    // Department Hitlist Charts
    const customerServiceHitlistChart = {
        series: Object.values(dashboardData.departmentHitlists.customerService.regions),
        options: {
            chart: { type: 'pie' },
            labels: Object.keys(dashboardData.departmentHitlists.customerService.regions),
            colors: ['#1C5B41', '#69AF57', '#3B82F6', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4', '#84CC16'],
            legend: { position: 'bottom', fontSize: '12px' },
            title: {
                text: `Total: ${dashboardData.departmentHitlists.customerService.total}`,
                align: 'center',
                style: { fontSize: '14px', fontWeight: 'bold', color: '#1f2937' }
            },
            responsive: [{
                breakpoint: 768,
                options: {
                    chart: { width: 280 },
                    legend: { position: 'bottom' }
                }
            }]
        }
    };

    const leadGenerationHitlistChart = {
        series: Object.values(dashboardData.departmentHitlists.leadGeneration.regions),
        options: {
            chart: { type: 'pie' },
            labels: Object.keys(dashboardData.departmentHitlists.leadGeneration.regions),
            colors: ['#1C5B41', '#69AF57', '#3B82F6', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4', '#84CC16'],
            legend: { position: 'bottom', fontSize: '12px' },
            title: {
                text: `Total: ${dashboardData.departmentHitlists.leadGeneration.total}`,
                align: 'center',
                style: { fontSize: '14px', fontWeight: 'bold', color: '#1f2937' }
            },
            responsive: [{
                breakpoint: 768,
                options: {
                    chart: { width: 280 },
                    legend: { position: 'bottom' }
                }
            }]
        }
    };

    const loanActivitiesHitlistChart = {
        series: Object.values(dashboardData.departmentHitlists.loanActivities.regions),
        options: {
            chart: { type: 'pie' },
            labels: Object.keys(dashboardData.departmentHitlists.loanActivities.regions),
            colors: ['#1C5B41', '#69AF57', '#3B82F6', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4', '#84CC16'],
            legend: { position: 'bottom', fontSize: '12px' },
            title: {
                text: `Total: ${dashboardData.departmentHitlists.loanActivities.total}`,
                align: 'center',
                style: { fontSize: '14px', fontWeight: 'bold', color: '#1f2937' }
            },
            responsive: [{
                breakpoint: 768,
                options: {
                    chart: { width: 280 },
                    legend: { position: 'bottom' }
                }
            }]
        }
    };

    // Regional Hitlist vs Converted Leads Bar Chart
    const regionalHitlistChart = {
        series: [{
            name: 'Total Leads',
            data: regionalChartData.totalLeadsData
        }, {
            name: 'Converted Leads',
            data: regionalChartData.convertedLeadsData
        }],
        options: {
            chart: { type: 'bar', toolbar: { show: false } },
            plotOptions: {
                bar: {
                    horizontal: false,
                    columnWidth: '60%',
                    borderRadius: 4
                }
            },
            xaxis: {
                categories: regionalChartData.categories,
                labels: {
                    style: { colors: '#6b7280', fontSize: '12px' },
                    rotate: -45
                }
            },
            yaxis: {
                title: { text: 'Count' },
                labels: { style: { colors: '#6b7280' } }
            },
            colors: ['#10B981', '#6EE7B7'],
            grid: { borderColor: '#e5e7eb' },
            legend: {
                position: 'top',
                horizontalAlign: 'center',
                fontSize: '14px',
                fontWeight: 500
            },
            tooltip: {
                theme: 'light',
                shared: true,
                intersect: false,
                y: {
                    formatter: function (val, { seriesIndex }) {
                        return val + (seriesIndex === 0 ? ' total leads' : ' converted');
                    }
                }
            },
            dataLabels: {
                enabled: true,
                style: {
                    fontSize: '10px',
                    fontWeight: 'bold',
                    colors: ['#fff']
                }
            }
        }
    };

    // Branches Hitlist vs Converted Leads Bar Chart
    const branchesHitlistChart = {
        series: [{
            name: 'Total Leads',
            data: branchChartData.totalLeadsData
        }, {
            name: 'Converted Leads',
            data: branchChartData.convertedLeadsData
        }],
        options: {
            chart: { type: 'bar', toolbar: { show: false } },
            plotOptions: {
                bar: {
                    horizontal: false,
                    columnWidth: '60%',
                    borderRadius: 4
                }
            },
            xaxis: {
                categories: branchChartData.categories,
                labels: {
                    style: { colors: '#6b7280', fontSize: '12px' },
                    rotate: -45
                }
            },
            yaxis: {
                title: { text: 'Count' },
                labels: { style: { colors: '#6b7280' } }
            },
            colors: ['#10B981', '#6EE7B7'],
            grid: { borderColor: '#e5e7eb' },
            legend: {
                position: 'top',
                horizontalAlign: 'center',
                fontSize: '14px',
                fontWeight: 500
            },
            tooltip: {
                theme: 'light',
                shared: true,
                intersect: false,
                y: {
                    formatter: function (val, { seriesIndex }) {
                        return val + (seriesIndex === 0 ? ' total leads' : ' converted');
                    }
                }
            },
            dataLabels: {
                enabled: true,
                style: {
                    fontSize: '10px',
                    fontWeight: 'bold',
                    colors: ['#fff']
                }
            }
        }
    };

    // Safety check to ensure data is loaded
    if (!dashboardData || !dashboardData.departmentHitlists) {
        return (
            <div className="container mx-auto p-4 space-y-6">
                <div className="text-center py-8">
                    <div className="text-lg text-gray-600">Loading dashboard data...</div>
                </div>
            </div>
        );
    }

    return (
        <div className="container mx-auto p-4 space-y-6" ref={dashboardRef}>
            {/* Header */}
            <div className="mb-6">
                <h1 className="text-2xl font-bold text-gray-900 mb-2">Finance Analyst Dashboard</h1>

            </div>

            {/* Key Performance Indicators */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                {statisticsLoading ? (
                    // Loading skeleton for KPI cards
                    <>
                        {[1, 2, 3, 4].map((item) => (
                            <div key={item} className="bg-white p-6 rounded-lg shadow animate-pulse">
                                <div className="flex items-center justify-between mb-4">
                                    <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                                    <div className="h-6 w-6 bg-gray-300 rounded"></div>
                                </div>
                                <div className="h-8 bg-gray-300 rounded w-1/2 mb-2"></div>
                                <div className="h-3 bg-gray-300 rounded w-full"></div>
                            </div>
                        ))}
                    </>
                ) : (
                    // Actual KPI cards with data
                    <>
                        <DashboardCard
                            title="Total Leads (MTD)"
                            value={dashboardData.totalLeads.mtd}
                            description="Month-to-date lead generation"
                            icon={<UserGroupIcon className="w-6 h-6" />}
                            iconColor="#1C5B41"
                            duration={2000}
                            delay={shouldAnimate ? 0 : 0}
                        />
                        <DashboardCard
                            title="Conversion Rate"
                            value={dashboardData.totalLeads.conversionRate}
                            description="Lead to customer conversion"
                            icon={<IoIosTrendingUp className="w-6 h-6" />}
                            iconColor="#69AF57"
                            duration={2000}
                            delay={shouldAnimate ? 200 : 0}
                        />
                        <DashboardCard
                            title="Converted Leads (MTD)"
                            value={dashboardData.totalLeads.convertedLeadsMtd}
                            description="Converted leads this month"
                            icon={<ChartBarIcon className="w-6 h-6" />}
                            iconColor="#1C5B41"
                            duration={2000}
                            delay={shouldAnimate ? 400 : 0}
                        />
                        <DashboardCard
                            title="Hitlist Size"
                            value={dashboardData.hitlistSize}
                            description="Bank-wide prospects"
                            icon={<BuildingOfficeIcon className="w-6 h-6" />}
                            iconColor="#69AF57"
                            duration={2000}
                            delay={shouldAnimate ? 600 : 0}
                        />
                    </>
                )}
            </div>

            {/* Lead Generation Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-white p-6 rounded-lg shadow">
                    <div className="flex justify-between items-center mb-4">
                        <h3 className="text-lg font-semibold text-gray-900">Lead Conversion Trend</h3>
                        <div className="flex items-center gap-2">
                            <label htmlFor="year-filter" className="text-sm text-gray-600">Year:</label>
                            <select
                                id="year-filter"
                                value={selectedYear}
                                onChange={(e) => setSelectedYear(parseInt(e.target.value))}
                                className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                disabled={conversionChartLoading}
                            >
                                {yearOptions.map(year => (
                                    <option key={year} value={year}>{year}</option>
                                ))}
                            </select>
                        </div>
                    </div>
                    {conversionChartLoading ? (
                        <div className="animate-pulse">
                            <div className="h-64 bg-gray-300 rounded"></div>
                        </div>
                    ) : (
                        <Chart
                            options={leadConversionChart.options}
                            series={leadConversionChart.series}
                            type="line"
                            height={300}
                        />
                    )}
                </div>
                <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Leads by Category</h3>
                    {chartsLoading ? (
                        <div className="animate-pulse">
                            <div className="h-64 bg-gray-300 rounded"></div>
                        </div>
                    ) : (
                        <Chart
                            options={leadsByCategoryChart.options}
                            series={leadsByCategoryChart.series}
                            type="bar"
                            height={300}
                        />
                    )}
                </div>
            </div>

            {/* Activity Performance */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Leads Status</h3>
                    {chartsLoading ? (
                        <div className="animate-pulse">
                            <div className="h-64 bg-gray-300 rounded"></div>
                        </div>
                    ) : (
                        <Chart
                            options={activityCompletionChart.options}
                            series={activityCompletionChart.series}
                            type="pie"
                            height={300}
                        />
                    )}
                </div>
                <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Customer Feedback Trends</h3>
                    <Chart
                        options={customerFeedbackChart.options}
                        series={customerFeedbackChart.series}
                        type="donut"
                        height={300}
                    />
                </div>
            </div>

            {/* Target Achievement and Performance Metrics */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Calls vs Target (MTD)</h3>
                    {callVisitStatsLoading ? (
                        <div className="space-y-4 animate-pulse">
                            <div className="flex justify-between items-center">
                                <div className="h-4 bg-gray-300 rounded w-16"></div>
                                <div className="h-4 bg-gray-300 rounded w-20"></div>
                            </div>
                            <div className="flex justify-between items-center">
                                <div className="h-4 bg-gray-300 rounded w-16"></div>
                                <div className="h-4 bg-gray-300 rounded w-20"></div>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="h-2 bg-gray-300 rounded-full w-3/4"></div>
                            </div>
                            <div className="text-center">
                                <div className="h-4 bg-gray-300 rounded w-24 mx-auto"></div>
                            </div>
                        </div>
                    ) : (
                        <div className="space-y-4">
                            <div className="flex justify-between items-center">
                                <span className="text-gray-600">Made</span>
                                <span className="font-semibold">{callVisitStats.callsVsTarget.made.toLocaleString()}</span>
                            </div>
                            <div className="flex justify-between items-center">
                                <span className="text-gray-600">Target</span>
                                <span className="font-semibold">{callVisitStats.callsVsTarget.target.toLocaleString()}</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div
                                    className="bg-green-600 h-2 rounded-full"
                                    style={{
                                        width: `${callVisitStats.callsVsTarget.target > 0
                                            ? Math.min((callVisitStats.callsVsTarget.made / callVisitStats.callsVsTarget.target) * 100, 100)
                                            : 0}%`
                                    }}
                                ></div>
                            </div>
                            <div className="text-center text-sm text-gray-600">
                                {callVisitStats.callsVsTarget.target > 0
                                    ? Math.round((callVisitStats.callsVsTarget.made / callVisitStats.callsVsTarget.target) * 100)
                                    : 0}% Achievement
                            </div>
                        </div>
                    )}
                </div>

                <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Visits vs Target (MTD)</h3>
                    {callVisitStatsLoading ? (
                        <div className="space-y-4 animate-pulse">
                            <div className="flex justify-between items-center">
                                <div className="h-4 bg-gray-300 rounded w-16"></div>
                                <div className="h-4 bg-gray-300 rounded w-20"></div>
                            </div>
                            <div className="flex justify-between items-center">
                                <div className="h-4 bg-gray-300 rounded w-16"></div>
                                <div className="h-4 bg-gray-300 rounded w-20"></div>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="h-2 bg-gray-300 rounded-full w-3/4"></div>
                            </div>
                            <div className="text-center">
                                <div className="h-4 bg-gray-300 rounded w-24 mx-auto"></div>
                            </div>
                        </div>
                    ) : (
                        <div className="space-y-4">
                            <div className="flex justify-between items-center">
                                <span className="text-gray-600">Made</span>
                                <span className="font-semibold">{callVisitStats.visitsVsTarget.made.toLocaleString()}</span>
                            </div>
                            <div className="flex justify-between items-center">
                                <span className="text-gray-600">Target</span>
                                <span className="font-semibold">{callVisitStats.visitsVsTarget.target.toLocaleString()}</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div
                                    className="bg-blue-600 h-2 rounded-full"
                                    style={{
                                        width: `${callVisitStats.visitsVsTarget.target > 0
                                            ? Math.min((callVisitStats.visitsVsTarget.made / callVisitStats.visitsVsTarget.target) * 100, 100)
                                            : 0}%`
                                    }}
                                ></div>
                            </div>
                            <div className="text-center text-sm text-gray-600">
                                {callVisitStats.visitsVsTarget.target > 0
                                    ? Math.round((callVisitStats.visitsVsTarget.made / callVisitStats.visitsVsTarget.target) * 100)
                                    : 0}% Achievement
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/*/!* Department Hitlists by Region *!/*/}
            {/*<div className="bg-white p-6 rounded-lg shadow">*/}
            {/*  <h3 className="text-lg font-semibold text-gray-900 mb-6">Department Hitlists by Region</h3>*/}
            {/*  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">*/}
            {/*    <div className="text-center">*/}
            {/*      <h4 className="text-md font-medium text-gray-700 mb-4">Customer Service</h4>*/}
            {/*      <Chart*/}
            {/*        options={customerServiceHitlistChart.options}*/}
            {/*        series={customerServiceHitlistChart.series}*/}
            {/*        type="pie"*/}
            {/*        height={300}*/}
            {/*      />*/}
            {/*    </div>*/}
            {/*    <div className="text-center">*/}
            {/*      <h4 className="text-md font-medium text-gray-700 mb-4">Lead Generation</h4>*/}
            {/*      <Chart*/}
            {/*        options={leadGenerationHitlistChart.options}*/}
            {/*        series={leadGenerationHitlistChart.series}*/}
            {/*        type="pie"*/}
            {/*        height={300}*/}
            {/*      />*/}
            {/*    </div>*/}
            {/*    <div className="text-center">*/}
            {/*      <h4 className="text-md font-medium text-gray-700 mb-4">Loan Activities</h4>*/}
            {/*      <Chart*/}
            {/*        options={loanActivitiesHitlistChart.options}*/}
            {/*        series={loanActivitiesHitlistChart.series}*/}
            {/*        type="pie"*/}
            {/*        height={300}*/}
            {/*      />*/}
            {/*    </div>*/}
            {/*  </div>*/}
            {/*</div>*/}

            {/* Regional Hitlist and Top Performing Anchors */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Regional Hitlist Bar Chart */}
                <div className="lg:col-span-2 bg-white p-6 rounded-lg shadow">
                    <div className="flex justify-between items-center mb-4">
                        <h3 className="text-lg font-semibold text-gray-900">Regional Leads Conversion Overview</h3>
                    </div>
                    {regionalChartLoading ? (
                        <div className="animate-pulse">
                            <div className="h-80 bg-gray-300 rounded"></div>
                        </div>
                    ) : (
                        <Chart
                            options={regionalHitlistChart.options}
                            series={regionalHitlistChart.series}
                            type="bar"
                            height={350}
                        />
                    )}
                </div>

                {/* Top Performing Anchors */}
                <div className="lg:col-span-1 bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Performing Anchors</h3>

                    {topAnchorsLoading ? (
                        <div className="space-y-3">
                            {[1, 2, 3].map((item) => (
                                <div key={item} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg animate-pulse">
                                    <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                                    <div className="h-4 bg-gray-300 rounded w-16"></div>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <div className="space-y-3">
                            {dashboardData.topAnchors.map((anchor, index) => (
                                <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                    <span className="font-medium text-gray-900">{anchor.name}</span>
                                    <span className="text-green-600 font-semibold">{anchor.leads} leads</span>
                                </div>
                            ))}
                        </div>
                    )}

                    {/* Additional stats */}
                    <div className="mt-6 pt-4 border-t border-gray-200">
                        <div className="grid grid-cols-2 gap-4 text-center">
                            <div>
                                <div className="text-2xl font-bold text-blue-600">
                                    {topAnchorsLoading ? (
                                        <div className="h-8 bg-gray-300 rounded animate-pulse"></div>
                                    ) : (
                                        dashboardData.topAnchors.reduce((sum, anchor) => sum + anchor.leads, 0)
                                    )}
                                </div>
                                <div className="text-sm text-gray-600">Total Leads</div>
                            </div>
                            <div>
                                <div className="text-2xl font-bold text-green-600">
                                    {topAnchorsLoading ? (
                                        <div className="h-8 bg-gray-300 rounded animate-pulse"></div>
                                    ) : (
                                        dashboardData.topAnchors.length
                                    )}
                                </div>
                                <div className="text-sm text-gray-600">Active Anchors</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Branches Hitlist vs Converted Leads */}
            <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-semibold text-gray-900">Branches Customer Conversion Overview</h3>
                    <div className="flex gap-4">
                        <select
                            value={selectedRegion}
                            onChange={(e) => setSelectedRegion(e.target.value)}
                            className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                            disabled={branchChartLoading}
                        >
                            {regions.map(region => (
                                <option key={region.id} value={region.name}>{region.name}</option>
                            ))}
                        </select>
                    </div>
                </div>
                {branchChartLoading ? (
                    <div className="animate-pulse">
                        <div className="h-96 bg-gray-300 rounded"></div>
                    </div>
                ) : (
                    <Chart
                        options={branchesHitlistChart.options}
                        series={branchesHitlistChart.series}
                        type="bar"
                        height={400}
                    />
                )}

                {/* Summary for selected region */}
                <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                        <div>
                            <div className="text-lg font-bold text-blue-600">
                                {branchChartData.totalLeadsData.reduce((sum, val) => sum + (val || 0), 0)}
                            </div>
                            <div className="text-xs text-gray-600">Total Leads</div>
                        </div>
                        <div>
                            <div className="text-lg font-bold text-green-600">
                                {branchChartData.convertedLeadsData.reduce((sum, val) => sum + (val || 0), 0)}
                            </div>
                            <div className="text-xs text-gray-600">Total Converted</div>
                        </div>
                        <div>
                            <div className="text-lg font-bold text-purple-600">
                                {branchChartData.categories.length}
                            </div>
                            <div className="text-xs text-gray-600">Branches</div>
                        </div>
                        <div>
                            <div className="text-lg font-bold text-orange-600">
                                {(() => {
                                    const converted = branchChartData.convertedLeadsData.reduce((sum, val) => sum + (val || 0), 0);
                                    const total = branchChartData.totalLeadsData.reduce((sum, val) => sum + (val || 0), 0);
                                    return converted > 0 && total > 0 ? ((converted / total) * 100).toFixed(1) : 0;
                                })()}%
                            </div>
                            <div className="text-xs text-gray-600">Conversion Rate</div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    );
}

export default FinanceAnalystDashboard;
