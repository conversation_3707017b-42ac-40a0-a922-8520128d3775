import React, { useState, useEffect } from "react";
import DashboardCard from "../cards/DashboardCard";
import Monthly2x2ActivityChart from "../charts/Monthly2x2ActivityChart";
import General2by2CompletionChart from "../charts/General2by2CompletionChart";
import CustomerFeedbackChart from "../charts/CustomerFeedbackChart";
import MonthlyCallStatusChart from "../charts/MonthlyCallStatusChart";
import DormantProgressChart from "../charts/DormantProgressChart";
import CallActivitiesTargetChart from "../charts/CallActivitiesTargetChart";
import OverdueCallsTrendChart from "../charts/OverdueCallsTrendChart";
import OverdueCallsTable from "../tables/OverdueCallsTable";
import CustomerExperienceSection from "../sections/CustomerExperienceSection";
import ChartSkeleton from "../skeletons/ChartSkeleton";
import { serviceDeliverySupervisorDashboardService } from "../../services/serviceDeliverySupervisorDashboardService";

function ServiceDeliverySupervisorDashboard({
  userRole = "service-delivery-supervisor",
}) {
  // Dashboard data state
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        const data =
          await serviceDeliverySupervisorDashboardService.getDashboardData();
        setDashboardData(data);
        setError(null);
      } catch (err) {
        console.error("Error fetching dashboard data:", err);
        setError("Failed to load dashboard data");
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // Extract summary cards data from API response
  const summaryCards = dashboardData?.summary_cards || {
    total_dormant_size: { total: 0, this_month: 0 },
    total_2by2by2_size: { total: 0, this_month: 0 },
    calls_this_month: 0,
    overdue_calls_this_month: 0,
  };

  // Icon components
  const DormantIcon = (
    <svg
      className="w-7 h-7 text-[#F47976]"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
      />
    </svg>
  );

  const Total2by2Icon = (
    <svg
      className="w-7 h-7 text-[#1c5b41]"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"
      />
    </svg>
  );

  const CallsIcon = (
    <svg
      className="w-7 h-7 text-[#3B82F6]"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
      />
    </svg>
  );

  const OverdueIcon = (
    <svg
      className="w-7 h-7 text-[#FDCA37]"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
      />
    </svg>
  );

  // Trend icon component
  const TrendIcon = (
    <svg
      className="w-4 h-4 text-green-600"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M7 17l9.2-9.2M17 17V7H7"
      />
    </svg>
  );

  // Loading state
  if (loading) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-medium mb-6">
          {userRole === "customer-experience-officer"
            ? "Customer Experience Officer Dashboard"
            : "Service Delivery Supervisor Dashboard"}
        </h1>

        {/* Dashboard Cards Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {[1, 2, 3, 4].map((item) => (
            <div
              key={item}
              className="bg-white p-6 rounded-lg shadow animate-pulse"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                <div className="h-6 w-6 bg-gray-300 rounded"></div>
              </div>
              <div className="h-8 bg-gray-300 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-300 rounded w-full"></div>
            </div>
          ))}
        </div>

        {/* Charts Skeleton */}
        <div className="mb-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <ChartSkeleton chartType="bar" title="Monthly 2x2x2 Activity" />
            </div>
            <div className="lg:col-span-1">
              <ChartSkeleton chartType="pie" title="2x2x2 Completion Rate" />
            </div>
          </div>
        </div>

        <div className="mb-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <ChartSkeleton chartType="pie" title="Customer Feedback" />
            <ChartSkeleton chartType="pie" title="Monthly Call Status" />
          </div>
        </div>

        <div className="mb-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-1">
              <ChartSkeleton chartType="pie" title="Dormant List Progress" />
            </div>
            <div className="lg:col-span-2">
              <ChartSkeleton chartType="line" title="Overdue Calls Trend" />
            </div>
          </div>
        </div>

        {userRole === "service-delivery-supervisor" && (
          <div className="mb-8">
            <ChartSkeleton chartType="bar" title="Call Activities vs Target" />
          </div>
        )}
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-medium mb-6">
          {userRole === "customer-experience-officer"
            ? "Customer Experience Officer Dashboard"
            : "Service Delivery Supervisor Dashboard"}
        </h1>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg text-red-600">{error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <h1 className="text-2xl font-medium mb-6">
        {userRole === "customer-experience-officer"
          ? "Customer Experience Officer Dashboard"
          : "Service Delivery Supervisor Dashboard"}
      </h1>

      {/* Top Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <DashboardCard
          title="Total Dormant Size"
          value={summaryCards.total_dormant_size.total}
          icon={DormantIcon}
          iconColor="#F47976"
          duration={2000}
          delay={200}
          trend={summaryCards.total_dormant_size.this_month.toString()}
          // trendIcon={TrendIcon}
        />

        <DashboardCard
          title="Total 2by2 Size"
          value={summaryCards.total_2by2by2_size.total}
          icon={Total2by2Icon}
          iconColor="#1c5b41"
          duration={1500}
          delay={400}
          trend={summaryCards.total_2by2by2_size.this_month.toString()}
          // trendIcon={TrendIcon}
        />

        <DashboardCard
          title="Calls this month"
          value={summaryCards.calls_this_month}
          icon={CallsIcon}
          iconColor="#3B82F6"
          duration={1000}
          delay={600}
          description={"Calls made this month"}
          // trend={callsTrend}
          trendIcon={TrendIcon}
        />

        <DashboardCard
          title="Overdue calls this month"
          value={summaryCards.overdue_calls_this_month}
          icon={OverdueIcon}
          iconColor="#FDCA37"
          duration={800}
          delay={800}
          description={"Overdue calls this month"}
          // trend={overdueTrend}
          trendIcon={TrendIcon}
        />
      </div>

      {/* Customer Experience Officer Section */}
      {userRole === "customer-experience-officer" && (
        <div className="mb-8">
          <CustomerExperienceSection dashboardData={dashboardData} />
        </div>
      )}

      {/* First Section: Monthly 2x2x2 Activity (2/3) and 2x2x2 Completion Rate (1/3) */}
      <div className="mb-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Monthly 2x2x2 Activity Chart - 2/3 width */}
          <div className="lg:col-span-2">
            <Monthly2x2ActivityChart dashboardData={dashboardData} />
          </div>

          {/* 2x2x2 Completion Rate Chart - 1/3 width */}
          <div className="lg:col-span-1">
            <General2by2CompletionChart dashboardData={dashboardData} />
          </div>
        </div>
      </div>

      {/* Second Section: Customer Feedback and Monthly Call Status */}
      <div className="mb-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Customer Feedback Chart */}
          <div>
            <CustomerFeedbackChart dashboardData={dashboardData} />
          </div>

          {/* Monthly Call Status Chart */}
          <div>
            <MonthlyCallStatusChart dashboardData={dashboardData} />
          </div>
        </div>
      </div>

      {/* Third Section: Dormant List Progress and Overdue Calls Trend */}
      <div className="mb-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Dormant List Progress Chart - 1/3 width */}
          <div className="lg:col-span-1">
            <DormantProgressChart dashboardData={dashboardData} />
          </div>

          {/* Overdue Calls Trend Chart - 2/3 width */}
          <div className="lg:col-span-2">
            <OverdueCallsTrendChart dashboardData={dashboardData} />
          </div>
        </div>
      </div>

      {/* Call Activities vs Target Chart - Only for service-delivery-supervisor */}
      {userRole === "service-delivery-supervisor" && (
        <div className="mb-8">
          <CallActivitiesTargetChart />
        </div>
      )}

      {/* Overdue Calls Table Section */}
      <OverdueCallsTable dashboardData={dashboardData} />
    </div>
  );
}

export default ServiceDeliverySupervisorDashboard;
