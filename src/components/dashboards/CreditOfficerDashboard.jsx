import React, { useRef, useEffect, useState } from "react";
import Chart from 'react-apexcharts';
import DashboardCard from "../cards/DashboardCard";
import ActivitiesTable from "../tables/ActivitiesTable";
import useCountUp from "../../hooks/useCountUp";
import { 
  CurrencyDollarIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  DocumentTextIcon,
  PhoneIcon,
  CalendarIcon,
  UserGroupIcon,
  ChartBarIcon,
  BanknotesIcon
} from '@heroicons/react/24/outline';
import { IoIosTrendingUp } from "react-icons/io";
import instance from "../../axios/instance";

function CreditOfficerDashboard() {
  // Animation state
  const [hasAnimated, setHasAnimated] = useState(false);
  const [shouldAnimate, setShouldAnimate] = useState(false);
  const dashboardRef = useRef(null);

  // Mock data - replace with API calls
  const [dashboardData, setDashboardData] = useState({
    // Loan Portfolio Overview
    loanPortfolio: {
      totalActiveLoans: 1247,
      applications: 89,
      restructures: 45,
      postDisbursement: 567,
      inArrears: 234,
      totalBalance: ********
    },
    
    // Arrears Summary
    arrearsSummary: {
      days1to6: { count: 156, amount: 2340000 },
      moreThan6Days: { count: 78, amount: 1890000 },
      totalOverdue: 4230000,
      topLoansByBalance: [
        { clientId: "CL001", accountNo: "*************", balance: 450000 },
        { clientId: "CL002", accountNo: "*************", balance: 380000 },
        { clientId: "CL003", accountNo: "*************", balance: 320000 }
      ]
    },
    
    // Credit Activity Metrics
    creditActivities: {
      byPurpose: {
        loanApplication: 89,
        restructure: 45,
        postDisbursement: 234,
        collection1to6: 156,
        firstDemand: 67,
        druForwarding: 23
      },
      successRates: {
        collections: 78.5,
        recoveries: 65.2,
        restructures: 82.1
      },
      attachments: 456,
      comments: 789
    },
    
    // Hitlist and Follow-Up Tracking
    hitlistTracking: {
      creditHitlistSize: 567,
      callsMTD: { made: 234, target: 280 },
      visitsMTD: { made: 89, target: 120 },
      overdueActivities: 45,
      scheduledActivities: 123
    },
    
    // Performance and Escalations
    performance: {
      rmPerformance: [
        { rmCode: "RM001", activities: 45, target: 50, achievement: 90 },
        { rmCode: "RM002", activities: 38, target: 45, achievement: 84 },
        { rmCode: "RM003", activities: 52, target: 55, achievement: 95 }
      ],
      escalations: {
        druForwards: 23,
        demandIssuances: 67,
        unresolvedArrears: 34
      }
    },
    
    // Risk and Recovery Insights
    riskRecovery: {
      monthlyRecovery: [1200000, 1350000, 1180000, 1420000, 1380000, 1560000],
      recoveryTarget: 1500000,
      recoveryRate: 78.5,
      riskCategories: {
        low: 234,
        medium: 156,
        high: 89,
        critical: 23
      }
    }
  });

  // Intersection Observer for animations
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !hasAnimated) {
            setShouldAnimate(true);
            setHasAnimated(true);
          }
        });
      },
      { threshold: 0.3 }
    );

    if (dashboardRef.current) {
      observer.observe(dashboardRef.current);
    }

    return () => observer.disconnect();
  }, [hasAnimated]);

  // API Integration Functions (for future use)
  const fetchLoanPortfolioData = async () => {
    try {
      const response = await instance.get('/dashboard/credit-officer/portfolio');
      return response.data;
    } catch (error) {
      console.error('Error fetching loan portfolio data:', error);
      throw error;
    }
  };

  const fetchArrearsData = async () => {
    try {
      const response = await instance.get('/dashboard/credit-officer/arrears');
      return response.data;
    } catch (error) {
      console.error('Error fetching arrears data:', error);
      throw error;
    }
  };

  const fetchCreditActivityData = async () => {
    try {
      const response = await instance.get('/dashboard/credit-officer/activities');
      return response.data;
    } catch (error) {
      console.error('Error fetching credit activity data:', error);
      throw error;
    }
  };

  const fetchPerformanceData = async () => {
    try {
      const response = await instance.get('/dashboard/credit-officer/performance');
      return response.data;
    } catch (error) {
      console.error('Error fetching performance data:', error);
      throw error;
    }
  };

  const fetchRiskRecoveryData = async () => {
    try {
      const response = await instance.get('/dashboard/credit-officer/risk-recovery');
      return response.data;
    } catch (error) {
      console.error('Error fetching risk recovery data:', error);
      throw error;
    }
  };

  // Chart configurations
  const loanStatusChart = {
    series: [
      dashboardData.loanPortfolio.applications,
      dashboardData.loanPortfolio.restructures,
      dashboardData.loanPortfolio.postDisbursement,
      dashboardData.loanPortfolio.inArrears
    ],
    options: {
      chart: { type: 'pie' },
      labels: ['Applications', 'Restructures', 'Post-Disbursement', 'In Arrears'],
      colors: ['#3B82F6', '#F59E0B', '#10B981', '#EF4444'],
      legend: { position: 'bottom' }
    }
  };

  const activityPurposeChart = {
    series: [{
      name: 'Activities',
      data: [
        dashboardData.creditActivities.byPurpose.loanApplication,
        dashboardData.creditActivities.byPurpose.restructure,
        dashboardData.creditActivities.byPurpose.postDisbursement,
        dashboardData.creditActivities.byPurpose.collection1to6,
        dashboardData.creditActivities.byPurpose.firstDemand,
        dashboardData.creditActivities.byPurpose.druForwarding
      ]
    }],
    options: {
      chart: { type: 'bar', toolbar: { show: false } },
      plotOptions: { bar: { horizontal: true } },
      xaxis: { title: { text: 'Number of Activities' } },
      yaxis: { 
        categories: ['Loan App', 'Restructure', 'Post-Disbursement', 'Collection 1-6', '1st Demand', 'DRU Forward'],
        title: { text: 'Activity Purpose' }
      },
      colors: ['#1C5B41']
    }
  };

  const recoveryTrendChart = {
    series: [{
      name: 'Monthly Recovery',
      data: dashboardData.riskRecovery.monthlyRecovery
    }, {
      name: 'Target',
      data: Array(6).fill(dashboardData.riskRecovery.recoveryTarget)
    }],
    options: {
      chart: { type: 'line', toolbar: { show: false } },
      stroke: { curve: 'smooth', width: 3 },
      colors: ['#10B981', '#6B7280'],
      xaxis: { 
        categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        labels: { style: { colors: '#6b7280' } }
      },
      yaxis: { 
        title: { text: 'Recovery Amount (USD)' },
        labels: { 
          style: { colors: '#6b7280' },
          formatter: function (val) {
            return '$' + (val / 1000000).toFixed(1) + 'M';
          }
        }
      },
      grid: { borderColor: '#e5e7eb' }
    }
  };

  const riskCategoriesChart = {
    series: [
      dashboardData.riskRecovery.riskCategories.low,
      dashboardData.riskRecovery.riskCategories.medium,
      dashboardData.riskRecovery.riskCategories.high,
      dashboardData.riskRecovery.riskCategories.critical
    ],
    options: {
      chart: { type: 'donut' },
      labels: ['Low Risk', 'Medium Risk', 'High Risk', 'Critical'],
      colors: ['#10B981', '#F59E0B', '#EF4444', '#7C2D12'],
      legend: { position: 'bottom' }
    }
  };

  return (
    <div className="container mx-auto p-4 space-y-6" ref={dashboardRef}>
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Credit Officer Dashboard</h1>
        <p className="text-gray-600">Loan management, collections, recoveries, and credit performance monitoring</p>
      </div>

      {/* Key Performance Indicators */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        <DashboardCard
          title="Active Loans"
          value={dashboardData.loanPortfolio.totalActiveLoans}
          description="Total loan accounts"
          icon={<DocumentTextIcon className="w-6 h-6" />}
          iconColor="#1C5B41"
          duration={2000}
          delay={shouldAnimate ? 0 : 0}
        />
        <DashboardCard
          title="Total Balance"
          value={Math.round(dashboardData.loanPortfolio.totalBalance / 1000000)}
          description="Million USD portfolio"
          icon={<BanknotesIcon className="w-6 h-6" />}
          iconColor="#10B981"
          duration={2000}
          delay={shouldAnimate ? 200 : 0}
        />
        <DashboardCard
          title="In Arrears"
          value={dashboardData.loanPortfolio.inArrears}
          description="Loans requiring attention"
          icon={<ExclamationTriangleIcon className="w-6 h-6" />}
          iconColor="#EF4444"
          duration={2000}
          delay={shouldAnimate ? 400 : 0}
        />
        <DashboardCard
          title="Recovery Rate"
          value={dashboardData.riskRecovery.recoveryRate}
          description="Collection efficiency"
          icon={<IoIosTrendingUp className="w-6 h-6" />}
          iconColor="#10B981"
          duration={2000}
          delay={shouldAnimate ? 600 : 0}
        />
      </div>

      {/* Loan Portfolio and Activity Purpose */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Loan Portfolio Status</h3>
          <Chart
            options={loanStatusChart.options}
            series={loanStatusChart.series}
            type="pie"
            height={300}
          />
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Activities by Purpose</h3>
          <Chart
            options={activityPurposeChart.options}
            series={activityPurposeChart.series}
            type="bar"
            height={300}
          />
        </div>
      </div>

      {/* Recovery Trends and Risk Categories */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recovery Trends</h3>
          <Chart
            options={recoveryTrendChart.options}
            series={recoveryTrendChart.series}
            type="line"
            height={300}
          />
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Risk Categories</h3>
          <Chart
            options={riskCategoriesChart.options}
            series={riskCategoriesChart.series}
            type="donut"
            height={300}
          />
        </div>
      </div>

      {/* Performance Metrics and Top Loans */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">RM Performance</h3>
          <div className="space-y-3">
            {dashboardData.performance.rmPerformance.map((rm, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center">
                  <span className="font-medium text-gray-900 mr-4">{rm.rmCode}</span>
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-green-600 h-2 rounded-full"
                      style={{ width: `${rm.achievement}%` }}
                    ></div>
                  </div>
                </div>
                <span className="text-green-600 font-semibold">{rm.achievement}%</span>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Loans by Balance</h3>
          <div className="space-y-3">
            {dashboardData.arrearsSummary.topLoansByBalance.map((loan, index) => (
              <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <div>
                  <div className="font-medium text-gray-900">{loan.clientId}</div>
                  <div className="text-sm text-gray-600">{loan.accountNo}</div>
                </div>
                <span className="text-red-600 font-semibold">
                  ${(loan.balance / 1000).toFixed(0)}K
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Arrears Summary and Escalations */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Arrears Summary</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">1 -6 Days</span>
              <div className="text-right">
                <div className="font-semibold">{dashboardData.arrearsSummary.days1to6.count}</div>
                <div className="text-sm text-gray-500">
                  ${(dashboardData.arrearsSummary.days1to6.amount / 1000).toFixed(0)}K
                </div>
              </div>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">6 Days</span>
              <div className="text-right">
                <div className="font-semibold text-red-600">{dashboardData.arrearsSummary.moreThan6Days.count}</div>
                <div className="text-sm text-red-500">
                  ${(dashboardData.arrearsSummary.moreThan6Days.amount / 1000).toFixed(0)}K
                </div>
              </div>
            </div>
            <div className="border-t pt-2">
              <div className="flex justify-between items-center">
                <span className="font-medium text-gray-900">Total Overdue</span>
                <span className="font-semibold text-red-600">
                  ${(dashboardData.arrearsSummary.totalOverdue / 1000000).toFixed(1)}M
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Activity Targets</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Calls MTD</span>
              <span className="font-semibold">
                {dashboardData.hitlistTracking.callsMTD.made} / {dashboardData.hitlistTracking.callsMTD.target}
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full"
                style={{ width: `${(dashboardData.hitlistTracking.callsMTD.made / dashboardData.hitlistTracking.callsMTD.target) * 100}%` }}
              ></div>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Visits MTD</span>
              <span className="font-semibold">
                {dashboardData.hitlistTracking.visitsMTD.made} / {dashboardData.hitlistTracking.visitsMTD.target}
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-green-600 h-2 rounded-full"
                style={{ width: `${(dashboardData.hitlistTracking.visitsMTD.made / dashboardData.hitlistTracking.visitsMTD.target) * 100}%` }}
              ></div>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Escalations</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <ExclamationTriangleIcon className="w-5 h-5 text-red-500 mr-2" />
                <span className="text-gray-600">DRU Forwards</span>
              </div>
              <span className="font-semibold text-red-600">{dashboardData.performance.escalations.druForwards}</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <DocumentTextIcon className="w-5 h-5 text-orange-500 mr-2" />
                <span className="text-gray-600">Demand Issuances</span>
              </div>
              <span className="font-semibold text-orange-600">{dashboardData.performance.escalations.demandIssuances}</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <ClockIcon className="w-5 h-5 text-yellow-500 mr-2" />
                <span className="text-gray-600">Unresolved Arrears</span>
              </div>
              <span className="font-semibold text-yellow-600">{dashboardData.performance.escalations.unresolvedArrears}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Activities Table */}
      <ActivitiesTable />
    </div>
  );
}

export default CreditOfficerDashboard;
