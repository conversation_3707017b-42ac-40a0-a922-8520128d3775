import React, { useRef, useEffect, useState } from "react";
import Chart from 'react-apexcharts';
import DashboardCard from "../cards/DashboardCard";
import SegmentHeadActivitiesTable from "../tables/SegmentHeadActivitiesTable";
import useCountUp from "../../hooks/useCountUp";
import { 
  UserGroupIcon, 
  ChartBarIcon,
  ClockIcon,
  BuildingOfficeIcon,
  PhoneIcon,
  CalendarIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { IoIosTrendingUp } from "react-icons/io";
import instance from "../../axios/instance";
import { getTopAnchors, getLeadStatistics, getLeadsGrouped, getCallVisitStatistics, getMonthlyConversionRates, getActivitySummary, getActivitiesByFeedback } from "../../services/segmentHeadDashboardService";

function SegmentHeadDashboard() {
  // Animation state
  const [hasAnimated, setHasAnimated] = useState(false);
  const [shouldAnimate, setShouldAnimate] = useState(false);
  const dashboardRef = useRef(null);

  // Loading states
  const [statisticsLoading, setStatisticsLoading] = useState(true);
  const [topAnchorsLoading, setTopAnchorsLoading] = useState(true);
  const [chartsLoading, setChartsLoading] = useState(true);
  const [callVisitStatsLoading, setCallVisitStatsLoading] = useState(true);
  const [conversionChartLoading, setConversionChartLoading] = useState(true);
  const [activitySummaryLoading, setActivitySummaryLoading] = useState(true);
  const [customerFeedbackLoading, setCustomerFeedbackLoading] = useState(true);

  // Year filter state for conversion chart
  const [conversionChartYear, setConversionChartYear] = useState(new Date().getFullYear());

  // Generate year options starting from 2024 to current year + 1
  const generateYearOptions = () => {
    const currentYear = new Date().getFullYear();
    const startYear = 2024;
    const endYear = currentYear + 1;
    const years = [];
    
    for (let year = startYear; year <= endYear; year++) {
      years.push(year);
    }
    
    return years;
  };
  
  const yearOptions = generateYearOptions();

  // Mock data - replace with API calls
  const [dashboardData, setDashboardData] = useState({
    // Lead Generation Summary
    totalLeads: {
      mtd: 1247,
      ytd: 8934,
      convertedLeadsMtd: 1234,
      conversionRate: "23.5%"
    },
    leadsByCategory: {
      employed: 456,
      corporate: 234,
      trader: 557
    },
    topAnchors: [],
    
    // Leads by Status
    leadsByStatus: {
      warm: 2847,
      hot: 453,
      pending: 3300,
      cold: 3300,

    },
    
    // Conversion Chart Data
    conversionChart: {
      categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
      conversionRates: [18.5, 21.2, 19.8, 23.5, 22.1, 24.3],
      year: new Date().getFullYear()
    },

    
    activityBreakdown: {
      relationshipMgmt: 1234,
      customerService: 987,
      loanCollection: 626
    },
    successRates: {
      successful: 78.5,
      failed: 12.3,
      rescheduled: 9.2
    },
    
    // Hitlist and Targets
    hitlistSize: 3456,
    callsVsTarget: {
      made: 1847,
      target: 2100
    },
    visitsVsTarget: {
      made: 567,
      target: 650
    },
    overdueActivities: 89,
    upcomingActivities: 234,
    
    // Customer Engagement
    customerFeedback: {
      positive: 67,
      neutral: 23,
      negative: 10
    },
feedbackTypes: [], // Dynamic feedback types from API
    dormancyRecovery: {
      contacted: 234,
      reactivated: 89
    },

    // Department Hitlists by Region
    departmentHitlists: {
      customerService: {
        total: 1234,
        regions: {
          "Central": 234,
          "Eastern": 189,
          "Western": 156,
          "Northern": 145,
          "Southern": 178,
          "Coast": 123,
          "Rift Valley": 134,
          "Nyanza": 75
        }
      },
      leadGeneration: {
        total: 2156,
        regions: {
          "Central": 456,
          "Eastern": 298,
          "Western": 234,
          "Northern": 189,
          "Southern": 267,
          "Coast": 198,
          "Rift Valley": 289,
          "Nyanza": 225
        }
      },
      loanActivities: {
        total: 987,
        regions: {
          "Central": 178,
          "Eastern": 134,
          "Western": 123,
          "Northern": 98,
          "Southern": 145,
          "Coast": 89,
          "Rift Valley": 156,
          "Nyanza": 64
        }
      }
    }
  });

  // Intersection Observer for animations
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !hasAnimated) {
            setShouldAnimate(true);
            setHasAnimated(true);
          }
        });
      },
      { threshold: 0.3 }
    );

    if (dashboardRef.current) {
      observer.observe(dashboardRef.current);
    }

    return () => observer.disconnect();
  }, [hasAnimated]);

  // Fetch lead statistics data on component mount
  useEffect(() => {
    const fetchLeadStatistics = async () => {
      try {
        setStatisticsLoading(true);
        const statisticsData = await getLeadStatistics();
        setDashboardData(prevData => ({
          ...prevData,
          totalLeads: statisticsData.totalLeads,
          hitlistSize: statisticsData.hitlistSize
        }));
      } catch (error) {
        console.error('Error fetching lead statistics:', error);
      } finally {
        setStatisticsLoading(false);
      }
    };

    fetchLeadStatistics();
  }, []);

  // Fetch leads grouped data on component mount
  useEffect(() => {
    const fetchLeadsGrouped = async () => {
      try {
        setChartsLoading(true);
        const leadsGroupedData = await getLeadsGrouped();
        setDashboardData(prevData => ({
          ...prevData,
          leadsByCategory: leadsGroupedData.leadsByCategory,
          leadsByStatus: leadsGroupedData.leadsByStatus
        }));
      } catch (error) {
        console.error('Error fetching leads grouped data:', error);
      } finally {
        setChartsLoading(false);
      }
    };

    fetchLeadsGrouped();
  }, []);

  // Fetch top anchors data on component mount
  useEffect(() => {
    const fetchTopAnchors = async () => {
      try {
        setTopAnchorsLoading(true);
        const response = await getTopAnchors();
        // Transform the data to match the existing structure
        const transformedAnchors = response.topAnchors.map(anchor => ({
          name: anchor.name,
          leads: anchor.lead_count
        }));
        setDashboardData(prevData => ({
          ...prevData,
          topAnchors: transformedAnchors
        }));
      } catch (error) {
        console.error('Error fetching top anchors:', error);
      } finally {
        setTopAnchorsLoading(false);
      }
    };

    fetchTopAnchors();
  }, []);

  // Fetch call visit statistics data on component mount
  useEffect(() => {
    const fetchCallVisitStatistics = async () => {
      try {
        setCallVisitStatsLoading(true);
        const callVisitStatsData = await getCallVisitStatistics();
        setDashboardData(prevData => ({
          ...prevData,
          callsVsTarget: callVisitStatsData.callsVsTarget,
          visitsVsTarget: callVisitStatsData.visitsVsTarget
        }));
      } catch (error) {
        console.error('Error fetching call visit statistics:', error);
      } finally {
        setCallVisitStatsLoading(false);
      }
    };

    fetchCallVisitStatistics();
  }, []);

  // Fetch conversion chart data on component mount and when year changes
  useEffect(() => {
    const fetchConversionChartData = async () => {
      try {
        setConversionChartLoading(true);
        const conversionData = await getMonthlyConversionRates(conversionChartYear);
        
        setDashboardData(prevData => ({
          ...prevData,
          conversionChart: {
            categories: conversionData.categories,
            conversionRates: conversionData.conversionRates,
            year: conversionData.year
          }
        }));
      } catch (error) {
        console.error('Error fetching conversion chart data:', error);
      } finally {
        setConversionChartLoading(false);
      }
    };

    
        fetchConversionChartData();
      }, [conversionChartYear]);
    
    
      // Fetch activity summary data on component mount
      useEffect(() => {
        const fetchActivitySummary = async () => {
          try {
            setActivitySummaryLoading(true);
            const activitySummaryData = await getActivitySummary();
            setDashboardData(prevData => ({
              ...prevData,
              overdueActivities: activitySummaryData.overdueActivities,
              upcomingActivities: activitySummaryData.upcomingActivities
            }));
          } catch (error) {
            console.error('Error fetching activity summary:', error);
          } finally {
            setActivitySummaryLoading(false);
          }
        };
    
        fetchActivitySummary();
      }, []);
// Fetch customer feedback data on component mount
  useEffect(() => {
    const fetchCustomerFeedback = async () => {
      try {
        setCustomerFeedbackLoading(true);
        const customerFeedbackData = await getActivitiesByFeedback();
        setDashboardData(prevData => ({
          ...prevData,
          customerFeedback: customerFeedbackData.activitiesByFeedback
        }));
feedbackTypes: customerFeedbackData.feedbackTypes
feedbackTypes: customerFeedbackData.feedbackTypes
      } catch (error) {
        console.error('Error fetching customer feedback data:', error);
      } finally {
        setCustomerFeedbackLoading(false);
      }
    };

    fetchCustomerFeedback();
  }, []);
  // API Integration Functions (for future use)
  const fetchLeadGenerationData = async () => {
    try {
      const response = await instance.get('/dashboard/segment-head/leads');
      return response.data;
    } catch (error) {
      console.error('Error fetching lead generation data:', error);
      throw error;
    }
  };

  const fetchActivityPerformanceData = async () => {
    try {
      const response = await instance.get('/dashboard/segment-head/activities');
      return response.data;
    } catch (error) {
      console.error('Error fetching activity performance data:', error);
      throw error;
    }
  };

  const fetchHitlistTargetData = async () => {
    try {
      const response = await instance.get('/dashboard/segment-head/targets');
      return response.data;
    } catch (error) {
      console.error('Error fetching hitlist target data:', error);
      throw error;
    }
  };

  const fetchCustomerEngagementData = async () => {
    try {
      const response = await instance.get('/dashboard/segment-head/engagement');
      return response.data;
    } catch (error) {
      console.error('Error fetching customer engagement data:', error);
      throw error;
    }
  };

  const fetchEcosystemInsights = async () => {
    try {
      const response = await instance.get('/dashboard/segment-head/ecosystem');
      return response.data;
    } catch (error) {
      console.error('Error fetching ecosystem insights:', error);
      throw error;
    }
  };

  // Chart configurations
  const leadConversionChart = {
    series: [{
      name: 'Conversion Rate',
      data: dashboardData.conversionChart.conversionRates
    }],
    options: {
      chart: { type: 'line', toolbar: { show: false } },
      stroke: { curve: 'smooth', width: 3 },
      colors: ['#1C5B41'],
      xaxis: {
        categories: dashboardData.conversionChart.categories,
        labels: { style: { colors: '#6b7280' } }
      },
      yaxis: {
        title: { text: 'Conversion Rate (%)' },
        labels: { style: { colors: '#6b7280' } }
      },
      grid: { borderColor: '#e5e7eb' },
      tooltip: {
        theme: 'light',
        y: {
          formatter: function (val) {
            return val + '%';
          }
        }
      }
    }
  };

  // Function to get leads by status chart data
  const getLeadsByStatusChartData = () => {
    const statuses = Object.keys(dashboardData.leadsByStatus || {});
    const data = Object.values(dashboardData.leadsByStatus || {});
    const labels = statuses.map(status =>
      status.charAt(0).toUpperCase() + status.slice(1)
    );

    // Define colors for different statuses
    const statusColors = {
      'Hot': '#FF0000',
      'Warm': '#369DC9',
      'Cold': '#1C5B41',
      'Pending': '#FFB800'
    };

    const colors = labels.map(label => statusColors[label] || '#6B7280');

    return { labels, data, colors };
  };

  const activityCompletionChart = {
    series: getLeadsByStatusChartData().data,
    options: {
      chart: { type: 'pie' },
      labels: getLeadsByStatusChartData().labels,
      colors: getLeadsByStatusChartData().colors,
      legend: { position: 'bottom' },
      responsive: [{
        breakpoint: 480,
        options: {
          chart: { width: 200 },
          legend: { position: 'bottom' }
        }
      }]
    }
  };

  // Function to get leads by category chart data
  const getLeadsByCategoryChartData = () => {
    const categories = Object.keys(dashboardData.leadsByCategory || {});
    const data = Object.values(dashboardData.leadsByCategory || {});
    const labels = categories.map(cat =>
      cat.charAt(0).toUpperCase() + cat.slice(1)
    );

    return { categories: labels, data };
  };

  const leadsByCategoryChart = {
    series: [{
      name: 'Leads',
      data: getLeadsByCategoryChartData().data
    }],
    options: {
      chart: { type: 'bar', toolbar: { show: false } },
      plotOptions: { bar: { horizontal: false, columnWidth: '50%' } },
      xaxis: { categories: getLeadsByCategoryChartData().categories },
      colors: ['#1C5B41'],
      yaxis: { title: { text: 'Number of Leads' } }
    }
  };

  // Function to get customer feedback chart data
  const getCustomerFeedbackChartData = () => {
    // Predefined colors for feedback types
    const feedbackColors = ['#10B981', '#F59E0B', '#EF4444', '#3B82F6', '#8B5CF6', '#06B6D4', '#84CC16', '#F97316'];
    
    // Use dynamic feedback types from API if available, otherwise fallback to hardcoded types
    if (dashboardData.feedbackTypes && dashboardData.feedbackTypes.length > 0) {
      const data = dashboardData.feedbackTypes.map(type => type.count);
      const labels = dashboardData.feedbackTypes.map(type => type.name);
      const colors = dashboardData.feedbackTypes.map((_, index) => feedbackColors[index % feedbackColors.length]);
      return { data, labels, colors };
    } else {
      // Fallback to hardcoded types if no dynamic data is available
      const feedbackTypes = ['positive', 'neutral', 'negative'];
      const data = feedbackTypes.map(type => dashboardData.customerFeedback[type] || 0);
      const labels = feedbackTypes.map(type => type.charAt(0).toUpperCase() + type.slice(1));
      const colors = feedbackTypes.map((_, index) => feedbackColors[index % feedbackColors.length]);
      return { data, labels, colors };
    }
  };

  const customerFeedbackChart = {
    series: getCustomerFeedbackChartData().data,
    options: {
      chart: { type: 'donut' },
      labels: getCustomerFeedbackChartData().labels,
      colors: getCustomerFeedbackChartData().colors,
      legend: { position: 'bottom' }
    }
  };

  // Department Hitlist Charts
  const customerServiceHitlistChart = {
    series: Object.values(dashboardData.departmentHitlists.customerService.regions),
    options: {
      chart: { type: 'pie' },
      labels: Object.keys(dashboardData.departmentHitlists.customerService.regions),
      colors: ['#1C5B41', '#69AF57', '#3B82F6', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4', '#84CC16'],
      legend: { position: 'bottom', fontSize: '12px' },
      title: {
        text: `Total: ${dashboardData.departmentHitlists.customerService.total}`,
        align: 'center',
        style: { fontSize: '14px', fontWeight: 'bold', color: '#1f2937' }
      },
      responsive: [{
        breakpoint: 768,
        options: {
          chart: { width: 280 },
          legend: { position: 'bottom' }
        }
      }]
    }
  };

  const leadGenerationHitlistChart = {
    series: Object.values(dashboardData.departmentHitlists.leadGeneration.regions),
    options: {
      chart: { type: 'pie' },
      labels: Object.keys(dashboardData.departmentHitlists.leadGeneration.regions),
      colors: ['#1C5B41', '#69AF57', '#3B82F6', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4', '#84CC16'],
      legend: { position: 'bottom', fontSize: '12px' },
      title: {
        text: `Total: ${dashboardData.departmentHitlists.leadGeneration.total}`,
        align: 'center',
        style: { fontSize: '14px', fontWeight: 'bold', color: '#1f2937' }
      },
      responsive: [{
        breakpoint: 768,
        options: {
          chart: { width: 280 },
          legend: { position: 'bottom' }
        }
      }]
    }
  };

  const loanActivitiesHitlistChart = {
    series: Object.values(dashboardData.departmentHitlists.loanActivities.regions),
    options: {
      chart: { type: 'pie' },
      labels: Object.keys(dashboardData.departmentHitlists.loanActivities.regions),
      colors: ['#1C5B41', '#69AF57', '#3B82F6', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4', '#84CC16'],
      legend: { position: 'bottom', fontSize: '12px' },
      title: {
        text: `Total: ${dashboardData.departmentHitlists.loanActivities.total}`,
        align: 'center',
        style: { fontSize: '14px', fontWeight: 'bold', color: '#1f2937' }
      },
      responsive: [{
        breakpoint: 768,
        options: {
          chart: { width: 280 },
          legend: { position: 'bottom' }
        }
      }]
    }
  };

  return (
    <div className="container mx-auto p-4 space-y-6" ref={dashboardRef}>
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Segment Head Dashboard</h1>

      </div>

      {/* Key Performance Indicators */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {statisticsLoading ? (
          // Loading skeleton for KPI cards
          <>
            {[1, 2, 3, 4].map((item) => (
              <div key={item} className="bg-white p-6 rounded-lg shadow animate-pulse">
                <div className="flex items-center justify-between mb-4">
                  <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                  <div className="h-6 w-6 bg-gray-300 rounded"></div>
                </div>
                <div className="h-8 bg-gray-300 rounded w-1/2 mb-2"></div>
                <div className="h-3 bg-gray-300 rounded w-full"></div>
              </div>
            ))}
          </>
        ) : (
          // Actual KPI cards with data
          <>
            <DashboardCard
              title="Total Leads (MTD)"
              value={dashboardData.totalLeads.mtd}
              description="Month-to-date lead generation"
              icon={<UserGroupIcon className="w-6 h-6" />}
              iconColor="#1C5B41"
              duration={2000}
              delay={shouldAnimate ? 0 : 0}
            />
            <DashboardCard
              title="Conversion Rate"
              value={dashboardData.totalLeads.conversionRate }
              description="Lead to customer conversion"
              icon={<IoIosTrendingUp className="w-6 h-6" />}
              iconColor="#69AF57"
              duration={2000}
              delay={shouldAnimate ? 200 : 0}
            />
            <DashboardCard
              title="CONVERTED LEADS (MTD)"
              value={dashboardData.totalLeads.convertedLeadsMtd}
              description="Converted leads this month"
              icon={<ChartBarIcon className="w-6 h-6" />}
              iconColor="#1C5B41"
              duration={2000}
              delay={shouldAnimate ? 400 : 0}
            />
            <DashboardCard
              title="Hitlist Size"
              value={dashboardData.hitlistSize}
              description="Bank-wide prospects"
              icon={<BuildingOfficeIcon className="w-6 h-6" />}
              iconColor="#69AF57"
              duration={2000}
              delay={shouldAnimate ? 600 : 0}
            />
          </>
        )}
      </div>

      {/* Lead Generation Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Lead Conversion Trend</h3>
            <div className="flex items-center space-x-2">
              <select
                value={conversionChartYear}
                onChange={(e) => setConversionChartYear(parseInt(e.target.value))}
                className="border border-gray-300 rounded-md px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-green-500"
              >
                {yearOptions.map(year => (
                  <option key={year} value={year}>{year}</option>
                ))}
              </select>
            </div>
          </div>
          {conversionChartLoading ? (
            <div className="animate-pulse h-64 flex items-center justify-center">
              <div className="h-4 bg-gray-300 rounded w-3/4"></div>
            </div>
          ) : (
            <Chart
              options={leadConversionChart.options}
              series={leadConversionChart.series}
              type="line"
              height={300}
            />
          )}
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Leads by Category</h3>
          {chartsLoading ? (
            <div className="animate-pulse">
              <div className="h-64 bg-gray-300 rounded"></div>
            </div>
          ) : (
            <Chart
              options={leadsByCategoryChart.options}
              series={leadsByCategoryChart.series}
              type="bar"
              height={300}
            />
          )}
        </div>
      </div>

      {/* Activity Performance */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Leads Status</h3>
          {chartsLoading ? (
            <div className="animate-pulse">
              <div className="h-64 bg-gray-300 rounded"></div>
            </div>
          ) : (
            <Chart
              options={activityCompletionChart.options}
              series={activityCompletionChart.series}
              type="pie"
              height={300}
            />
          )}
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Customer Feedback Trends</h3>
          {customerFeedbackLoading ? (
            <div className="animate-pulse">
              <div className="h-64 bg-gray-300 rounded"></div>
            </div>
          ) : (
            <Chart
              options={customerFeedbackChart.options}
              series={customerFeedbackChart.series}
              type="donut"
              height={300}
            />
          )}
        </div>
      </div>

      {/* Target Achievement and Performance Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Calls vs Target (MTD)</h3>
          {callVisitStatsLoading ? (
            <div className="space-y-4 animate-pulse">
              <div className="flex justify-between items-center">
                <div className="h-4 bg-gray-300 rounded w-16"></div>
                <div className="h-4 bg-gray-300 rounded w-20"></div>
              </div>
              <div className="flex justify-between items-center">
                <div className="h-4 bg-gray-300 rounded w-16"></div>
                <div className="h-4 bg-gray-300 rounded w-20"></div>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="h-2 bg-gray-300 rounded-full w-3/4"></div>
              </div>
              <div className="text-center">
                <div className="h-4 bg-gray-300 rounded w-24 mx-auto"></div>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Made</span>
                <span className="font-semibold">{dashboardData.callsVsTarget.made.toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Target</span>
                <span className="font-semibold">{dashboardData.callsVsTarget.target.toLocaleString()}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-green-600 h-2 rounded-full"
                  style={{
                    width: `${dashboardData.callsVsTarget.target > 0
                      ? Math.min((dashboardData.callsVsTarget.made / dashboardData.callsVsTarget.target) * 100, 100)
                      : 0}%`
                  }}
                ></div>
              </div>
              <div className="text-center text-sm text-gray-600">
                {dashboardData.callsVsTarget.target > 0
                  ? Math.round((dashboardData.callsVsTarget.made / dashboardData.callsVsTarget.target) * 100)
                  : 0}% Achievement
              </div>
            </div>
          )}
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Visits vs Target (MTD)</h3>
          {callVisitStatsLoading ? (
            <div className="space-y-4 animate-pulse">
              <div className="flex justify-between items-center">
                <div className="h-4 bg-gray-300 rounded w-16"></div>
                <div className="h-4 bg-gray-300 rounded w-20"></div>
              </div>
              <div className="flex justify-between items-center">
                <div className="h-4 bg-gray-300 rounded w-16"></div>
                <div className="h-4 bg-gray-300 rounded w-20"></div>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="h-2 bg-gray-300 rounded-full w-3/4"></div>
              </div>
              <div className="text-center">
                <div className="h-4 bg-gray-300 rounded w-24 mx-auto"></div>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Made</span>
                <span className="font-semibold">{dashboardData.visitsVsTarget.made.toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Target</span>
                <span className="font-semibold">{dashboardData.visitsVsTarget.target.toLocaleString()}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full"
                  style={{
                    width: `${dashboardData.visitsVsTarget.target > 0
                      ? Math.min((dashboardData.visitsVsTarget.made / dashboardData.visitsVsTarget.target) * 100, 100)
                      : 0}%`
                  }}
                ></div>
              </div>
              <div className="text-center text-sm text-gray-600">
                {dashboardData.visitsVsTarget.target > 0
                  ? Math.round((dashboardData.visitsVsTarget.made / dashboardData.visitsVsTarget.target) * 100)
                  : 0}% Achievement
              </div>
            </div>
          )}
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Activity Status</h3>
          <div className="space-y-5">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <ExclamationTriangleIcon className="w-5 h-5 text-red-500 mr-2" />
                <span className="text-gray-600">Overdue</span>
              </div>
              <span className="font-semibold text-red-600">{dashboardData.overdueActivities}</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <ClockIcon className="w-5 h-5 text-blue-500 mr-2" />
                <span className="text-gray-600">Upcoming</span>
              </div>
              <span className="font-semibold text-blue-600">{dashboardData.upcomingActivities}</span>
            </div>
          </div>
        </div>
      </div>

      {/*/!* Department Hitlists by Region *!/*/}
      {/*<div className="bg-white p-6 rounded-lg shadow">*/}
      {/*  <h3 className="text-lg font-semibold text-gray-900 mb-6">Department Hitlists by Region</h3>*/}
      {/*  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">*/}
      {/*    <div className="text-center">*/}
      {/*      <h4 className="text-md font-medium text-gray-700 mb-4">Customer Service</h4>*/}
      {/*      <Chart*/}
      {/*        options={customerServiceHitlistChart.options}*/}
      {/*        series={customerServiceHitlistChart.series}*/}
      {/*        type="pie"*/}
      {/*        height={300}*/}
      {/*      />*/}
      {/*    </div>*/}
      {/*    <div className="text-center">*/}
      {/*      <h4 className="text-md font-medium text-gray-700 mb-4">Lead Generation</h4>*/}
      {/*      <Chart*/}
      {/*        options={leadGenerationHitlistChart.options}*/}
      {/*        series={leadGenerationHitlistChart.series}*/}
      {/*        type="pie"*/}
      {/*        height={300}*/}
      {/*      />*/}
      {/*    </div>*/}
      {/*    <div className="text-center">*/}
      {/*      <h4 className="text-md font-medium text-gray-700 mb-4">Loan Activities</h4>*/}
      {/*      <Chart*/}
      {/*        options={loanActivitiesHitlistChart.options}*/}
      {/*        series={loanActivitiesHitlistChart.series}*/}
      {/*        type="pie"*/}
      {/*        height={300}*/}
      {/*      />*/}
      {/*    </div>*/}
      {/*  </div>*/}
      {/*</div>*/}

      {/* Top Performing Anchors */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Performing Anchors</h3>
        {topAnchorsLoading ? (
          <div className="space-y-3">
            {[1, 2, 3].map((item) => (
              <div key={item} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg animate-pulse">
                <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                <div className="h-4 bg-gray-300 rounded w-16"></div>
              </div>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
            {dashboardData.topAnchors.map((anchor, index) => (
              <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <span className="font-medium text-gray-900">{anchor.name}</span>
                <span className="text-green-600 font-semibold">{anchor.leads} leads</span>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Activities Table */}
      <SegmentHeadActivitiesTable />
    </div>
  );
}

export default SegmentHeadDashboard;
