import React, { useRef, useEffect, useState } from "react";
import DashboardCard from "../cards/DashboardCard";
import ActivitiesTable from "../tables/ActivitiesTable";
import BranchHitlistConversionChart from "../charts/BranchHitlistConversionChart";
import BranchConversionTrendChart from "../charts/BranchConversionTrendChart";
import BranchCallsVisitsChart from "../charts/BranchCallsVisitsChart";
import BranchDormantStatusChart from "../charts/BranchDormantStatusChart";
import MonthlyHitlistProgressChart from "../charts/MonthlyHitlistProgressChart";
import CallsVisitsVsTargetsChart from "../charts/CallsVisitsVsTargetsChart";
import MonthlyCallsVsTargetsPerOfficerChart from "../charts/MonthlyCallsVsTargetsPerOfficerChart";
import MonthlyVisitsVsTargetsPerOfficerChart from "../charts/MonthlyVisitsVsTargetsPerOfficerChart";
import useCountUp from "../../hooks/useCountUp";
import ChartSkeleton from "../skeletons/ChartSkeleton";
import { branchManagerDashboardService } from "../../services/branchManagerDashboardService";

function BranchManagerDashboard() {
  // Animation state for Activities Overview
  const [hasAnimatedActivities, setHasAnimatedActivities] = useState(false);
  const [shouldAnimateActivities, setShouldAnimateActivities] = useState(false);
  const activitiesRef = useRef(null);

  // Dashboard data state
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        const data = await branchManagerDashboardService.getAllData();
        setDashboardData(data);
        setError(null);
      } catch (err) {
        console.error("Error fetching dashboard data:", err);
        setError("Failed to load dashboard data");
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // Extract summary cards data from API response
  const summaryCards = dashboardData?.firstSection?.summary_cards || {
    hitlist_size: { all: 0, this_month: 0 },
    converted_customers: { all: 0, this_month: 0 },
    calls_completed: { all: 0, this_month: 0 },
    visits: { all: 0, this_month: 0 },
  };

  // Activities data for animation
  const upcomingCount = 2;
  const scheduledCount = 3;
  const overdueCount = 3;

  // Intersection Observer for Activities Overview
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !hasAnimatedActivities) {
            setShouldAnimateActivities(true);
            setHasAnimatedActivities(true);
          }
        });
      },
      { threshold: 0.3 }
    );

    if (activitiesRef.current) {
      observer.observe(activitiesRef.current);
    }

    return () => {
      if (activitiesRef.current) {
        observer.unobserve(activitiesRef.current);
      }
    };
  }, [hasAnimatedActivities]);

  // Animated values for Activities Overview
  const animatedUpcoming = useCountUp(
    shouldAnimateActivities ? upcomingCount : 0,
    1000,
    0
  );
  const animatedScheduled = useCountUp(
    shouldAnimateActivities ? scheduledCount : 0,
    1000,
    150
  );
  const animatedOverdue = useCountUp(
    shouldAnimateActivities ? overdueCount : 0,
    1000,
    300
  );

  // Icon components (original)
  const HitlistIcon = (
    <svg
      className="w-7 h-7 text-[#F47976]"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
      />
    </svg>
  );

  // New icon components (regional manager style)
  const BranchHitlistIcon = (
    <svg
      className="w-7 h-7 text-[#F47976]"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
      />
    </svg>
  );

  const ConvertedIcon = (
    <svg
      className="w-7 h-7 text-[#1c5b41]"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
      />
    </svg>
  );

  const CallsIcon = (
    <svg
      className="w-7 h-7 text-[#3B82F6]"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
      />
    </svg>
  );

  const VisitsIcon = (
    <svg
      className="w-7 h-7 text-[#FDCA37]"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
      />
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
      />
    </svg>
  );

  // Trend icon component
  const TrendIcon = (
    <svg
      className="w-4 h-4 text-green-600"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M7 17l9.2-9.2M17 17V7H7"
      />
    </svg>
  );

  const ActivitiesIcon = (
    <svg
      className="w-7 h-7 text-[#1c5b41]"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
      />
    </svg>
  );

  const OverdueIcon = (
    <svg
      className="w-7 h-7 text-[#FDCA37]"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
      />
    </svg>
  );

  // Loading state
  if (loading) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-medium mb-6">Branch Manager Dashboard</h1>

        {/* Dashboard Cards Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {[1, 2, 3, 4].map((item) => (
            <div
              key={item}
              className="bg-white p-6 rounded-lg shadow animate-pulse"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                <div className="h-6 w-6 bg-gray-300 rounded"></div>
              </div>
              <div className="h-8 bg-gray-300 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-300 rounded w-full"></div>
            </div>
          ))}
        </div>

        {/* Charts Skeleton */}
        <div className="mb-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-1">
              <ChartSkeleton
                chartType="pie"
                title="Hitlist to Conversion Overview"
              />
            </div>
            <div className="lg:col-span-2">
              <ChartSkeleton chartType="line" title="Branch Conversion Trend" />
            </div>
          </div>
        </div>

        <div className="mb-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <ChartSkeleton chartType="bar" title="Branch Calls & Visits" />
            <ChartSkeleton chartType="pie" title="Branch Dormant Status" />
          </div>
        </div>

        <div className="mb-8">
          <ChartSkeleton chartType="bar" title="Monthly Hitlist Progress" />
        </div>

        <div className="mb-8">
          <ChartSkeleton chartType="bar" title="Calls & Visits vs Targets" />
        </div>

        <div className="mb-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <ChartSkeleton
              chartType="bar"
              title="Monthly Calls vs Targets per Officer"
            />
            <ChartSkeleton
              chartType="bar"
              title="Monthly Visits vs Targets per Officer"
            />
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-medium mb-6">Branch Manager Dashboard</h1>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg text-red-600">{error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <h1 className="text-2xl font-medium mb-6">Branch Manager Dashboard</h1>

      {/* Summary Cards Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <DashboardCard
          title="Branch Lead Hitlist Size"
          value={summaryCards.hitlist_size.all}
          icon={BranchHitlistIcon}
          iconColor="#F47976"
          duration={2000}
          delay={200}
          trend={summaryCards.hitlist_size.this_month.toString()}
          trendIcon={TrendIcon}
        />

        <DashboardCard
          title="Total Converted Customers"
          value={summaryCards.converted_customers.all}
          icon={ConvertedIcon}
          iconColor="#1c5b41"
          duration={1500}
          delay={400}
          trend={summaryCards.converted_customers.this_month.toString()}
          trendIcon={TrendIcon}
        />

        <DashboardCard
          title="Total Calls Completed"
          value={summaryCards.calls_completed.all}
          icon={CallsIcon}
          iconColor="#3B82F6"
          duration={1000}
          delay={600}
          trend={summaryCards.calls_completed.this_month.toString()}
          trendIcon={TrendIcon}
        />

        <DashboardCard
          title="Total Visits Made"
          value={summaryCards.visits.all}
          icon={VisitsIcon}
          iconColor="#FDCA37"
          duration={800}
          delay={800}
          trend={summaryCards.visits.this_month.toString()}
          trendIcon={TrendIcon}
        />
      </div>

      {/* Hitlist Conversion and Branch Trend Section */}
      <div className="mb-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Hitlist to Conversion Overview - 1/3 width */}
          <div className="lg:col-span-1">
            <BranchHitlistConversionChart data={dashboardData?.firstSection} />
          </div>

          {/* Branch Conversion Trend - 2/3 width */}
          <div className="lg:col-span-2">
            <BranchConversionTrendChart data={dashboardData?.firstSection} />
          </div>
        </div>
      </div>

      {/* Branch Calls & Visits and Dormant Status Section */}
      <div className="mb-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Branch Calls & Visits - 1/2 width */}
          <div className="lg:col-span-1">
            <BranchCallsVisitsChart data={dashboardData?.firstSection} />
          </div>

          {/* Branch Dormant Status - 1/2 width */}
          <div className="lg:col-span-1">
            <BranchDormantStatusChart data={dashboardData?.firstSection} />
          </div>
        </div>
      </div>

      {/* Monthly Hitlist Progress and Calls & Visits vs Targets Section */}
      <div className="mb-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Monthly Hitlist Progress - 1/2 width */}
          <div className="lg:col-span-1">
            <MonthlyHitlistProgressChart data={dashboardData?.secondSection} />
          </div>

          {/* Calls & Visits vs Targets - 1/2 width */}
          <div className="lg:col-span-1">
            <CallsVisitsVsTargetsChart data={dashboardData?.secondSection} />
          </div>
        </div>
      </div>

      {/* Monthly Targets per Officer Section */}
      <div className="mb-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Monthly Calls vs Targets per Officer - 1/2 width */}
          <div className="lg:col-span-1">
            <MonthlyCallsVsTargetsPerOfficerChart
              data={dashboardData?.secondSection}
            />
          </div>

          {/* Monthly Visits vs Targets per Officer - 1/2 width */}
          <div className="lg:col-span-1">
            <MonthlyVisitsVsTargetsPerOfficerChart
              data={dashboardData?.secondSection}
            />
          </div>
        </div>
      </div>

      {/* Activities Statistics Section */}
      <div
        ref={activitiesRef}
        className="bg-white rounded-xl shadow-lg p-6 mb-8"
      >
        <div className="mb-6">
          <h3 className="text-[23px] font-semibold text-gray-800 mb-2">
            MONTHLY ACTIVITIES OVERVIEW
          </h3>
          <p className="text-sm text-gray-600" style={{ fontSize: "15px" }}>
            Summary of upcoming, scheduled and overdue activities
          </p>
        </div>

        {/* Stats Summary */}
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold" style={{ color: "#1E3A8A" }}>
              {animatedUpcoming}
            </div>
            <div className="text-sm text-gray-600">Upcoming</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold" style={{ color: "#10b981" }}>
              {animatedScheduled}
            </div>
            <div className="text-sm text-gray-600">Scheduled</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold" style={{ color: "#ef4444" }}>
              {animatedOverdue}
            </div>
            <div className="text-sm text-gray-600">Overdue</div>
          </div>
        </div>
      </div>

      {/* Activities Table Section */}
      <ActivitiesTable />
    </div>
  );
}

export default BranchManagerDashboard;
