import React, { useState } from "react";
import DashboardCard from "../cards/DashboardCard";
import RegionalHitlistChart from "../charts/RegionalHitlistChart";
import DepartmentalPerformanceChart from "../charts/DepartmentalPerformanceChart";
import RegionalConversionTrendChart from "../charts/RegionalConversionTrendChart";
import RegionalDormantPieChart from "../charts/RegionalDormantPieChart";
import DormantCustomersBarChart from "../charts/DormantCustomersBarChart";
import ChartSkeleton from "../skeletons/ChartSkeleton";

function RegionalManagerDashboard() {
  // Dashboard data variables with trend indicators
  const regionalHitlistSize = 2847;
  const hitlistTrend = "245";

  const totalConvertedCustomers = 1456;
  const convertedTrend = "189";

  const totalCallsCompleted = 8934;
  const callsTrend = "567";

  const totalVisitsMade = 2341;
  const visitsTrend = "123";

  // Icon components
  const HitlistIcon = (
    <svg
      className="w-7 h-7 text-[#F47976]"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
      />
    </svg>
  );

  const ConvertedIcon = (
    <svg
      className="w-7 h-7 text-[#1c5b41]"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
      />
    </svg>
  );

  const CallsIcon = (
    <svg
      className="w-7 h-7 text-[#3B82F6]"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
      />
    </svg>
  );

  const VisitsIcon = (
    <svg
      className="w-7 h-7 text-[#FDCA37]"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
      />
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
      />
    </svg>
  );

  // Trend icon component
  const TrendIcon = (
    <svg
      className="w-4 h-4 text-green-600"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M7 17l9.2-9.2M17 17V7H7"
      />
    </svg>
  );

  return (
    <div className="p-6">
      <h1 className="text-2xl font-medium mb-6">Regional Manager Dashboard</h1>

      {/* Top Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <DashboardCard
          title="Regional Lead Hitlist Size"
          value={regionalHitlistSize}
          icon={HitlistIcon}
          iconColor="#F47976"
          duration={2000}
          delay={200}
          trend={hitlistTrend}
          trendIcon={TrendIcon}
        />

        <DashboardCard
          title="Total Converted Customers"
          value={totalConvertedCustomers}
          icon={ConvertedIcon}
          iconColor="#1c5b41"
          duration={1500}
          delay={400}
          trend={convertedTrend}
          trendIcon={TrendIcon}
        />

        <DashboardCard
          title="Total Calls Completed"
          value={totalCallsCompleted}
          icon={CallsIcon}
          iconColor="#3B82F6"
          duration={1000}
          delay={600}
          trend={callsTrend}
          trendIcon={TrendIcon}
        />

        <DashboardCard
          title="Total Visits Made"
          value={totalVisitsMade}
          icon={VisitsIcon}
          iconColor="#FDCA37"
          duration={800}
          delay={800}
          trend={visitsTrend}
          trendIcon={TrendIcon}
        />
      </div>

      {/* Regional Hitlist Chart */}
      <div className="mb-8">
        <RegionalHitlistChart />
      </div>

      {/* Regional Conversion Trend Chart */}
      <div className="mb-8">
        <RegionalConversionTrendChart />
      </div>

      {/* Departmental Performance and Dormant Pie Chart */}
      <div className="mb-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Departmental Performance Chart - 2/3 width */}
          <div className="lg:col-span-2">
            <DepartmentalPerformanceChart />
          </div>

          {/* Regional Dormant Pie Chart - 1/3 width */}
          <div className="lg:col-span-1">
            <RegionalDormantPieChart />
          </div>
        </div>
      </div>

      {/* Dormant Customers Bar Chart */}
      <div className="mb-8">
        <DormantCustomersBarChart />
      </div>
    </div>
  );
}

export default RegionalManagerDashboard;
