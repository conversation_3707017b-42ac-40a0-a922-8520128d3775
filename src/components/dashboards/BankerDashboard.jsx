import React, { useState, useEffect } from "react";
import Chart from "react-apexcharts";
import {
  UserGroupIcon,
  PhoneIcon,
  CalendarIcon,
  CheckCircleIcon,
} from "@heroicons/react/24/outline";
import ActivitiesTable from "../tables/ActivitiesTable";
import { bankerDashboardService } from "../../services/bankerDashboardService";
import { useAuth } from "../../contexts/AuthContext";
import {
  getUserData,
  getAccessToken,
  isAuthenticated,
} from "../../utils/cookieUtils";
import { toast } from "react-toastify";

/*
  SummaryCard Component: displays a KPI with a title, value, and icon.
  Props:
    - title: label of the metric
    - value: numeric value to display
    - icon: SVG icon component
    - iconColor: Tailwind text color for the icon
    - bgColor: Tailwind background color for the card
*/
const SummaryCard = ({ title, value, icon: Icon, iconColor, bgColor }) => (
  <div
    className={`flex items-center p-4 rounded-lg shadow ${bgColor} min-h-[7rem]`}
  >
    <div className={`p-2 rounded-full ${iconColor} bg-grey bg-opacity-30`}>
      <Icon className="w-6 h-6" />{" "}
      {/* slightly increased icon size for balance */}
    </div>
    <div className="ml-4">
      <p className="text-sm font-medium text-gray-600">{title}</p>
      <p className="mt-1 text-xl font-semibold text-gray-900">{value}</p>
    </div>
  </div>
);

/*
  Dashboard Component: main component assembling the summary cards, charts, and lists.
*/
const BankerDashboard = () => {
  // Get auth context
  const { user, isLoggedIn } = useAuth();

  // Dashboard data state
  const [data, setData] = useState({
    hitlistSize: 0,
    calls: { made: 0, target: 100 },
    visits: { made: 0, target: 80 },
    contacted: 0,

    // Lead Status Data (using API status categories)
    leadsByCategory: {
      pending: 0,
      warm: 0,
      hot: 0,
      cold: 0,
    },

    // Hitlist Progress Data
    hitlistProgress: {
      total: 0,
      contacted: 0,
      remaining: 0,
    },

    // Additional metadata
    monthYear: "",
    userPermissions: {},
    filtersApplied: {},
  });

  // Loading and error states
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Fetch dashboard data from API
  const fetchDashboardData = async (filters = {}) => {
    try {
      setLoading(true);
      setError(null);

      console.log("=== BANKER DASHBOARD: Fetching data ===");
      console.log("Auth user:", user);
      console.log("Is logged in:", isLoggedIn);

      // Check authentication status
      const accessToken = getAccessToken();
      const authenticated = isAuthenticated();
      console.log("Access token exists:", !!accessToken);
      console.log("Is authenticated (cookie):", authenticated);
      console.log(
        "Access token preview:",
        accessToken ? accessToken.substring(0, 20) + "..." : "null"
      );

      // Get user data from cookies (where the actual user data is stored)
      const cookieUserData = getUserData();
      console.log("Cookie user data:", cookieUserData);

      // Set user context from cookie data if available
      if (cookieUserData && isLoggedIn) {
        // Extract user ID and branch ID from the cookie user object
        const userId =
          cookieUserData.id ||
          cookieUserData.user_id ||
          cookieUserData.userId ||
          cookieUserData.uuid;
        const branchId =
          cookieUserData.branch_id ||
          cookieUserData.branchId ||
          cookieUserData.branch?.id ||
          cookieUserData.branchUuid;

        console.log("Setting user context from cookie data:", {
          userId,
          branchId,
        });

        if (userId) {
          bankerDashboardService.setUserContext(userId, branchId);
        }
      } else if (user && isLoggedIn) {
        // Fallback to AuthContext user data
        const userId = user.id || user.user_id || user.userId;
        const branchId = user.branch_id || user.branchId || user.branch?.id;

        console.log("Setting user context from auth context:", {
          userId,
          branchId,
        });

        if (userId) {
          bankerDashboardService.setUserContext(userId, branchId);
        }
      }

      // Check if we have user context
      const userContext = bankerDashboardService.getUserContext();
      console.log("Final user context:", userContext);

      if (!userContext.userId && !userContext.branchId) {
        console.warn("No user context found, API may require authentication");
      }

      // Check if we have authentication before making API call
      if (!authenticated) {
        console.warn(
          "No access token found, user may not be properly authenticated"
        );
        throw new Error("Authentication required - no access token found");
      }

      const dashboardData = await bankerDashboardService.getDashboardAnalytics(
        filters
      );

      setData(dashboardData);
      console.log("Dashboard data loaded successfully:", dashboardData);
    } catch (error) {
      console.error("Error loading dashboard data:", error);

      // Check if it's a UUID validation error
      if (
        error.response?.status === 400 &&
        error.response?.data?.message?.includes("uuid is expected")
      ) {
        setError("Demo mode: User authentication required for live data");
        // Use mock data for demo
        const mockData = {
          hitlistSize: 150,
          calls: { made: 25, target: 100 },
          visits: { made: 12, target: 80 },
          contacted: 85,
          leadsByCategory: { pending: 45, warm: 30, hot: 25, cold: 50 },
          hitlistProgress: { total: 150, contacted: 85, remaining: 65 },
          monthYear: "2025-08",
          userPermissions: { can_view_all_leads: true },
          filtersApplied: {},
        };
        setData(mockData);
      } else {
        setError(error.message || "Failed to load dashboard data");
        // Set empty fallback data on other errors
        setData({
          hitlistSize: 0,
          calls: { made: 0, target: 100 },
          visits: { made: 0, target: 80 },
          contacted: 0,
          leadsByCategory: { pending: 0, warm: 0, hot: 0, cold: 0 },
          hitlistProgress: { total: 0, contacted: 0, remaining: 0 },
          monthYear: "",
          userPermissions: {},
          filtersApplied: {},
        });
      }
    } finally {
      setLoading(false);
    }
  };

  // Refresh dashboard data
  const refreshDashboard = () => {
    setRefreshTrigger((prev) => prev + 1);
  };

  // Load dashboard data on component mount and when refresh is triggered
  useEffect(() => {
    // Only fetch data if user is logged in and authenticated
    if (isLoggedIn && isAuthenticated()) {
      fetchDashboardData();
    } else {
      console.warn("User not authenticated, skipping dashboard data fetch");
      setLoading(false);
    }
  }, []);

  // API Integration Functions for specific data (for future use)
  const fetchLeadStatusData = async (filters = {}) => {
    try {
      return await bankerDashboardService.getLeadStatusData(filters);
    } catch (error) {
      console.error("Error fetching lead status data:", error);
      throw error;
    }
  };

  const fetchHitlistProgressData = async (filters = {}) => {
    try {
      return await bankerDashboardService.getHitlistProgressData(filters);
    } catch (error) {
      console.error("Error fetching hitlist progress data:", error);
      throw error;
    }
  };

  // Chart configuration for Calls vs Target (bar chart)
  const callsChart = {
    series: [
      { name: "Made", data: [data.calls.made] },
      { name: "Target", data: [data.calls.target] },
    ],
    options: {
      chart: { type: "bar", toolbar: { show: false } },
      plotOptions: { bar: { horizontal: false, columnWidth: "50%" } },
      xaxis: { categories: ["Calls MTD"] },
      colors: ["#69AF57", "#1C5B41"],
      legend: { position: "top" },
      yaxis: { title: { text: "Number" } },
    },
  };

  // Chart configuration for Visits vs Target (bar chart)
  const visitsChart = {
    series: [
      { name: "Made", data: [data.visits.made] },
      { name: "Target", data: [data.visits.target] },
    ],
    options: {
      chart: { type: "bar", toolbar: { show: false } },
      plotOptions: { bar: { horizontal: false, columnWidth: "50%" } },
      xaxis: { categories: ["Visits MTD"] },
      colors: ["#69AF57", "#1C5B41"],
      legend: { position: "top" },
      yaxis: { title: { text: "Number" } },
    },
  };

  // Lead Status Pie Chart (using API status categories)
  const leadStatusChart = {
    series: [
      data.leadsByCategory.pending,
      data.leadsByCategory.warm,
      data.leadsByCategory.hot,
      data.leadsByCategory.cold,
    ],
    options: {
      chart: { type: "pie" },
      labels: ["Pending", "Warm", "Hot", "Cold"],
      colors: ["#F59E0B", "#10B981", "#EF4444", "#6B7280"], // Orange, Green, Red, Gray
      legend: {
        position: "bottom",
        fontSize: "14px",
        fontWeight: 500,
      },
      title: {
        text: "Lead Status Distribution",
        align: "center",
        style: { fontSize: "16px", fontWeight: "bold", color: "#1f2937" },
      },
      responsive: [
        {
          breakpoint: 768,
          options: {
            chart: { width: 280 },
            legend: { position: "bottom" },
          },
        },
      ],
      tooltip: {
        y: {
          formatter: function (val) {
            return val + " leads";
          },
        },
      },
    },
  };

  // Hitlist Progress Donut Chart (similar to BranchManagerDashboard)
  const hitlistProgressChart = {
    series: [
      (data.hitlistProgress.contacted / data.hitlistProgress.total) * 100,
      (data.hitlistProgress.remaining / data.hitlistProgress.total) * 100,
    ],
    options: {
      chart: {
        type: "donut",
        height: 300,
        animations: {
          enabled: true,
          easing: "easeinout",
          speed: 800,
          animateGradually: {
            enabled: true,
            delay: 50,
          },
        },
      },
      plotOptions: {
        pie: {
          donut: {
            size: "70%",
            labels: {
              show: true,
              name: {
                show: true,
                fontSize: "14px",
                fontWeight: 600,
                color: "#374151",
                offsetY: -10,
              },
              value: {
                show: true,
                fontSize: "20px",
                fontWeight: 700,
                color: "#1f2937",
                offsetY: 10,
                formatter: function (val) {
                  return val.toFixed(1) + "%";
                },
              },
              total: {
                show: true,
                showAlways: true,
                label: "Contacted",
                fontSize: "12px",
                fontWeight: 600,
                color: "#6b7280",
                formatter: function (w) {
                  const percentage = (
                    (data.hitlistProgress.contacted /
                      data.hitlistProgress.total) *
                    100
                  ).toFixed(1);
                  return percentage + "%";
                },
              },
            },
          },
        },
      },
      colors: ["#69AF57", "#e5e7eb"], // Green for contacted, light gray for remaining
      labels: ["Contacted", "Remaining"],
      dataLabels: {
        enabled: false,
      },
      legend: {
        show: true,
        position: "bottom",
        horizontalAlign: "center",
        fontSize: "14px",
        fontWeight: 500,
        markers: {
          width: 12,
          height: 12,
          radius: 6,
        },
        itemMargin: {
          horizontal: 15,
          vertical: 5,
        },
      },
      title: {
        text: "Hitlist Progress",
        align: "center",
        style: { fontSize: "16px", fontWeight: "bold", color: "#1f2937" },
      },
      tooltip: {
        enabled: true,
        custom: function ({ series, seriesIndex }) {
          const values = [
            data.hitlistProgress.contacted,
            data.hitlistProgress.remaining,
          ];
          const labels = ["Contacted", "Remaining"];
          const colors = ["#69AF57", "#6b7280"];

          return `
            <div style="padding: 8px 12px; background: white; border-radius: 4px; box-shadow: 0 2px 8px rgba(0,0,0,0.15); font-size: 14px;">
              <span style="color: ${colors[seriesIndex]};">
                ${labels[seriesIndex]}: ${
            values[seriesIndex]
          } prospects (${series[seriesIndex].toFixed(1)}%)
              </span>
            </div>
          `;
        },
      },
      stroke: {
        show: false,
      },
      responsive: [
        {
          breakpoint: 768,
          options: {
            chart: {
              height: 250,
            },
            plotOptions: {
              pie: {
                donut: {
                  size: "65%",
                },
              },
            },
          },
        },
      ],
    },
  };

  // Loading state
  if (loading) {
    return (
      <div className="container mx-auto p-4 space-y-6">
        <div className="text-center py-8">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
          <div className="mt-4 text-lg text-gray-600">
            Loading dashboard data...
          </div>
        </div>
      </div>
    );
  }

  // Error state (but still show dashboard with demo data if available)
  const isDemoMode = error && error.includes("Demo mode");

  if (error && !isDemoMode) {
    return (
      <div className="container mx-auto p-4 space-y-6">
        <div className="text-center py-8">
          <div className="text-red-600 text-lg mb-4">
            Error loading dashboard
          </div>
          <div className="text-gray-600 mb-4">{error}</div>
          <button
            onClick={refreshDashboard}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 space-y-6">
      {/* Dashboard Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <div className="flex items-center gap-3">
            <h1 className="text-2xl font-bold text-gray-900">
              Banker Dashboard
            </h1>
            {isDemoMode && (
              <span className="px-2 py-1 bg-orange-100 text-orange-800 text-xs font-medium rounded-full">
                Demo Mode
              </span>
            )}
          </div>
          {/*<p className="text-gray-600">*/}
          {/*  {isDemoMode*/}
          {/*    ? 'Showing demo data - login required for live data'*/}
          {/*    : data.monthYear*/}
          {/*      ? `Data for ${data.monthYear}`*/}
          {/*      : 'Personal performance overview'*/}
          {/*  }*/}
          {/*</p>*/}
        </div>
        {/*<button*/}
        {/*  onClick={refreshDashboard}*/}
        {/*  disabled={loading}*/}
        {/*  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"*/}
        {/*>*/}
        {/*  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">*/}
        {/*    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />*/}
        {/*  </svg>*/}
        {/*  Refresh*/}
        {/*</button>*/}
      </div>

      {/* Summary Widgets */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <SummaryCard
          title="Hitlist Size"
          value={data.hitlistSize}
          icon={UserGroupIcon}
          iconColor="text-[#69AF57]"
          bgColor="bg-white"
        />
        <SummaryCard
          title="Calls Made (MTD)"
          // value={`${data.calls.made} / ${data.calls.target}`}
          value={`${data.calls.made} `}
          icon={PhoneIcon}
          iconColor="text-[#1C5B41]"
          bgColor="bg-white"
        />
        <SummaryCard
          title="Visits Made (MTD)"
          // value={`${data.visits.made} / ${data.visits.target}`}
          value={`${data.visits.made}`}
          icon={CalendarIcon}
          iconColor="text-[#69AF57]"
          bgColor="bg-white"
        />
        <SummaryCard
          title="Hitlist Contacted"
          value={data.contacted}
          icon={CheckCircleIcon}
          iconColor="text-[#1C5B41]"
          bgColor="bg-white"
        />
      </div>

      {/* Charts: Calls vs Target and Visits vs Target */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <div className="p-4 rounded-lg shadow bg-white">
          <h2 className="text-lg font-medium text-gray-700 mb-2">
            Calls Made vs Target
          </h2>
          <Chart
            options={callsChart.options}
            series={callsChart.series}
            type="bar"
            height={300}
          />
        </div>
        <div className="p-4 rounded-lg shadow bg-white">
          <h2 className="text-lg font-medium text-gray-700 mb-2">
            Visits Made vs Target
          </h2>
          <Chart
            options={visitsChart.options}
            series={visitsChart.series}
            type="bar"
            height={300}
          />
        </div>
      </div>

      {/* Lead Status and Hitlist Progress Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <Chart
            options={leadStatusChart.options}
            series={leadStatusChart.series}
            type="pie"
            height={300}
          />
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <Chart
            options={hitlistProgressChart.options}
            series={hitlistProgressChart.series}
            type="donut"
            height={300}
          />
          {/* Summary stats */}
          <div className="mt-4 grid grid-cols-2 gap-4 pt-4 border-t border-gray-200">
            <div className="text-center">
              <div className="text-xl font-bold" style={{ color: "#69AF57" }}>
                {data.hitlistProgress.contacted}
              </div>
              <div className="text-xs text-gray-600">Contacted</div>
            </div>
            <div className="text-center">
              <div className="text-xl font-bold text-gray-500">
                {data.hitlistProgress.remaining}
              </div>
              <div className="text-xs text-gray-600">Remaining</div>
            </div>
          </div>
        </div>
      </div>

      {/* Activities Table */}
      <ActivitiesTable />
    </div>
  );
};

/*
  Note: 
  - Mock data is hardcoded in the state; replace `useEffect` and `fetch` logic here for real API data as needed.
  - The layout is mobile-responsive: widgets and lists stack on small screens and form columns on larger screens, as per Tailwind’s responsive design.
*/

/*
  API Integration Status:

  ✅ INTEGRATED WITH BACKEND API:
  - Hitlist Size (total_leads from API)
  - Contacted Leads (contacted_leads from API)
  - Calls Made MTD (total_calls from API)
  - Visits Made MTD (total_visits from API)
  - Lead Status Distribution (leads_by_status from API: pending, warm, hot, cold)
  - Hitlist Progress (calculated from total_leads and contacted_leads)
  - Month/Year (month_year from API)
  - User Permissions (user_permissions from API)

  📝 MOCK DATA (to be replaced when API provides):
  - Call Targets (currently hardcoded to 100)
  - Visit Targets (currently hardcoded to 80)

  🔗 API ENDPOINT: GET /leads/rbac-analytics
  📁 SERVICE: bankerDashboardService.js
*/

export default BankerDashboard;
