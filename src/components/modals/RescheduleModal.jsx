import { useState, useEffect } from "react";
import { Calendar, Clock, X } from "lucide-react";
import SuccessModal from "./SuccessModal";
import ErrorModal from "./ErrorModal";
import { followUpsService } from "../../services/followUpsService";

const RescheduleModal = ({ isOpen, onClose, followUp, onRescheduleSuccess }) => {
  const [formData, setFormData] = useState({
    date: "",
    time: "",
  });
  const [loading, setLoading] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  // Initialize form data when modal opens
  useEffect(() => {
    if (isOpen && followUp) {
      // Parse the existing followup_date to pre-fill the form
      if (followUp.followup_date) {
        try {
          const existingDate = new Date(followUp.followup_date);
          const dateStr = existingDate.toISOString().split('T')[0];
          const timeStr = existingDate.toTimeString().slice(0, 5);
          
          setFormData({
            date: dateStr,
            time: timeStr,
          });
        } catch (error) {
          console.error("Error parsing existing date:", error);
          setFormData({ date: "", time: "" });
        }
      } else {
        setFormData({ date: "", time: "" });
      }
    }
  }, [isOpen, followUp]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.date || !formData.time) {
      setErrorMessage("Please select both date and time");
      setShowErrorModal(true);
      return;
    }

    try {
      setLoading(true);
      
      // Combine date and time into ISO string
      const combinedDateTime = new Date(`${formData.date}T${formData.time}`).toISOString();
      
      // Call reschedule API
      const response = await followUpsService.reschedule(followUp.id, combinedDateTime);
      
      // Show success modal
      setShowSuccessModal(true);
      
      // Call success callback with updated data
      if (onRescheduleSuccess) {
        onRescheduleSuccess(followUp.id, response.for_date);
      }
      
    } catch (error) {
      console.error("Error rescheduling follow-up:", error);
      setErrorMessage(error.response?.data?.message || "Failed to reschedule follow-up");
      setShowErrorModal(true);
    } finally {
      setLoading(false);
    }
  };

  const handleSuccessModalClose = () => {
    setShowSuccessModal(false);
    onClose();
  };

  const handleErrorModalClose = () => {
    setShowErrorModal(false);
  };

  if (!isOpen) return null;

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <>
      <div
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
        onClick={handleBackdropClick}
      >
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Reschedule Follow-up
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <X size={20} />
            </button>
          </div>

          {/* Body */}
          <form onSubmit={handleSubmit} className="p-6">
            <div className="space-y-4">
              {/* Customer Name Display */}
              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                <p className="text-sm text-gray-600 dark:text-gray-400">Customer</p>
                <p className="font-medium text-gray-900 dark:text-white">
                  {followUp?.customer_name || "Unknown Customer"}
                </p>
              </div>

              {/* Date Input */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  <Calendar size={16} className="inline mr-2" />
                  New Date *
                </label>
                <input
                  type="date"
                  name="date"
                  value={formData.date}
                  onChange={handleInputChange}
                  required
                  min={new Date().toISOString().split('T')[0]}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:border-green-500 outline-none transition-colors duration-200"
                />
              </div>

              {/* Time Input */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  <Clock size={16} className="inline mr-2" />
                  New Time *
                </label>
                <input
                  type="time"
                  name="time"
                  value={formData.time}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:border-green-500 outline-none transition-colors duration-200"
                />
              </div>
            </div>

            {/* Footer */}
            <div className="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
              <button
                type="button"
                onClick={onClose}
                disabled={loading}
                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Rescheduling...
                  </>
                ) : (
                  "Reschedule"
                )}
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* Success Modal */}
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={handleSuccessModalClose}
        title="Follow-up Rescheduled"
        message="The follow-up has been successfully rescheduled!"
      />

      {/* Error Modal */}
      <ErrorModal
        isOpen={showErrorModal}
        onClose={handleErrorModalClose}
        title="Reschedule Failed"
        message={errorMessage}
      />
    </>
  );
};

export default RescheduleModal;
