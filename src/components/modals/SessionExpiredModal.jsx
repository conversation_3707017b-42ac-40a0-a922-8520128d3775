import { useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { X, AlertTriangle } from "lucide-react";
import { Button } from "primereact/button";

const SessionExpiredModal = ({ isOpen, onClose, onLoginRedirect }) => {
  const navigate = useNavigate();
  const location = useLocation();

  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }

    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isOpen]);

  const handleLoginRedirect = () => {
    onLoginRedirect();
    // Small delay to ensure state updates are processed before navigation
    setTimeout(() => {
      navigate("/login");
    }, 100);
  };

  // Don't show modal on login page
  if (!isOpen || location.pathname === "/login" || location.pathname === "/") {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black/70 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-lg w-full p-8 relative">
        {/* Header */}
        <div className="flex items-center gap-3 mb-4">
          <div className="flex-shrink-0">
            <AlertTriangle className="h-8 w-8 text-amber-500" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Session Expired
            </h3>
          </div>
        </div>

        {/* Content */}
        <div className="mb-6">
          <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
            Your session has expired for security reasons. Please log in again
            to continue using the application.
          </p>
        </div>

        {/* Actions */}
        <div className="flex justify-center">
          <button
            onClick={handleLoginRedirect}
            className="px-6 py-3 bg-white border-2 border-[rgba(28,91,65,0.3)] text-[#1c5b41] font-medium rounded-lg hover:bg-gray-50 transition-colors duration-200"
          >
            Go to Login
          </button>
        </div>
      </div>
    </div>
  );
};

export default SessionExpiredModal;
