import React from "react";
import { DataGrid } from "@mui/x-data-grid";
import { Box, Typography, Paper } from "@mui/material";
import { createTheme, ThemeProvider } from "@mui/material/styles";

function OverdueCallsTable({ dashboardData }) {
  // Use passed dashboardData instead of fetching
  const loading = !dashboardData;

  // Create custom theme for professional styling
  const theme = createTheme({
    components: {
      MuiDataGrid: {
        styleOverrides: {
          root: {
            border: "none",
            "& .MuiDataGrid-columnHeaders": {
              backgroundColor: "#1c5b41 !important",
              borderBottom: "1px solid #e2e8f0",
              "& .MuiDataGrid-columnHeader": {
                backgroundColor: "#1c5b41 !important",
                borderRight: "1px solid #ffffff",
                "&:focus": {
                  outline: "none",
                },
                "&:last-child": {
                  borderRight: "none",
                },
              },
            },
            "& .MuiDataGrid-columnHeaderTitle": {
              fontWeight: 600,
              fontSize: "0.75rem",
              color: "#ffffff !important",
              textTransform: "uppercase",
              letterSpacing: "0.05em",
            },
            "& .MuiDataGrid-cell": {
              borderBottom: "1px solid #f1f5f9",
              borderRight: "1px solid #e2e8f0",
              fontSize: "15px",
              "&:focus": {
                outline: "none",
              },
              "&:last-child": {
                borderRight: "none",
              },
            },
            "& .MuiDataGrid-row": {
              "&:nth-of-type(odd)": {
                backgroundColor: "#f8fafc",
                "&:hover": {
                  backgroundColor: "#f8fafc",
                },
              },
              "&:nth-of-type(even)": {
                backgroundColor: "#ffffff",
                "&:hover": {
                  backgroundColor: "#ffffff",
                },
              },
            },
          },
        },
      },
    },
  });

  // Extract overdue calls data from API response and transform it
  const apiOverdueCallsData = dashboardData?.overdue_calls || [];

  // Helper function to calculate handled date
  const calculateHandledDate = (expectedDate, overdueDays, isDone) => {
    if (!isDone) return "-";
    const date = new Date(expectedDate);
    date.setDate(date.getDate() + overdueDays);
    return date.toLocaleDateString();
  };

  // Transform API data to match table structure
  const overdueCallsData = apiOverdueCallsData.map((call, index) => ({
    id: index + 1,
    agent: call.agent,
    customerName: call.customer_name || "N/A",
    phase: call.phase,
    expectedDate: new Date(call.date).toLocaleDateString(),
    overdueBy: call.overdue_by,
    handledOn: calculateHandledDate(call.date, call.overdue_by, call.is_done),
    isDone: call.is_done,
  }));

  // Define columns for DataGrid - Updated to match requirements
  const columns = [
    {
      field: "agent",
      headerName: "AGENT",
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Typography variant="body2" sx={{ color: "#6b7280", fontSize: "15px" }}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: "customerName",
      headerName: "CUSTOMER NAME",
      flex: 1.2,
      minWidth: 180,
      renderCell: (params) => (
        <Typography variant="body2" sx={{ color: "#6b7280", fontSize: "15px" }}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: "phase",
      headerName: "PHASE",
      flex: 0.8,
      minWidth: 120,
      renderCell: (params) => (
        <Typography variant="body2" sx={{ color: "#6b7280", fontSize: "15px" }}>
          {params.value || "-"}
        </Typography>
      ),
    },
    {
      field: "expectedDate",
      headerName: "EXPECTED DATE",
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Typography
          variant="body2"
          sx={{ fontWeight: 500, color: "#1f2937", fontSize: "15px" }}
        >
          {params.value}
        </Typography>
      ),
    },
    {
      field: "overdueBy",
      headerName: "OVERDUE BY",
      flex: 0.8,
      minWidth: 120,
      renderCell: (params) => (
        <Typography
          variant="body2"
          sx={{
            color: "#1f2937",
            fontWeight: 500,
            fontSize: "15px",
          }}
        >
          {params.value} days
        </Typography>
      ),
    },
    {
      field: "handledOn",
      headerName: "HANDLED ON",
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Typography
          variant="body2"
          sx={{ fontWeight: 500, color: "#1f2937", fontSize: "15px" }}
        >
          {params.value}
        </Typography>
      ),
    },
  ];

  // Loading state
  if (loading) {
    return (
      <ThemeProvider theme={theme}>
        <Paper
          elevation={3}
          sx={{
            borderRadius: "12px",
            padding: "24px",
            marginBottom: "32px",
            backgroundColor: "white",
          }}
        >
          <Box sx={{ marginBottom: "24px" }}>
            <Typography
              variant="h5"
              sx={{
                fontWeight: 600,
                color: "#1f2937",
                marginBottom: "8px",
                fontSize: "23px",
              }}
            >
              OVERDUE CALLS THIS MONTH
            </Typography>
          </Box>
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              height: "200px",
            }}
          >
            <Typography variant="body1" sx={{ color: "#6b7280" }}>
              Loading overdue calls data...
            </Typography>
          </Box>
        </Paper>
      </ThemeProvider>
    );
  }

  return (
    <ThemeProvider theme={theme}>
      <Paper
        elevation={3}
        sx={{
          borderRadius: "12px",
          padding: "24px",
          marginBottom: "32px",
          backgroundColor: "white",
        }}
      >
        {/* Header */}
        <Box sx={{ marginBottom: "24px" }}>
          <Typography
            variant="h5"
            sx={{
              fontWeight: 600,
              color: "#1f2937",
              marginBottom: "8px",
              fontSize: "1.4rem",
            }}
          >
            OVERDUE CALLS THIS MONTH
          </Typography>
          <Typography
            variant="body2"
            sx={{ color: "#6b7280", fontSize: "15px" }}
          >
            Shows all overdue calls requiring immediate attention
          </Typography>
        </Box>

        {/* DataGrid */}
        <Box sx={{ height: 400, width: "100%" }}>
          <DataGrid
            rows={overdueCallsData}
            columns={columns}
            initialState={{
              pagination: {
                paginationModel: { page: 0, pageSize: 5 },
              },
            }}
            pageSizeOptions={[5, 10, 25]}
            disableRowSelectionOnClick
            disableColumnMenu
            hideFooterSelectedRowCount
            sx={{
              "& .MuiDataGrid-columnHeaders": {
                backgroundColor: "#1c5b41 !important",
              },
              "& .MuiDataGrid-columnHeader": {
                backgroundColor: "#1c5b41 !important",
              },
              "& .MuiDataGrid-columnHeaderTitle": {
                color: "#ffffff !important",
              },
              "& .MuiDataGrid-footerContainer": {
                borderTop: "1px solid #e5e7eb",
                backgroundColor: "#f8fafc",
              },
              "& .MuiTablePagination-root": {
                fontSize: "0.875rem",
                color: "#6b7280",
              },
            }}
          />
        </Box>

        {/* Summary */}
        <Box
          sx={{
            marginTop: "16px",
            paddingTop: "16px",
            borderTop: "1px solid #e5e7eb",
          }}
        >
          <Typography variant="body2" sx={{ color: "#6b7280" }}>
            Showing {overdueCallsData.length} overdue calls
          </Typography>
        </Box>
      </Paper>
    </ThemeProvider>
  );
}

export default OverdueCallsTable;
