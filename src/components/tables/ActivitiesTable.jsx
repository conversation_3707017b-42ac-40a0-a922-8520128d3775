import React, { useState, useEffect } from "react";
import { DataGrid } from "@mui/x-data-grid";
import {
  Box,
  FormControl,
  Select,
  MenuItem,
  Typography,
  Paper,
  CircularProgress,
} from "@mui/material";
import { createTheme, ThemeProvider } from "@mui/material/styles";
import { bankerDashboardService } from '../../services/bankerDashboardService';

function ActivitiesTable() {
  // Filter states
  const [statusFilter, setStatusFilter] = useState("all");
  const [activityTypeFilter, setActivityTypeFilter] = useState("all");
  
  // Data states
  const [activitiesData, setActivitiesData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });

  // API integration functions
  const fetchActivities = async () => {
    setLoading(true);
    try {
      const params = {
        page: pagination.page,
        limit: pagination.limit
      };

      // Add filters if they are not "all"
      if (statusFilter !== "all") params.status = statusFilter;
      if (activityTypeFilter !== "all") params.interaction_type = activityTypeFilter;

      const response = await bankerDashboardService.getActivityStatusData(params);

      // Transform API data to match table structure
      const transformedData = response.activities.map(activity => ({
        id: activity.id,
        customerName: activity.customer_name,
        staffName: activity.staff_name,
        activityType: activity.interaction_type,
        scheduledDate: new Date(activity.next_followup_date).toLocaleDateString(),
        scheduledTime: new Date(activity.next_followup_date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}),
        status: activity.status,
        contactInfo: activity.contact_info,
        notes: activity.notes
      }));

      setActivitiesData(transformedData);
      setPagination(prev => ({
        ...prev,
        total: response.total,
        totalPages: response.total_pages
      }));
    } catch (error) {
      console.error('Error fetching activities:', error);
      setActivitiesData([]);
    } finally {
      setLoading(false);
    }
  };

  // useEffect hooks for API calls
  useEffect(() => {
    fetchActivities();
  }, []);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchActivities();
    }, 300); // Debounce API calls

    return () => clearTimeout(timeoutId);
  }, [statusFilter, activityTypeFilter, pagination.page, pagination.limit]);

  // Filter change handlers
  const handleStatusChange = (value) => {
    setStatusFilter(value);
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleActivityTypeChange = (value) => {
    setActivityTypeFilter(value);
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  // Create custom theme for professional styling
  const theme = createTheme({
    components: {
      MuiDataGrid: {
        styleOverrides: {
          root: {
            border: "none",
            "& .MuiDataGrid-columnHeaders": {
              backgroundColor: "#1c5b41 !important",
              borderBottom: "1px solid #e2e8f0",
              "& .MuiDataGrid-columnHeader": {
                backgroundColor: "#1c5b41 !important",
                borderRight: "1px solid #ffffff",
                "&:focus": {
                  outline: "none",
                },
                "&:last-child": {
                  borderRight: "none",
                },
              },
            },
            "& .MuiDataGrid-columnHeaderTitle": {
              fontWeight: 600,
              fontSize: "0.75rem",
              color: "#ffffff !important",
              textTransform: "uppercase",
              letterSpacing: "0.05em",
            },
            "& .MuiDataGrid-cell": {
              borderBottom: "1px solid #f1f5f9",
              borderRight: "1px solid #e2e8f0",
              fontSize: "15px",
              "&:focus": {
                outline: "none",
              },
              "&:last-child": {
                borderRight: "none",
              },
            },
            "& .MuiDataGrid-row": {
              "&:nth-of-type(odd)": {
                backgroundColor: "#f8fafc",
                "&:hover": {
                  backgroundColor: "#f8fafc",
                },
              },
              "&:nth-of-type(even)": {
                backgroundColor: "#ffffff",
                "&:hover": {
                  backgroundColor: "#ffffff",
                },
              },
            },
          },
        },
      },
    },
  });

  // Define columns for DataGrid
  const columns = [
    {
      field: "customerName",
      headerName: "CUSTOMER NAME",
      flex: 1.5,
      minWidth: 200,
      renderCell: (params) => (
        <Typography
          variant="body2"
          sx={{ fontWeight: 500, color: "#1f2937", fontSize: "15px" }}
        >
          {params.value}
        </Typography>
      ),
    },
    {
      field: "staffName",
      headerName: "STAFF NAME",
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Typography variant="body2" sx={{ color: "#6b7280", fontSize: "15px" }}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: "activityType",
      headerName: "ACTIVITY TYPE",
      flex: 0.8,
      minWidth: 120,
      renderCell: (params) => (
        <Typography variant="body2" sx={{ color: "#6b7280", fontSize: "15px" }}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: "scheduledDate",
      headerName: "SCHEDULED DATE",
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Box sx={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <Typography
            variant="body2"
            sx={{ fontWeight: 500, color: "#1f2937", fontSize: "15px" }}
          >
            {params.row.scheduledDate}
          </Typography>
          <Typography
            variant="body2"
            sx={{ color: "#9ca3af", fontSize: "15px" }}
          >
            {params.row.scheduledTime}
          </Typography>
        </Box>
      ),
    },
    {
      field: "status",
      headerName: "STATUS",
      flex: 0.8,
      minWidth: 100,
      renderCell: (params) => {
        const getStatusColor = (status) => {
          switch (status.toLowerCase()) {
            case "upcoming":
              return "#1E3A8A";
            case "scheduled":
              return "#10b981";
            case "overdue":
              return "#ef4444";
            default:
              return "#6b7280";
          }
        };
        return (
          <Typography
            variant="body2"
            sx={{
              color: getStatusColor(params.value),
              fontWeight: 500,
              fontSize: "15px",
            }}
          >
            {params.value}
          </Typography>
        );
      },
    },
    {
      field: "contactInfo",
      headerName: "CONTACT INFO",
      flex: 1.2,
      minWidth: 180,
      renderCell: (params) => (
        <Typography variant="body2" sx={{ color: "#6b7280", fontSize: "15px" }}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: "notes",
      headerName: "NOTES",
      flex: 1.5,
      minWidth: 200,
      renderCell: (params) => (
        <Typography
          variant="body2"
          sx={{
            color: "#6b7280",
            fontSize: "15px",
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
          }}
        >
          {params.value}
        </Typography>
      ),
    },
  ];

  return (
    <ThemeProvider theme={theme}>
      <Paper
        elevation={3}
        sx={{
          borderRadius: "12px",
          padding: "24px",
          marginBottom: "32px",
          backgroundColor: "white",
        }}
      >
        {/* Header */}
        <Box sx={{ marginBottom: "24px" }}>
          <Typography
            variant="h5"
            sx={{
              fontWeight: 600,
              color: "#1f2937",
              marginBottom: "8px",
              fontSize: "1.4rem",
            }}
          >
            ACTIVITIES SCHEDULE
          </Typography>
          <Typography
            variant="body2"
            sx={{ color: "#6b7280", fontSize: "15px" }}
          >
            Shows a summary of upcoming, scheduled and overdue calls and visits
          </Typography>
        </Box>

        {/* Filter Dropdowns */}
        <Box
          sx={{
            display: "flex",
            gap: "32px",
            marginBottom: "24px",
            flexWrap: "wrap",
            alignItems: "center",
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: "12px" }}>
            <Typography
              sx={{
                fontSize: "0.75rem",
                fontWeight: 600,
                color: "#6b7280",
                textTransform: "uppercase",
                letterSpacing: "0.05em",
                minWidth: "60px",
              }}
            >
              STATUS:
            </Typography>
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <Select
                value={statusFilter}
                onChange={(e) => handleStatusChange(e.target.value)}
                displayEmpty
                sx={{
                  fontSize: "0.875rem",
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#e5e7eb",
                  },
                  "&:hover .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#d1d5db",
                  },
                  "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#3b82f6",
                  },
                }}
              >
                <MenuItem value="all">All</MenuItem>
                <MenuItem value="upcoming">Upcoming</MenuItem>
                <MenuItem value="scheduled">Scheduled</MenuItem>
                <MenuItem value="overdue">Overdue</MenuItem>
              </Select>
            </FormControl>
          </Box>

          <Box sx={{ display: "flex", alignItems: "center", gap: "12px" }}>
            <Typography
              sx={{
                fontSize: "0.75rem",
                fontWeight: 600,
                color: "#6b7280",
                textTransform: "uppercase",
                letterSpacing: "0.05em",
                minWidth: "100px",
              }}
            >
              ACTIVITY TYPE:
            </Typography>
            <FormControl size="small" sx={{ minWidth: 140 }}>
              <Select
                value={activityTypeFilter}
                onChange={(e) => handleActivityTypeChange(e.target.value)}
                displayEmpty
                sx={{
                  fontSize: "0.875rem",
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#e5e7eb",
                  },
                  "&:hover .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#d1d5db",
                  },
                  "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#3b82f6",
                  },
                }}
              >
                <MenuItem value="all">All</MenuItem>
                <MenuItem value="call">Call</MenuItem>
                <MenuItem value="visit">Visit</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </Box>

        {/* DataGrid */}
        <Box sx={{ height: 600, width: "100%", position: "relative" }}>
          {loading && (
            <Box
              sx={{
                position: "absolute",
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                backgroundColor: "rgba(255, 255, 255, 0.8)",
                zIndex: 1,
              }}
            >
              <CircularProgress />
            </Box>
          )}
          <DataGrid
            rows={activitiesData}
            columns={columns}
            paginationMode="server"
            rowCount={pagination.total}
            page={pagination.page - 1}
            pageSize={pagination.limit}
            rowsPerPageOptions={[10, 25, 50]}
            onPageChange={(newPage) => setPagination(prev => ({ ...prev, page: newPage + 1 }))}
            onPageSizeChange={(newPageSize) => setPagination(prev => ({ ...prev, limit: newPageSize, page: 1 }))}
            disableSelectionOnClick
            disableColumnMenu
            loading={loading}
            sx={{
              border: "1px solid #e5e7eb",
              borderRadius: "8px",
              "& .MuiDataGrid-footerContainer": {
                borderTop: "1px solid #e5e7eb",
                backgroundColor: "#f9fafb",
              },
              "& .MuiDataGrid-columnSeparator": {
                display: "none",
              },
              "& .MuiDataGrid-virtualScroller": {
                backgroundColor: "white",
              },
            }}
          />
        </Box>

        {/* Summary */}
        <Box
          sx={{
            marginTop: "16px",
            padding: "16px",
            backgroundColor: "#f9fafb",
            borderRadius: "8px",
            border: "1px solid #e5e7eb",
          }}
        >
          <Typography
            variant="body2"
            sx={{ color: "#6b7280", fontSize: "14px" }}
          >
            Showing {activitiesData.length} of {pagination.total} activities
          </Typography>
        </Box>
      </Paper>
    </ThemeProvider>
  );
}

export default ActivitiesTable;
