import { useEffect } from "react";
import { useLocation } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";
import SessionExpiredModal from "./modals/SessionExpiredModal";

const SessionManager = () => {
  const location = useLocation();
  const {
    showSessionExpiredModal,
    handleSessionExpiredClose,
    handleSessionExpiredLoginRedirect,
  } = useAuth();

  // Hide modal when on login page
  useEffect(() => {
    if (location.pathname === "/login" || location.pathname === "/") {
      if (showSessionExpiredModal) {
        localStorage.removeItem("sessionExpired");
        handleSessionExpiredClose();
      }
    }
  }, [location.pathname, showSessionExpiredModal, handleSessionExpiredClose]);

  return (
    <SessionExpiredModal
      isOpen={showSessionExpiredModal}
      onClose={handleSessionExpiredClose}
      onLoginRedirect={handleSessionExpiredLoginRedirect}
    />
  );
};

export default SessionManager;
