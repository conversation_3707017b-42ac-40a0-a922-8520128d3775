import React, { useState } from "react";
import Chart from "react-apexcharts";

function CallActivitiesTargetChart() {
  const defaultBranch = "Downtown Branch";
  const [selectedYear, setSelectedYear] = useState("2025");
  const [selectedBranch, setSelectedBranch] = useState(defaultBranch);

  const years = ["2023", "2024", "2025"];
  const branches = [
    "Downtown Branch",
    "Westside Branch", 
    "Eastgate Branch",
    "Northpoint Branch",
    "Southpark Branch",
    "Central Branch"
  ];

  // Test data for call activities vs targets by year and branch
  const callData = {
    "Downtown Branch": {
      "2023": {
        activities: [45, 52, 48, 61, 58, 65, 72, 68, 75, 82, 78, 85],
        targets: [50, 55, 50, 65, 60, 70, 75, 70, 80, 85, 80, 90]
      },
      "2024": {
        activities: [52, 58, 55, 68, 65, 72, 79, 75, 82, 89, 85, 92],
        targets: [55, 60, 55, 70, 65, 75, 80, 75, 85, 90, 85, 95]
      },
      "2025": {
        activities: [58, 65, 62, 75, 72, 79, 86, 82, 89, 96, 92, 99],
        targets: [60, 65, 60, 75, 70, 80, 85, 80, 90, 95, 90, 100]
      }
    },
    "Westside Branch": {
      "2023": {
        activities: [38, 45, 41, 54, 51, 58, 65, 61, 68, 75, 71, 78],
        targets: [40, 45, 40, 55, 50, 60, 65, 60, 70, 75, 70, 80]
      },
      "2024": {
        activities: [45, 51, 48, 61, 58, 65, 72, 68, 75, 82, 78, 85],
        targets: [45, 50, 45, 60, 55, 65, 70, 65, 75, 80, 75, 85]
      },
      "2025": {
        activities: [51, 58, 55, 68, 65, 72, 79, 75, 82, 89, 85, 92],
        targets: [50, 55, 50, 65, 60, 70, 75, 70, 80, 85, 80, 90]
      }
    },
    "Eastgate Branch": {
      "2023": {
        activities: [52, 59, 55, 68, 65, 72, 79, 75, 82, 89, 85, 92],
        targets: [55, 60, 55, 70, 65, 75, 80, 75, 85, 90, 85, 95]
      },
      "2024": {
        activities: [59, 65, 62, 75, 72, 79, 86, 82, 89, 96, 92, 99],
        targets: [60, 65, 60, 75, 70, 80, 85, 80, 90, 95, 90, 100]
      },
      "2025": {
        activities: [65, 72, 68, 82, 79, 86, 93, 89, 96, 103, 99, 106],
        targets: [65, 70, 65, 80, 75, 85, 90, 85, 95, 100, 95, 105]
      }
    },
    "Northpoint Branch": {
      "2023": {
        activities: [32, 38, 35, 48, 45, 52, 59, 55, 62, 69, 65, 72],
        targets: [35, 40, 35, 50, 45, 55, 60, 55, 65, 70, 65, 75]
      },
      "2024": {
        activities: [38, 45, 41, 54, 51, 58, 65, 61, 68, 75, 71, 78],
        targets: [40, 45, 40, 55, 50, 60, 65, 60, 70, 75, 70, 80]
      },
      "2025": {
        activities: [45, 51, 48, 61, 58, 65, 72, 68, 75, 82, 78, 85],
        targets: [45, 50, 45, 60, 55, 65, 70, 65, 75, 80, 75, 85]
      }
    },
    "Southpark Branch": {
      "2023": {
        activities: [42, 48, 45, 58, 55, 62, 69, 65, 72, 79, 75, 82],
        targets: [45, 50, 45, 60, 55, 65, 70, 65, 75, 80, 75, 85]
      },
      "2024": {
        activities: [48, 55, 51, 64, 61, 68, 75, 71, 78, 85, 81, 88],
        targets: [50, 55, 50, 65, 60, 70, 75, 70, 80, 85, 80, 90]
      },
      "2025": {
        activities: [55, 61, 58, 71, 68, 75, 82, 78, 85, 92, 88, 95],
        targets: [55, 60, 55, 70, 65, 75, 80, 75, 85, 90, 85, 95]
      }
    },
    "Central Branch": {
      "2023": {
        activities: [39, 45, 42, 55, 52, 59, 66, 62, 69, 76, 72, 79],
        targets: [42, 47, 42, 57, 52, 62, 67, 62, 72, 77, 72, 82]
      },
      "2024": {
        activities: [45, 52, 48, 61, 58, 65, 72, 68, 75, 82, 78, 85],
        targets: [47, 52, 47, 62, 57, 67, 72, 67, 77, 82, 77, 87]
      },
      "2025": {
        activities: [52, 58, 55, 68, 65, 72, 79, 75, 82, 89, 85, 92],
        targets: [52, 57, 52, 67, 62, 72, 77, 72, 82, 87, 82, 92]
      }
    }
  };

  const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
  const currentData = callData[selectedBranch][selectedYear];

  const chartOptions = {
    chart: {
      type: "bar",
      height: 400,
      toolbar: {
        show: false,
      },
      animations: {
        enabled: true,
        easing: "easeinout",
        speed: 800,
        animateGradually: {
          enabled: true,
          delay: 150,
        },
        dynamicAnimation: {
          enabled: true,
          speed: 350,
        },
      },
      background: "#ffffff",
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: "60%",
        grouped: true,
        endingShape: "flat",
        borderRadius: 0,
        dataLabels: {
          position: "top",
        },
      },
    },
    dataLabels: {
      enabled: true,
      formatter: function (val) {
        return val.toLocaleString();
      },
      offsetY: -20,
      style: {
        fontSize: "12px",
        fontWeight: 600,
        colors: ["#2D3748"],
      },
    },
    stroke: {
      show: false,
      width: 0,
    },
    xaxis: {
      categories: months,
      labels: {
        style: {
          fontSize: "14px",
          fontWeight: 600,
          colors: ["#2D3748"],
        },
      },
      axisBorder: {
        show: true,
        color: "#e5e7eb",
      },
      axisTicks: {
        show: true,
        color: "#e5e7eb",
      },
      title: {
        text: "Months",
        style: {
          fontSize: "14px",
          fontWeight: 600,
          color: "#2D3748",
        },
      },
    },
    yaxis: {
      labels: {
        style: {
          fontSize: "14px",
          fontWeight: 600,
          colors: ["#2D3748"],
        },
      },
      title: {
        text: "Number of Calls",
        style: {
          fontSize: "14px",
          fontWeight: 600,
          color: "#2D3748",
        },
      },
    },
    fill: {
      opacity: 1,
      type: "solid",
    },
    colors: ["#1c5b41", "#82C355"], // Dark green for activities, Light green for targets
    legend: {
      show: true,
      position: "top",
      horizontalAlign: "center",
      fontSize: "14px",
      fontWeight: 600,
      fontFamily: "Inter, system-ui, sans-serif",
      markers: {
        width: 14,
        height: 14,
        radius: 2,
        offsetX: -2,
      },
      itemMargin: {
        horizontal: 20,
        vertical: 0,
      },
      labels: {
        colors: ["#374151"],
      },
    },
    tooltip: {
      enabled: true,
      shared: false,
      intersect: true,
      y: {
        formatter: function (val, { seriesIndex }) {
          const labels = ["activities", "target"];
          return `${val.toLocaleString()} ${labels[seriesIndex]}`;
        },
      },
      style: {
        fontSize: "12px",
      },
    },
    grid: {
      borderColor: "#e5e7eb",
      strokeDashArray: 0,
      xaxis: {
        lines: {
          show: false,
        },
      },
      yaxis: {
        lines: {
          show: true,
        },
      },
      padding: {
        top: 0,
        right: 20,
        bottom: 0,
        left: 10,
      },
    },
    responsive: [
      {
        breakpoint: 768,
        options: {
          chart: {
            height: 300,
          },
          plotOptions: {
            bar: {
              columnWidth: "70%",
            },
          },
          dataLabels: {
            style: {
              fontSize: "10px",
            },
          },
          legend: {
            fontSize: "12px",
            itemMargin: {
              horizontal: 10,
              vertical: 3,
            },
          },
        },
      },
    ],
  };

  const series = [
    {
      name: "Call Activities",
      data: currentData.activities,
    },
    {
      name: "Aggregate Target",
      data: currentData.targets,
    },
  ];

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h3 className="text-[23px] font-semibold text-gray-800 mb-2">
              Call Activities vs Aggregate Target per Branch
            </h3>
            <p className="text-sm text-gray-600">
              Monthly call performance vs targets for selected branch and year
            </p>
          </div>
          
          {/* Filters */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium text-gray-700">Year:</label>
              <select
                value={selectedYear}
                onChange={(e) => setSelectedYear(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {years.map((year) => (
                  <option key={year} value={year}>
                    {year}
                  </option>
                ))}
              </select>
            </div>
            
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium text-gray-700">Branch:</label>
              <select
                value={selectedBranch}
                onChange={(e) => setSelectedBranch(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {branches.map((branch) => (
                  <option key={branch} value={branch}>
                    {branch}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </div>

      <Chart options={chartOptions} series={series} type="bar" height={400} />

      {/* Summary stats */}
      <div className="mt-6 grid grid-cols-3 gap-4 pt-4 border-t border-gray-200">
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#1c5b41" }}>
            {currentData.activities.reduce((sum, val) => sum + val, 0).toLocaleString()}
          </div>
          <div className="text-xs text-gray-600">Total Activities</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#82C355" }}>
            {currentData.targets.reduce((sum, val) => sum + val, 0).toLocaleString()}
          </div>
          <div className="text-xs text-gray-600">Total Target</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#374151" }}>
            {((currentData.activities.reduce((sum, val) => sum + val, 0) / currentData.targets.reduce((sum, val) => sum + val, 0)) * 100).toFixed(1)}%
          </div>
          <div className="text-xs text-gray-600">Achievement Rate</div>
        </div>
      </div>
    </div>
  );
}

export default CallActivitiesTargetChart;
