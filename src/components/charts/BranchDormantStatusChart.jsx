import React, { useState, useEffect } from "react";
import Chart from "react-apexcharts";
import { branchManagerDashboardService } from "../../services/branchManagerDashboardService";

function BranchDormantStatusChart({ data }) {
  const [selectedTimeRange, setSelectedTimeRange] = useState("All time");

  // Extract dormant records data from props
  const dormantRecordsData = data?.dormant_records || {
    total: { uncontacted: 0, contacted: 0 },
    this_month: { uncontacted: 0, contacted: 0 },
  };

  const dormantData = {
    "All time": {
      dormant: dormantRecordsData.total.uncontacted,
      contacted: dormantRecordsData.total.contacted,
    },
    "This month": {
      dormant: dormantRecordsData.this_month.uncontacted,
      contacted: dormantRecordsData.this_month.contacted,
    },
  };

  const currentData = dormantData[selectedTimeRange];
  const total = currentData.dormant + currentData.contacted;

  const chartOptions = {
    chart: {
      type: "pie",
      height: 400,
      animations: {
        enabled: true,
        easing: "easeinout",
        speed: 800,
        animateGradually: {
          enabled: true,
          delay: 150,
        },
      },
    },
    colors: ["#e5e7eb", "#1c5b41"], // Gray for dormant, Green for contacted
    labels: ["Dormant Customers", "Contacted Customers"],
    dataLabels: {
      enabled: true,
      formatter: function (val, opts) {
        return val.toFixed(1) + "%";
      },
      style: {
        fontSize: "12px",
        fontWeight: 600,
        colors: ["#ffffff"],
      },
      dropShadow: {
        enabled: false,
      },
    },
    legend: {
      show: true,
      position: "bottom",
      horizontalAlign: "center",
      fontSize: "12px",
      fontWeight: 500,
      markers: {
        width: 10,
        height: 10,
        radius: 5,
      },
      itemMargin: {
        horizontal: 8,
        vertical: 3,
      },
      formatter: function (seriesName, opts) {
        const value = opts.w.globals.series[opts.seriesIndex];
        const actualValue =
          seriesName === "Dormant Customers"
            ? currentData.dormant
            : currentData.contacted;
        return seriesName + ": " + actualValue.toLocaleString();
      },
    },
    tooltip: {
      enabled: true,
      custom: function ({ series, seriesIndex }) {
        const values = [currentData.dormant, currentData.contacted];
        const labels = ["Dormant Customers", "Contacted Customers"];
        const colors = ["#F47976", "#1c5b41"];

        return `
          <div style="padding: 8px 12px; background: white; border-radius: 4px; box-shadow: 0 2px 8px rgba(0,0,0,0.15); font-size: 14px;">
            <span style="color: ${colors[seriesIndex]};">
              ${labels[seriesIndex]}: ${values[seriesIndex]} customers (${series[seriesIndex]}%)
            </span>
          </div>
        `;
      },
    },
    stroke: {
      show: true,
      width: 2,
      colors: ["#ffffff"],
    },
    states: {
      hover: {
        filter: {
          type: "none",
        },
      },
    },
    responsive: [
      {
        breakpoint: 768,
        options: {
          chart: {
            height: 300,
          },
          legend: {
            fontSize: "10px",
            itemMargin: {
              horizontal: 5,
              vertical: 2,
            },
          },
          dataLabels: {
            style: {
              fontSize: "10px",
            },
          },
        },
      },
    ],
  };

  const series = [
    ((currentData.dormant / total) * 100).toFixed(1),
    ((currentData.contacted / total) * 100).toFixed(1),
  ];

  // Loading state - show loading if no data is provided
  if (!data) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="mb-4">
          <h3 className="text-[23px] font-semibold text-gray-800 mb-2">
            Branch Dormant Status
          </h3>
          <p className="text-sm text-gray-600">
            Dormant vs Contacted Customers
          </p>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-600">Loading chart data...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <div className="mb-4">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h3 className="text-[23px] font-semibold text-gray-800 mb-2">
              Branch Dormant Status
            </h3>
            <p className="text-sm text-gray-600">
              Dormant vs Contacted Customers
            </p>
          </div>

          {/* Time Range Filter */}
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-gray-700">
              Time Range:
            </label>
            <select
              value={selectedTimeRange}
              onChange={(e) => setSelectedTimeRange(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="All time">All time</option>
              <option value="This month">This month</option>
            </select>
          </div>
        </div>
      </div>

      <Chart
        options={chartOptions}
        series={series.map(Number)}
        type="pie"
        height={400}
      />

      {/* Summary stats */}
      <div className="mt-4 grid grid-cols-2 gap-4 pt-4 border-t border-gray-200">
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#F47976" }}>
            {currentData.dormant.toLocaleString()}
          </div>
          <div className="text-xs text-gray-600">Dormant</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#1c5b41" }}>
            {currentData.contacted.toLocaleString()}
          </div>
          <div className="text-xs text-gray-600">Contacted</div>
        </div>
      </div>
    </div>
  );
}

export default BranchDormantStatusChart;
