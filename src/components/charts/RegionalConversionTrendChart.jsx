import React, { useState } from "react";
import Chart from "react-apexcharts";

function RegionalConversionTrendChart() {
  const [selectedBranch, setSelectedBranch] = useState("All");
  const [selectedYear, setSelectedYear] = useState("2025");

  // Test data for branches
  const branches = [
    "All",
    "Downtown Branch",
    "Westside Branch", 
    "Eastgate Branch",
    "Northpoint Branch",
    "Southpark Branch",
    "Central Branch"
  ];

  const years = ["2023", "2024", "2025"];

  // Test data for conversion trends by branch and year
  const conversionData = {
    "All": {
      "2023": [45, 52, 48, 61, 58, 65, 72, 68, 75, 82, 78, 85],
      "2024": [52, 58, 55, 68, 65, 72, 79, 75, 82, 89, 85, 92],
      "2025": [58, 65, 62, 75, 72, 79, 86, 82, 89, 96, 92, 99]
    },
    "Downtown Branch": {
      "2023": [12, 15, 13, 18, 16, 19, 22, 20, 23, 26, 24, 27],
      "2024": [15, 18, 16, 21, 19, 22, 25, 23, 26, 29, 27, 30],
      "2025": [18, 21, 19, 24, 22, 25, 28, 26, 29, 32, 30, 33]
    },
    "Westside Branch": {
      "2023": [8, 11, 9, 14, 12, 15, 18, 16, 19, 22, 20, 23],
      "2024": [11, 14, 12, 17, 15, 18, 21, 19, 22, 25, 23, 26],
      "2025": [14, 17, 15, 20, 18, 21, 24, 22, 25, 28, 26, 29]
    },
    "Eastgate Branch": {
      "2023": [15, 18, 16, 21, 19, 22, 25, 23, 26, 29, 27, 30],
      "2024": [18, 21, 19, 24, 22, 25, 28, 26, 29, 32, 30, 33],
      "2025": [21, 24, 22, 27, 25, 28, 31, 29, 32, 35, 33, 36]
    },
    "Northpoint Branch": {
      "2023": [6, 9, 7, 12, 10, 13, 16, 14, 17, 20, 18, 21],
      "2024": [9, 12, 10, 15, 13, 16, 19, 17, 20, 23, 21, 24],
      "2025": [12, 15, 13, 18, 16, 19, 22, 20, 23, 26, 24, 27]
    },
    "Southpark Branch": {
      "2023": [10, 13, 11, 16, 14, 17, 20, 18, 21, 24, 22, 25],
      "2024": [13, 16, 14, 19, 17, 20, 23, 21, 24, 27, 25, 28],
      "2025": [16, 19, 17, 22, 20, 23, 26, 24, 27, 30, 28, 31]
    },
    "Central Branch": {
      "2023": [9, 12, 10, 15, 13, 16, 19, 17, 20, 23, 21, 24],
      "2024": [12, 15, 13, 18, 16, 19, 22, 20, 23, 26, 24, 27],
      "2025": [15, 18, 16, 21, 19, 22, 25, 23, 26, 29, 27, 30]
    }
  };

  const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
  const currentData = conversionData[selectedBranch][selectedYear];

  const chartOptions = {
    chart: {
      type: "line",
      height: 400,
      toolbar: {
        show: false,
      },
      animations: {
        enabled: true,
        easing: "easeinout",
        speed: 800,
        animateGradually: {
          enabled: true,
          delay: 150,
        },
      },
      background: "#ffffff",
    },
    stroke: {
      curve: "smooth",
      width: 3,
    },
    markers: {
      size: 6,
      colors: ["#1c5b41"],
      strokeColors: "#ffffff",
      strokeWidth: 2,
      hover: {
        size: 8,
      },
    },
    dataLabels: {
      enabled: false,
    },
    xaxis: {
      categories: months,
      labels: {
        style: {
          fontSize: "14px",
          fontWeight: 600,
          colors: ["#2D3748"],
        },
      },
      axisBorder: {
        show: true,
        color: "#e5e7eb",
      },
      axisTicks: {
        show: true,
        color: "#e5e7eb",
      },
      title: {
        text: "Month",
        style: {
          fontSize: "14px",
          fontWeight: 600,
          color: "#2D3748",
        },
      },
    },
    yaxis: {
      labels: {
        style: {
          fontSize: "14px",
          fontWeight: 600,
          colors: ["#2D3748"],
        },
      },
      title: {
        text: "Number of Conversions",
        style: {
          fontSize: "14px",
          fontWeight: 600,
          color: "#2D3748",
        },
      },
    },
    colors: ["#1c5b41"],
    tooltip: {
      enabled: true,
      y: {
        formatter: function (val) {
          return `${val} conversions`;
        },
      },
      style: {
        fontSize: "12px",
      },
    },
    grid: {
      borderColor: "#e5e7eb",
      strokeDashArray: 0,
      xaxis: {
        lines: {
          show: false,
        },
      },
      yaxis: {
        lines: {
          show: true,
        },
      },
      padding: {
        top: 0,
        right: 20,
        bottom: 0,
        left: 10,
      },
    },
    responsive: [
      {
        breakpoint: 768,
        options: {
          chart: {
            height: 300,
          },
        },
      },
    ],
  };

  const series = [
    {
      name: "Conversions",
      data: currentData,
    },
  ];

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h3 className="text-[23px] font-semibold text-gray-800 mb-2">
              Regional Conversion Trend
            </h3>
            <p className="text-sm text-gray-600">
              Monthly conversion trends for selected branch and year
            </p>
          </div>
          
          {/* Filters */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium text-gray-700">Branch:</label>
              <select
                value={selectedBranch}
                onChange={(e) => setSelectedBranch(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {branches.map((branch) => (
                  <option key={branch} value={branch}>
                    {branch}
                  </option>
                ))}
              </select>
            </div>
            
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium text-gray-700">Year:</label>
              <select
                value={selectedYear}
                onChange={(e) => setSelectedYear(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {years.map((year) => (
                  <option key={year} value={year}>
                    {year}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </div>

      <Chart options={chartOptions} series={series} type="line" height={400} />

      {/* Summary stats */}
      <div className="mt-6 grid grid-cols-3 gap-4 pt-4 border-t border-gray-200">
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#1c5b41" }}>
            {Math.max(...currentData)}
          </div>
          <div className="text-xs text-gray-600">Peak Month</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#1c5b41" }}>
            {Math.round(currentData.reduce((sum, val) => sum + val, 0) / currentData.length)}
          </div>
          <div className="text-xs text-gray-600">Average</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#1c5b41" }}>
            {currentData.reduce((sum, val) => sum + val, 0)}
          </div>
          <div className="text-xs text-gray-600">Total {selectedYear}</div>
        </div>
      </div>
    </div>
  );
}

export default RegionalConversionTrendChart;
