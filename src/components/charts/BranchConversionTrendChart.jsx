import React, { useState, useEffect } from "react";
import Chart from "react-apexcharts";
import { branchManagerDashboardService } from "../../services/branchManagerDashboardService";

function BranchConversionTrendChart({ data }) {
  const [selectedYear, setSelectedYear] = useState("");

  // Extract conversion trend data from props
  const conversionTrendData = data?.branch_conversion_trend || {};

  // Set default year to the latest year available when data changes
  useEffect(() => {
    if (data?.branch_conversion_trend) {
      const years = Object.keys(data.branch_conversion_trend);
      if (years.length > 0) {
        const latestYear = Math.max(...years.map(Number)).toString();
        setSelectedYear(latestYear);
      }
    }
  }, [data]);
  const years = Object.keys(conversionTrendData).sort(
    (a, b) => Number(b) - Number(a)
  ); // Sort descending

  const months = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];
  const currentData = selectedYear
    ? conversionTrendData[selectedYear] || []
    : [];

  const chartOptions = {
    chart: {
      type: "line",
      height: 350,
      toolbar: {
        show: false,
      },
      animations: {
        enabled: true,
        easing: "easeinout",
        speed: 800,
        animateGradually: {
          enabled: true,
          delay: 150,
        },
      },
      background: "#ffffff",
    },
    stroke: {
      curve: "smooth",
      width: 3,
    },
    markers: {
      size: 6,
      colors: ["#1c5b41"],
      strokeColors: "#ffffff",
      strokeWidth: 2,
      hover: {
        size: 8,
      },
    },
    dataLabels: {
      enabled: false,
    },
    xaxis: {
      categories: months,
      labels: {
        style: {
          fontSize: "12px",
          fontWeight: 600,
          colors: ["#2D3748"],
        },
      },
      axisBorder: {
        show: true,
        color: "#e5e7eb",
      },
      axisTicks: {
        show: true,
        color: "#e5e7eb",
      },
      title: {
        text: "Month",
        style: {
          fontSize: "12px",
          fontWeight: 600,
          color: "#2D3748",
        },
      },
    },
    yaxis: {
      labels: {
        style: {
          fontSize: "12px",
          fontWeight: 600,
          colors: ["#2D3748"],
        },
      },
      title: {
        text: "Conversions",
        style: {
          fontSize: "12px",
          fontWeight: 600,
          color: "#2D3748",
        },
      },
    },
    colors: ["#1c5b41"],
    tooltip: {
      enabled: true,
      y: {
        formatter: function (val) {
          return `${val} conversions`;
        },
      },
      style: {
        fontSize: "12px",
      },
    },
    grid: {
      borderColor: "#e5e7eb",
      strokeDashArray: 0,
      xaxis: {
        lines: {
          show: false,
        },
      },
      yaxis: {
        lines: {
          show: true,
        },
      },
      padding: {
        top: 0,
        right: 20,
        bottom: 0,
        left: 10,
      },
    },
    responsive: [
      {
        breakpoint: 768,
        options: {
          chart: {
            height: 300,
          },
        },
      },
    ],
  };

  const series = [
    {
      name: "Conversions",
      data: currentData,
    },
  ];

  // Loading state - show loading if no data is provided
  if (!data) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="mb-6">
          <h3 className="text-[23px] font-semibold text-gray-800 mb-2">
            Branch Conversion Trend
          </h3>
          <p className="text-sm text-gray-600">
            Monthly conversion trends for selected year
          </p>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-600">Loading chart data...</div>
        </div>
      </div>
    );
  }

  // Filter out null values for calculations
  const validData = currentData.filter(
    (val) => val !== null && val !== undefined
  );

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h3 className="text-[23px] font-semibold text-gray-800 mb-2">
              Branch Conversion Trend
            </h3>
            <p className="text-sm text-gray-600">
              Monthly conversion trends for selected year
            </p>
          </div>

          {/* Year Filter */}
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-gray-700">Year:</label>
            <select
              value={selectedYear}
              onChange={(e) => setSelectedYear(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {years.map((year) => (
                <option key={year} value={year}>
                  {year}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      <Chart options={chartOptions} series={series} type="line" height={350} />

      {/* Summary stats */}
      <div className="mt-6 grid grid-cols-3 gap-4 pt-4 border-t border-gray-200">
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#1c5b41" }}>
            {validData.length > 0 ? Math.max(...validData) : 0}
          </div>
          <div className="text-xs text-gray-600">Peak Month</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#1c5b41" }}>
            {validData.length > 0
              ? Math.round(
                  validData.reduce((sum, val) => sum + val, 0) /
                    validData.length
                )
              : 0}
          </div>
          <div className="text-xs text-gray-600">Average</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#1c5b41" }}>
            {validData.length > 0
              ? validData.reduce((sum, val) => sum + val, 0)
              : 0}
          </div>
          <div className="text-xs text-gray-600">Total {selectedYear}</div>
        </div>
      </div>
    </div>
  );
}

export default BranchConversionTrendChart;
