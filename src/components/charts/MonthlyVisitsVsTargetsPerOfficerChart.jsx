import React, { useState, useEffect, useRef } from "react";
import Chart from "react-apexcharts";
import useCountUp from "../../hooks/useCountUp";
import { branchManagerDashboardService } from "../../services/branchManagerDashboardService";

function MonthlyVisitsVsTargetsPerOfficerChart({ data }) {
  const [selectedUser, setSelectedUser] = useState(0);

  // Animation state and ref
  const [hasAnimated, setHasAnimated] = useState(false);
  const [shouldAnimate, setShouldAnimate] = useState(false);
  const chartRef = useRef(null);

  // Extract visits vs targets per officer data from props
  const staffData = data?.visits_vs_targets_per_officer || [
    {
      user_name: "Loading...",
      visits_made: 0,
      visits_target: 0,
    },
  ];

  // Get selected user data
  const selectedUserData = staffData[selectedUser] || staffData[0];

  // Intersection Observer for animation
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !hasAnimated) {
            setShouldAnimate(true);
            setHasAnimated(true);
          }
        });
      },
      { threshold: 0.3 }
    );

    if (chartRef.current) {
      observer.observe(chartRef.current);
    }

    return () => {
      if (chartRef.current) {
        observer.unobserve(chartRef.current);
      }
    };
  }, [hasAnimated]);

  // Extract data for chart - single user
  const categories = [selectedUserData.user_name];
  const visitsMadeData = [selectedUserData.visits_made];
  const targetData = [selectedUserData.visits_target];

  // Animated values for summary stats
  const totalVisitsMade = staffData.reduce(
    (sum, staff) => sum + staff.visits_made,
    0
  );
  const totalTarget = staffData.reduce(
    (sum, staff) => sum + staff.visits_target,
    0
  );
  const animatedVisitsMade = useCountUp(
    shouldAnimate ? totalVisitsMade : 0,
    2000,
    0
  );
  const animatedTarget = useCountUp(shouldAnimate ? totalTarget : 0, 2000, 200);

  const chartOptions = {
    chart: {
      type: "bar",
      height: 450,
      toolbar: {
        show: false,
      },
      animations: {
        enabled: shouldAnimate,
        easing: "easeinout",
        speed: 1200,
        animateGradually: {
          enabled: true,
          delay: 150,
        },
      },
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: "50%",
        endingShape: "rounded",
        borderRadius: 6,
        dataLabels: {
          position: "top",
        },
      },
    },
    dataLabels: {
      enabled: true,
      offsetY: -20,
      style: {
        fontSize: "14px",
        fontWeight: 700,
        colors: ["#2D3748"],
      },
      formatter: function (val) {
        return val.toLocaleString();
      },
    },
    stroke: {
      show: true,
      width: 2,
      colors: ["transparent"],
    },
    xaxis: {
      categories: categories,
      labels: {
        style: {
          fontSize: "14px",
          fontWeight: 600,
          colors: ["#2D3748"],
        },
        maxHeight: 60,
        formatter: function (val) {
          // Truncate long names
          return val.length > 15 ? val.substring(0, 15) + "..." : val;
        },
      },
      axisBorder: {
        show: true,
        color: "#e5e7eb",
      },
      axisTicks: {
        show: true,
        color: "#e5e7eb",
      },
    },
    yaxis: {
      labels: {
        style: {
          fontSize: "12px",
          fontWeight: 600,
          colors: ["#2D3748"],
        },
        formatter: function (val) {
          return val.toLocaleString();
        },
      },
      title: {
        text: "Number of Visits",
        style: {
          fontSize: "14px",
          fontWeight: 600,
          color: "#2D3748",
        },
      },
    },
    fill: {
      opacity: 1,
      type: "solid",
    },
    colors: ["#FDCA37", "#1c5b41"], // Yellow for visits made, Dark green for target
    legend: {
      show: true,
      position: "top",
      horizontalAlign: "center",
      fontSize: "14px",
      fontWeight: 600,
      fontFamily: "Inter, system-ui, sans-serif",
      markers: {
        width: 14,
        height: 14,
        radius: 2,
        offsetX: -2,
      },
      itemMargin: {
        horizontal: 20,
        vertical: 0,
      },
      labels: {
        colors: ["#374151"],
      },
    },
    tooltip: {
      enabled: true,
      shared: false,
      intersect: true,
      y: {
        formatter: function (val, { seriesIndex }) {
          const labels = ["visits made", "target"];
          return `${val.toLocaleString()} ${labels[seriesIndex]}`;
        },
      },
      style: {
        fontSize: "12px",
      },
    },
    grid: {
      borderColor: "#e5e7eb",
      strokeDashArray: 0,
      xaxis: {
        lines: {
          show: false,
        },
      },
      yaxis: {
        lines: {
          show: true,
        },
      },
      padding: {
        top: 0,
        right: 20,
        bottom: 0,
        left: 10,
      },
    },
    responsive: [
      {
        breakpoint: 768,
        options: {
          chart: {
            height: 350,
          },
          plotOptions: {
            bar: {
              columnWidth: "60%",
            },
          },
          legend: {
            fontSize: "12px",
            itemMargin: {
              horizontal: 10,
              vertical: 3,
            },
          },
        },
      },
    ],
  };

  const series = [
    {
      name: "Visits Made",
      data: visitsMadeData,
    },
    {
      name: "Target",
      data: targetData,
    },
  ];

  // Loading state - show loading if no data is provided
  if (!data) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="mb-6">
          <h3 className="text-[23px] font-semibold">
            Monthly Visits vs Targets per Officer
          </h3>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-600">Loading chart data...</div>
        </div>
      </div>
    );
  }

  return (
    <div ref={chartRef} className="bg-white rounded-xl shadow-lg p-6">
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-[23px] font-semibold">
            MTD Visits vs Targets per Officer
          </h3>
          <select
            value={selectedUser}
            onChange={(e) => setSelectedUser(parseInt(e.target.value))}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {staffData.map((staff, index) => (
              <option key={index} value={index}>
                {staff.user_name}
              </option>
            ))}
          </select>
        </div>

        {/* Summary Stats */}
        {/* <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="text-center p-3 bg-yellow-50 rounded-lg">
            <div className="text-2xl font-bold text-yellow-700">
              {animatedVisitsMade}
            </div>
            <div className="text-sm text-gray-600">Total Visits Made</div>
          </div>
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-700">
              {animatedTarget}
            </div>
            <div className="text-sm text-gray-600">Total Target</div>
          </div>
        </div> */}
      </div>

      {/* Chart container for single user */}
      <div className="w-full">
        <Chart options={chartOptions} series={series} type="bar" height={450} />
      </div>
    </div>
  );
}

export default MonthlyVisitsVsTargetsPerOfficerChart;
