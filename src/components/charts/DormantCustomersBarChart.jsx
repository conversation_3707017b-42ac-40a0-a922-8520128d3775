import React from "react";
import Chart from "react-apexcharts";

function DormantCustomersBarChart() {
  // Test data for branches
  const branches = [
    "Downtown Branch",
    "Westside Branch",
    "Eastgate Branch",
    "Northpoint Branch",
    "Southpark Branch",
    "Central Branch",
  ];

  const dormantCustomers = [287, 234, 356, 198, 289, 245];
  const contactedCustomers = [189, 156, 234, 123, 198, 167];

  const chartOptions = {
    chart: {
      type: "bar",
      height: 500,
      toolbar: {
        show: false,
      },
      animations: {
        enabled: true,
        easing: "easeinout",
        speed: 800,
        animateGradually: {
          enabled: true,
          delay: 150,
        },
        dynamicAnimation: {
          enabled: true,
          speed: 350,
        },
      },
      background: "#ffffff",
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: "60%",
        grouped: true,
        endingShape: "flat",
        borderRadius: 0,
        dataLabels: {
          position: "top",
        },
      },
    },
    dataLabels: {
      enabled: true,
      formatter: function (val) {
        return val.toLocaleString();
      },
      offsetY: -20,
      style: {
        fontSize: "12px",
        fontWeight: 600,
        colors: ["#2D3748"],
      },
    },
    stroke: {
      show: false,
      width: 0,
    },
    xaxis: {
      categories: branches,
      labels: {
        style: {
          fontSize: "14px",
          fontWeight: 600,
          colors: ["#2D3748"],
        },
      },
      axisBorder: {
        show: true,
        color: "#e5e7eb",
      },
      axisTicks: {
        show: true,
        color: "#e5e7eb",
      },
      title: {
        text: "Branches",
        style: {
          fontSize: "14px",
          fontWeight: 600,
          color: "#2D3748",
        },
      },
    },
    yaxis: {
      labels: {
        style: {
          fontSize: "14px",
          fontWeight: 600,
          colors: ["#2D3748"],
        },
      },
      title: {
        text: "Number of Customers",
        style: {
          fontSize: "14px",
          fontWeight: 600,
          color: "#2D3748",
        },
      },
    },
    fill: {
      opacity: 1,
      type: "solid",
    },
    colors: ["#F47976", "#1c5b41"], // Red for dormant, Green for contacted
    legend: {
      show: true,
      position: "top",
      horizontalAlign: "center",
      fontSize: "14px",
      fontWeight: 600,
      fontFamily: "Inter, system-ui, sans-serif",
      markers: {
        width: 14,
        height: 14,
        radius: 2,
        offsetX: -2,
      },
      itemMargin: {
        horizontal: 20,
        vertical: 0,
      },
      labels: {
        colors: ["#374151"],
      },
    },
    tooltip: {
      enabled: true,
      shared: false,
      intersect: true,
      y: {
        formatter: function (val, { seriesIndex }) {
          const labels = ["dormant customers", "contacted customers"];
          return `${val.toLocaleString()} ${labels[seriesIndex]}`;
        },
      },
      style: {
        fontSize: "12px",
      },
    },
    grid: {
      borderColor: "#e5e7eb",
      strokeDashArray: 0,
      xaxis: {
        lines: {
          show: false,
        },
      },
      yaxis: {
        lines: {
          show: true,
        },
      },
      padding: {
        top: 0,
        right: 20,
        bottom: 0,
        left: 10,
      },
    },
    responsive: [
      {
        breakpoint: 768,
        options: {
          chart: {
            height: 400,
          },
          plotOptions: {
            bar: {
              columnWidth: "70%",
            },
          },
          dataLabels: {
            style: {
              fontSize: "10px",
            },
          },
          legend: {
            fontSize: "12px",
            itemMargin: {
              horizontal: 10,
              vertical: 3,
            },
          },
        },
      },
    ],
  };

  const series = [
    {
      name: "Dormant Customers",
      data: dormantCustomers,
    },
    {
      name: "Contacted Customers",
      data: contactedCustomers,
    },
  ];

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <div className="mb-6">
        <h3 className="text-[23px] font-semibold text-gray-800 text-center mb-2">
          Dormant Customers vs Contacted
        </h3>
        <p className="text-sm text-gray-600 text-center">
          Branch-wise comparison of dormant and contacted customers
        </p>
      </div>

      <Chart options={chartOptions} series={series} type="bar" height={500} />

      {/* Summary stats */}
      <div className="mt-6 grid grid-cols-3 gap-4 pt-4 border-t border-gray-200">
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#F47976" }}>
            {dormantCustomers
              .reduce((sum, val) => sum + val, 0)
              .toLocaleString()}
          </div>
          <div className="text-xs text-gray-600">Total Dormant</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#1c5b41" }}>
            {contactedCustomers
              .reduce((sum, val) => sum + val, 0)
              .toLocaleString()}
          </div>
          <div className="text-xs text-gray-600">Total Contacted</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#374151" }}>
            {(
              (contactedCustomers.reduce((sum, val) => sum + val, 0) /
                (dormantCustomers.reduce((sum, val) => sum + val, 0) +
                  contactedCustomers.reduce((sum, val) => sum + val, 0))) *
              100
            ).toFixed(1)}
            %
          </div>
          <div className="text-xs text-gray-600">Contact Rate</div>
        </div>
      </div>
    </div>
  );
}

export default DormantCustomersBarChart;
