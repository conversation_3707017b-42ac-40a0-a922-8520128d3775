import React from "react";
import Chart from "react-apexcharts";

function Monthly2x2ActivityChart({ dashboardData }) {
  // Use passed dashboardData instead of fetching
  const loading = !dashboardData;

  // Extract completion by phase data from API response
  const completionByPhase = dashboardData?.completion_by_phase || [
    { name: "First 2", total: 0, completed: 0 },
    { name: "Second 2", total: 0, completed: 0 },
    { name: "Third 2", total: 0, completed: 0 },
  ];

  // Convert API data to chart format
  const activityData = {
    first2: {
      total:
        completionByPhase.find((phase) => phase.name === "First 2")?.total || 0,
      completed:
        completionByPhase.find((phase) => phase.name === "First 2")
          ?.completed || 0,
    },
    second2: {
      total:
        completionByPhase.find((phase) => phase.name === "Second 2")?.total ||
        0,
      completed:
        completionByPhase.find((phase) => phase.name === "Second 2")
          ?.completed || 0,
    },
    third2: {
      total:
        completionByPhase.find((phase) => phase.name === "Third 2")?.total || 0,
      completed:
        completionByPhase.find((phase) => phase.name === "Third 2")
          ?.completed || 0,
    },
  };

  const chartOptions = {
    chart: {
      type: "bar",
      height: 400,
      toolbar: {
        show: false,
      },
      animations: {
        enabled: true,
        easing: "easeinout",
        speed: 800,
        animateGradually: {
          enabled: true,
          delay: 150,
        },
        dynamicAnimation: {
          enabled: true,
          speed: 350,
        },
      },
      background: "#ffffff",
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: "60%",
        grouped: true,
        endingShape: "flat",
        borderRadius: 0,
        dataLabels: {
          position: "top",
        },
      },
    },
    dataLabels: {
      enabled: true,
      formatter: function (val) {
        return val.toLocaleString();
      },
      offsetY: -20,
      style: {
        fontSize: "12px",
        fontWeight: 600,
        colors: ["#2D3748"],
      },
    },
    stroke: {
      show: false,
      width: 0,
    },
    xaxis: {
      categories: ["First 2", "Second 2", "Third 2"],
      labels: {
        style: {
          fontSize: "14px",
          fontWeight: 600,
          colors: ["#2D3748"],
        },
      },
      axisBorder: {
        show: true,
        color: "#e5e7eb",
      },
      axisTicks: {
        show: true,
        color: "#e5e7eb",
      },
      title: {
        text: "Phase",
        style: {
          fontSize: "14px",
          fontWeight: 600,
          color: "#2D3748",
        },
      },
    },
    yaxis: {
      labels: {
        style: {
          fontSize: "14px",
          fontWeight: 600,
          colors: ["#2D3748"],
        },
      },
      title: {
        text: "Number of Activities",
        style: {
          fontSize: "14px",
          fontWeight: 600,
          color: "#2D3748",
        },
      },
    },
    fill: {
      opacity: 1,
      type: "solid",
    },
    colors: ["#82C355", "#1c5b41"], // Light green for total, Dark green for completed
    legend: {
      show: true,
      position: "top",
      horizontalAlign: "center",
      fontSize: "14px",
      fontWeight: 600,
      fontFamily: "Inter, system-ui, sans-serif",
      markers: {
        width: 14,
        height: 14,
        radius: 2,
        offsetX: -2,
      },
      itemMargin: {
        horizontal: 20,
        vertical: 0,
      },
      labels: {
        colors: ["#374151"],
      },
    },
    tooltip: {
      enabled: true,
      shared: false,
      intersect: true,
      y: {
        formatter: function (val, { seriesIndex, dataPointIndex }) {
          const phases = ["first2", "second2", "third2"];
          const currentPhase = phases[dataPointIndex];
          const phaseData = activityData[currentPhase];

          if (seriesIndex === 1) {
            // Completed activities
            const percentage = ((val / phaseData.total) * 100).toFixed(1);
            return `${val.toLocaleString()} (${percentage}% of total)`;
          }

          return val.toLocaleString();
        },
      },
      style: {
        fontSize: "12px",
      },
    },
    grid: {
      borderColor: "#e5e7eb",
      strokeDashArray: 0,
      xaxis: {
        lines: {
          show: false,
        },
      },
      yaxis: {
        lines: {
          show: true,
        },
      },
      padding: {
        top: 0,
        right: 20,
        bottom: 0,
        left: 10,
      },
    },
    responsive: [
      {
        breakpoint: 768,
        options: {
          chart: {
            height: 300,
          },
          plotOptions: {
            bar: {
              columnWidth: "70%",
            },
          },
          dataLabels: {
            style: {
              fontSize: "10px",
            },
          },
          legend: {
            fontSize: "12px",
            itemMargin: {
              horizontal: 10,
              vertical: 3,
            },
          },
        },
      },
    ],
  };

  const series = [
    {
      name: "Total Activities",
      data: [
        activityData.first2.total,
        activityData.second2.total,
        activityData.third2.total,
      ],
    },
    {
      name: "Completed Activities",
      data: [
        activityData.first2.completed,
        activityData.second2.completed,
        activityData.third2.completed,
      ],
    },
  ];

  // Loading state
  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="mb-6">
          <h3 className="text-[23px] font-semibold text-gray-800 text-center mb-2">
            Monthly 2x2x2 Activity Completion by Phase
          </h3>
          <p className="text-sm text-gray-600 text-center">
            Total vs Completed Activities
          </p>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-600">Loading chart data...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <div className="mb-6">
        <h3 className="text-[23px] font-semibold text-gray-800 text-center mb-2">
          Monthly 2x2x2 Activity Completion by Phase
        </h3>
        <p className="text-sm text-gray-600 text-center">
          Total vs Completed Activities
        </p>
      </div>

      <Chart options={chartOptions} series={series} type="bar" height={372} />

      {/* Summary stats */}
      <div className="mt-6 grid grid-cols-3 gap-4 pt-4 border-t border-gray-200">
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#1C5B41" }}>
            {activityData.first2.completed}
          </div>
          <div className="text-xs text-gray-600">First 2 Completed</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#1C5B41" }}>
            {activityData.second2.completed}
          </div>
          <div className="text-xs text-gray-600">Second 2 Completed</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#1C5B41" }}>
            {activityData.third2.completed}
          </div>
          <div className="text-xs text-gray-600">Third 2 Completed</div>
        </div>
      </div>
    </div>
  );
}

export default Monthly2x2ActivityChart;
