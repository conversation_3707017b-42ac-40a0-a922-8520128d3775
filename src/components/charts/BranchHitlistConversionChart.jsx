import React, { useState, useEffect } from "react";
import Chart from "react-apexcharts";
import { branchManagerDashboardService } from "../../services/branchManagerDashboardService";

function BranchHitlistConversionChart({ data }) {
  const [selectedTimeRange, setSelectedTimeRange] = useState("All time");

  // Extract conversion data from props
  const summaryCards = data?.summary_cards || {
    hitlist_size: { all: 0, this_month: 0 },
    converted_customers: { all: 0, this_month: 0 },
  };

  const conversionData = {
    "All time": {
      hitlist: summaryCards.hitlist_size.all,
      converted: summaryCards.converted_customers.all,
    },
    "This month": {
      hitlist: summaryCards.hitlist_size.this_month,
      converted: summaryCards.converted_customers.this_month,
    },
  };

  const currentData = conversionData[selectedTimeRange];
  const total = currentData.hitlist + currentData.converted;

  const chartOptions = {
    chart: {
      type: "donut",
      height: 350,
      animations: {
        enabled: true,
        easing: "easeinout",
        speed: 800,
        animateGradually: {
          enabled: true,
          delay: 150,
        },
      },
    },
    colors: ["#1c5b41", "#e5e7eb"], // Dark green for converted, light gray for remaining
    labels: ["Converted", "Remaining Hitlist"],
    dataLabels: {
      enabled: true,
      formatter: function (val, opts) {
        return val.toFixed(1) + "%";
      },
      style: {
        fontSize: "12px",
        fontWeight: 600,
        colors: ["#ffffff"],
      },
      dropShadow: {
        enabled: false,
      },
    },
    legend: {
      show: true,
      position: "bottom",
      horizontalAlign: "center",
      fontSize: "12px",
      fontWeight: 500,
      markers: {
        width: 10,
        height: 10,
        radius: 5,
      },
      itemMargin: {
        horizontal: 8,
        vertical: 3,
      },
      formatter: function (seriesName, opts) {
        const value = opts.w.globals.series[opts.seriesIndex];
        const actualValue =
          seriesName === "Converted"
            ? currentData.converted
            : currentData.hitlist;
        return seriesName + ": " + actualValue.toLocaleString();
      },
    },
    tooltip: {
      enabled: true,
      custom: function ({ series, seriesIndex }) {
        const values = [currentData.converted, currentData.hitlist];
        const labels = ["Converted", "Remaining Hitlist"];
        const colors = ["#1c5b41", "#e5e7eb"];

        return `
          <div style="padding: 8px 12px; background: white; border-radius: 4px; box-shadow: 0 2px 8px rgba(0,0,0,0.15); font-size: 14px;">
            <span style="color: ${colors[seriesIndex]};">
              ${labels[seriesIndex]}: ${values[seriesIndex]} customers (${series[seriesIndex]}%)
            </span>
          </div>
        `;
      },
    },
    stroke: {
      show: true,
      width: 2,
      colors: ["#ffffff"],
    },
    states: {
      hover: {
        filter: {
          type: "none",
        },
      },
    },
    plotOptions: {
      pie: {
        donut: {
          size: "60%",
          labels: {
            show: true,
            name: {
              show: true,
              fontSize: "14px",
              fontWeight: 600,
              color: "#374151",
            },
            value: {
              show: true,
              fontSize: "16px",
              fontWeight: 700,
              color: "#1c5b41",
              formatter: function (val) {
                return val;
              },
            },
            total: {
              show: true,
              showAlways: true,
              label: "Total",
              fontSize: "14px",
              fontWeight: 600,
              color: "#6B7280",
              formatter: function (w) {
                return total.toLocaleString();
              },
            },
          },
        },
      },
    },
    responsive: [
      {
        breakpoint: 768,
        options: {
          chart: {
            height: 300,
          },
          legend: {
            fontSize: "10px",
            itemMargin: {
              horizontal: 5,
              vertical: 2,
            },
          },
          dataLabels: {
            style: {
              fontSize: "10px",
            },
          },
        },
      },
    ],
  };

  const series = [
    ((currentData.converted / total) * 100).toFixed(1),
    ((currentData.hitlist / total) * 100).toFixed(1),
  ];

  // Loading state - show loading if no data is provided
  if (!data) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="mb-4">
          <h3 className="text-[23px] font-semibold text-gray-800 mb-2">
            Hitlist to Conversion Overview
          </h3>
          <p className="text-sm text-gray-600">Conversion rate from hitlist</p>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-600">Loading chart data...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <div className="mb-4">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h3 className="text-[23px] font-semibold text-gray-800 mb-2">
              Hitlist to Conversion Overview
            </h3>
            <p className="text-sm text-gray-600">
              Conversion rate from hitlist
            </p>
          </div>

          {/* Time Range Filter */}
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-gray-700">
              Time Range:
            </label>
            <select
              value={selectedTimeRange}
              onChange={(e) => setSelectedTimeRange(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="All time">All time</option>
              <option value="This month">This month</option>
            </select>
          </div>
        </div>
      </div>

      <Chart
        options={chartOptions}
        series={series.map(Number)}
        type="donut"
        height={350}
      />

      {/* Summary stats */}
      <div className="mt-4 grid grid-cols-2 gap-4 pt-4 border-t border-gray-200">
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#1c5b41" }}>
            {total > 0 ? ((currentData.converted / total) * 100).toFixed(1) : 0}
            %
          </div>
          <div className="text-xs text-gray-600">Conversion Rate</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#374151" }}>
            {total.toLocaleString()}
          </div>
          <div className="text-xs text-gray-600">Total Leads</div>
        </div>
      </div>
    </div>
  );
}

export default BranchHitlistConversionChart;
