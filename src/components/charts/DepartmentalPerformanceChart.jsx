import React, { useState } from "react";
import Chart from "react-apexcharts";

function DepartmentalPerformanceChart() {
  const [selectedBranch, setSelectedBranch] = useState("Downtown Branch");

  // Test data for branches and departments
  const branches = [
    "Downtown Branch",
    "Westside Branch",
    "Eastgate Branch",
    "Northpoint Branch",
    "Southpark Branch",
    "Central Branch",
  ];

  const departments = ["Leads", "Customer Service", "Loans"];

  // Test data for calls and visits by branch and department
  const branchData = {
    "Downtown Branch": {
      calls: [245, 189, 167],
      visits: [89, 0, 45], // Customer Service visits = 0
    },
    "Westside Branch": {
      calls: [198, 156, 134],
      visits: [67, 0, 38],
    },
    "Eastgate Branch": {
      calls: [289, 234, 201],
      visits: [98, 0, 56],
    },
    "Northpoint Branch": {
      calls: [167, 145, 123],
      visits: [54, 0, 32],
    },
    "Southpark Branch": {
      calls: [223, 178, 156],
      visits: [76, 0, 41],
    },
    "Central Branch": {
      calls: [201, 167, 145],
      visits: [69, 0, 37],
    },
  };

  const currentData = branchData[selectedBranch];

  const chartOptions = {
    chart: {
      type: "bar",
      height: 400,
      toolbar: {
        show: false,
      },
      animations: {
        enabled: true,
        easing: "easeinout",
        speed: 800,
        animateGradually: {
          enabled: true,
          delay: 150,
        },
        dynamicAnimation: {
          enabled: true,
          speed: 350,
        },
      },
      background: "#ffffff",
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: "60%",
        grouped: true,
        endingShape: "flat",
        borderRadius: 0,
        dataLabels: {
          position: "top",
        },
      },
    },
    dataLabels: {
      enabled: true,
      formatter: function (val) {
        return val.toLocaleString();
      },
      offsetY: -20,
      style: {
        fontSize: "12px",
        fontWeight: 600,
        colors: ["#2D3748"],
      },
    },
    stroke: {
      show: false,
      width: 0,
    },
    xaxis: {
      categories: departments,
      labels: {
        style: {
          fontSize: "14px",
          fontWeight: 600,
          colors: ["#2D3748"],
        },
      },
      axisBorder: {
        show: true,
        color: "#e5e7eb",
      },
      axisTicks: {
        show: true,
        color: "#e5e7eb",
      },
      title: {
        text: "Departments",
        style: {
          fontSize: "14px",
          fontWeight: 600,
          color: "#2D3748",
        },
      },
    },
    yaxis: {
      labels: {
        style: {
          fontSize: "14px",
          fontWeight: 600,
          colors: ["#2D3748"],
        },
      },
      title: {
        text: "Number of Activities",
        style: {
          fontSize: "14px",
          fontWeight: 600,
          color: "#2D3748",
        },
      },
    },
    fill: {
      opacity: 1,
      type: "solid",
    },
    colors: ["#82C355", "#1c5b41"], // Light green for calls, Dark green for visits
    legend: {
      show: true,
      position: "top",
      horizontalAlign: "center",
      fontSize: "14px",
      fontWeight: 600,
      fontFamily: "Inter, system-ui, sans-serif",
      markers: {
        width: 14,
        height: 14,
        radius: 2,
        offsetX: -2,
      },
      itemMargin: {
        horizontal: 20,
        vertical: 0,
      },
      labels: {
        colors: ["#374151"],
      },
    },
    tooltip: {
      enabled: true,
      shared: false,
      intersect: true,
      y: {
        formatter: function (val, { seriesIndex }) {
          const labels = ["calls", "visits"];
          return `${val.toLocaleString()} ${labels[seriesIndex]}`;
        },
      },
      style: {
        fontSize: "12px",
      },
    },
    grid: {
      borderColor: "#e5e7eb",
      strokeDashArray: 0,
      xaxis: {
        lines: {
          show: false,
        },
      },
      yaxis: {
        lines: {
          show: true,
        },
      },
      padding: {
        top: 0,
        right: 20,
        bottom: 0,
        left: 10,
      },
    },
    responsive: [
      {
        breakpoint: 768,
        options: {
          chart: {
            height: 300,
          },
          plotOptions: {
            bar: {
              columnWidth: "70%",
            },
          },
          dataLabels: {
            style: {
              fontSize: "10px",
            },
          },
          legend: {
            fontSize: "12px",
            itemMargin: {
              horizontal: 10,
              vertical: 3,
            },
          },
        },
      },
    ],
  };

  const series = [
    {
      name: "Calls Made",
      data: currentData.calls,
    },
    {
      name: "Visits Made",
      data: currentData.visits,
    },
  ];

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h3 className="text-[23px] font-semibold text-gray-800 mb-2">
              Branches Calls & Visits this month
            </h3>
            <p className="text-sm text-gray-600">
              Performance breakdown by department for selected branch
            </p>
          </div>

          {/* Branch Filter Dropdown */}
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-gray-700">Branch:</label>
            <select
              value={selectedBranch}
              onChange={(e) => setSelectedBranch(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {branches.map((branch) => (
                <option key={branch} value={branch}>
                  {branch}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      <Chart options={chartOptions} series={series} type="bar" height={400} />

      {/* Summary stats */}
      <div className="mt-6 grid grid-cols-3 gap-4 pt-4 border-t border-gray-200">
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#82C355" }}>
            {currentData.calls
              .reduce((sum, val) => sum + val, 0)
              .toLocaleString()}
          </div>
          <div className="text-xs text-gray-600">Total Calls</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#1c5b41" }}>
            {currentData.visits
              .reduce((sum, val) => sum + val, 0)
              .toLocaleString()}
          </div>
          <div className="text-xs text-gray-600">Total Visits</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#374151" }}>
            {selectedBranch.split(" ")[0]}
          </div>
          <div className="text-xs text-gray-600">Selected Branch</div>
        </div>
      </div>
    </div>
  );
}

export default DepartmentalPerformanceChart;
