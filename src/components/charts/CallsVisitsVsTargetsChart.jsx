import React, { useState, useEffect } from "react";
import Chart from "react-apexcharts";
import { branchManagerDashboardService } from "../../services/branchManagerDashboardService";

function CallsVisitsVsTargetsChart({ data }) {
  // Extract calls visits vs targets data from props
  const targetsData = data?.calls_visits_vs_target || {
    actual: { name: "Actual", data: [0, 0] },
    target: { name: "Target", data: [0, 0] },
  };

  const categories = ["Calls", "Visits"];

  const chartOptions = {
    chart: {
      type: "bar",
      height: 400,
      toolbar: {
        show: false,
      },
      animations: {
        enabled: true,
        easing: "easeinout",
        speed: 800,
        animateGradually: {
          enabled: true,
          delay: 150,
        },
      },
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: "60%",
        endingShape: "rounded",
        borderRadius: 4,
        dataLabels: {
          position: "top",
        },
      },
    },
    dataLabels: {
      enabled: true,
      offsetY: -20,
      style: {
        fontSize: "12px",
        fontWeight: 600,
        colors: ["#2D3748"],
      },
    },
    stroke: {
      show: true,
      width: 2,
      colors: ["transparent"],
    },
    xaxis: {
      categories: categories,
      labels: {
        style: {
          fontSize: "12px",
          fontWeight: 600,
          colors: ["#2D3748"],
        },
      },
      axisBorder: {
        show: true,
        color: "#e5e7eb",
      },
      axisTicks: {
        show: true,
        color: "#e5e7eb",
      },
    },
    yaxis: {
      labels: {
        style: {
          fontSize: "12px",
          fontWeight: 600,
          colors: ["#2D3748"],
        },
      },
      title: {
        text: "Count",
        style: {
          fontSize: "14px",
          fontWeight: 600,
          color: "#2D3748",
        },
      },
    },
    fill: {
      opacity: 1,
      type: "solid",
    },
    colors: ["#82C355", "#1c5b41"], // Light green for actual, Dark green for target
    legend: {
      show: true,
      position: "top",
      horizontalAlign: "center",
      fontSize: "14px",
      fontWeight: 600,
      fontFamily: "Inter, system-ui, sans-serif",
      markers: {
        width: 14,
        height: 14,
        radius: 2,
        offsetX: -2,
      },
      itemMargin: {
        horizontal: 20,
        vertical: 0,
      },
      labels: {
        colors: ["#374151"],
      },
    },
    tooltip: {
      enabled: true,
      shared: false,
      intersect: true,
      y: {
        formatter: function (val, { seriesIndex }) {
          const labels = ["actual", "target"];
          return `${val.toLocaleString()} ${labels[seriesIndex]}`;
        },
      },
      style: {
        fontSize: "12px",
      },
    },
    grid: {
      borderColor: "#e5e7eb",
      strokeDashArray: 0,
      xaxis: {
        lines: {
          show: false,
        },
      },
      yaxis: {
        lines: {
          show: true,
        },
      },
      padding: {
        top: 0,
        right: 20,
        bottom: 0,
        left: 10,
      },
    },
    responsive: [
      {
        breakpoint: 768,
        options: {
          chart: {
            height: 300,
          },
          plotOptions: {
            bar: {
              columnWidth: "70%",
            },
          },
          legend: {
            fontSize: "12px",
            itemMargin: {
              horizontal: 10,
              vertical: 3,
            },
          },
        },
      },
    ],
  };

  const series = [
    {
      name: targetsData.actual.name,
      data: targetsData.actual.data,
    },
    {
      name: targetsData.target.name,
      data: targetsData.target.data,
    },
  ];

  // Loading state - show loading if no data is provided
  if (!data) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="mb-6">
          <h3 className="text-[23px] font-semibold text-gray-800 mb-2">
            Calls & Visits vs Targets this month
          </h3>
          <p className="text-sm text-gray-600">
            Performance against monthly targets
          </p>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-600">Loading chart data...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <div className="mb-6">
        <div className="mb-4">
          <h3 className="text-[23px] font-semibold text-gray-800 mb-2">
            Calls & Visits vs Targets MTD
          </h3>
          <p className="text-sm text-gray-600">
            Performance against monthly targets
          </p>
        </div>
      </div>

      <Chart options={chartOptions} series={series} type="bar" height={400} />

      {/* Summary stats */}
      <div className="mt-6 grid grid-cols-2 gap-4 pt-4 border-t border-gray-200">
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#82C355" }}>
            {targetsData.actual.data
              .reduce((sum, val) => sum + val, 0)
              .toLocaleString()}
          </div>
          <div className="text-xs text-gray-600">Total Actual</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#1c5b41" }}>
            {targetsData.target.data
              .reduce((sum, val) => sum + val, 0)
              .toLocaleString()}
          </div>
          <div className="text-xs text-gray-600">Total Target</div>
        </div>
      </div>
    </div>
  );
}

export default CallsVisitsVsTargetsChart;
