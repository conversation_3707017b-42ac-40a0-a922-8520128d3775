import React, { useState } from "react";
import Chart from "react-apexcharts";

function RegionalHitlistChart() {
  const [selectedDuration, setSelectedDuration] = useState("General");

  // Test data for branches
  const branches = [
    "Downtown Branch",
    "Westside Branch",
    "Eastgate Branch",
    "Northpoint Branch",
    "Southpark Branch",
    "Central Branch",
  ];

  // Test data for different durations
  const chartData = {
    General: {
      hitlistSizes: [485, 423, 567, 389, 512, 471],
      customersConverted: [89, 76, 134, 67, 98, 82],
    },
    "This month": {
      hitlistSizes: [67, 54, 89, 45, 72, 61],
      customersConverted: [12, 8, 18, 7, 14, 11],
    },
  };

  const currentData = chartData[selectedDuration];

  const chartOptions = {
    chart: {
      type: "bar",
      height: 500,
      toolbar: {
        show: false,
      },
      animations: {
        enabled: true,
        easing: "easeinout",
        speed: 800,
        animateGradually: {
          enabled: true,
          delay: 150,
        },
        dynamicAnimation: {
          enabled: true,
          speed: 350,
        },
      },
      background: "#ffffff",
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: "75%",
        grouped: true,
        endingShape: "flat",
        borderRadius: 0,
        dataLabels: {
          position: "top",
        },
      },
    },
    dataLabels: {
      enabled: true,
      formatter: function (val) {
        return val.toLocaleString();
      },
      offsetY: -20,
      style: {
        fontSize: "12px",
        fontWeight: 600,
        colors: ["#2D3748"],
      },
    },
    stroke: {
      show: false,
      width: 0,
    },
    xaxis: {
      categories: branches,
      labels: {
        style: {
          fontSize: "14px",
          fontWeight: 600,
          colors: ["#2D3748"],
        },
      },
      axisBorder: {
        show: true,
        color: "#e5e7eb",
      },
      axisTicks: {
        show: true,
        color: "#e5e7eb",
      },
      title: {
        text: "Branches",
        style: {
          fontSize: "14px",
          fontWeight: 600,
          color: "#2D3748",
        },
      },
    },
    yaxis: {
      labels: {
        style: {
          fontSize: "14px",
          fontWeight: 600,
          colors: ["#2D3748"],
        },
      },
      title: {
        text: "Number of Leads/Customers",
        style: {
          fontSize: "14px",
          fontWeight: 600,
          color: "#2D3748",
        },
      },
    },
    fill: {
      opacity: 1,
      type: "solid",
    },
    colors: ["#82C355", "#1c5b41"], // Light green for hitlist, Dark green for converted
    legend: {
      show: true,
      position: "top",
      horizontalAlign: "center",
      fontSize: "14px",
      fontWeight: 600,
      fontFamily: "Inter, system-ui, sans-serif",
      markers: {
        width: 14,
        height: 14,
        radius: 2,
        offsetX: -2,
      },
      itemMargin: {
        horizontal: 20,
        vertical: 0,
      },
      labels: {
        colors: ["#374151"],
      },
    },
    tooltip: {
      enabled: true,
      shared: false,
      intersect: true,
      y: {
        formatter: function (val, { seriesIndex }) {
          const labels = ["leads", "customers"];
          return `${val.toLocaleString()} ${labels[seriesIndex]}`;
        },
      },
      style: {
        fontSize: "12px",
      },
    },
    grid: {
      borderColor: "#e5e7eb",
      strokeDashArray: 0,
      xaxis: {
        lines: {
          show: false,
        },
      },
      yaxis: {
        lines: {
          show: true,
        },
      },
      padding: {
        top: 0,
        right: 20,
        bottom: 0,
        left: 10,
      },
    },
    responsive: [
      {
        breakpoint: 768,
        options: {
          chart: {
            height: 400,
          },
          plotOptions: {
            bar: {
              barHeight: "60%",
            },
          },
          dataLabels: {
            style: {
              fontSize: "10px",
            },
          },
          legend: {
            fontSize: "12px",
            itemMargin: {
              horizontal: 10,
              vertical: 3,
            },
          },
        },
      },
    ],
  };

  const series = [
    {
      name: "Hitlist Size",
      data: currentData.hitlistSizes,
    },
    {
      name: "Customers Converted",
      data: currentData.customersConverted,
    },
  ];

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h3 className="text-[23px] font-semibold text-gray-800 mb-2">
              Hitlist to Conversion Overview
            </h3>
            <p className="text-sm text-gray-600">
              Hitlist Size vs Customer Conversions by Branch
            </p>
          </div>

          {/* Duration Filter Dropdown */}
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-gray-700">
              Time Duration:
            </label>
            <select
              value={selectedDuration}
              onChange={(e) => setSelectedDuration(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="General">All time</option>
              <option value="This month">This month</option>
            </select>
          </div>
        </div>
      </div>

      <Chart options={chartOptions} series={series} type="bar" height={500} />

      {/* Summary stats */}
      <div className="mt-6 grid grid-cols-3 gap-4 pt-4 border-t border-gray-200">
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#82C355" }}>
            {currentData.hitlistSizes
              .reduce((sum, val) => sum + val, 0)
              .toLocaleString()}
          </div>
          <div className="text-xs text-gray-600">Total Hitlist Size</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#1c5b41" }}>
            {currentData.customersConverted
              .reduce((sum, val) => sum + val, 0)
              .toLocaleString()}
          </div>
          <div className="text-xs text-gray-600">Total Converted</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#374151" }}>
            {(
              (currentData.customersConverted.reduce(
                (sum, val) => sum + val,
                0
              ) /
                currentData.hitlistSizes.reduce((sum, val) => sum + val, 0)) *
              100
            ).toFixed(1)}
            %
          </div>
          <div className="text-xs text-gray-600">Conversion Rate</div>
        </div>
      </div>
    </div>
  );
}

export default RegionalHitlistChart;
