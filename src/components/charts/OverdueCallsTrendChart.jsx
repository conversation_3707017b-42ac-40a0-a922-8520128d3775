import React from "react";
import Chart from "react-apexcharts";

function OverdueCallsTrendChart({ dashboardData }) {
  // Use passed dashboardData instead of fetching
  const loading = !dashboardData;

  // Extract overdue calls trend data from API response
  const overdueCallsTrend = dashboardData?.overdue_calls_trend || [
    { month: "January 2025", value: null },
    { month: "February 2025", value: null },
    { month: "March 2025", value: null },
    { month: "April 2025", value: null },
    { month: "May 2025", value: null },
    { month: "June 2025", value: null },
  ];

  // Extract months and data from API response
  const months = overdueCallsTrend.map((item) => {
    if (!item.month) return "N/A";
    const date = new Date(item.month);
    return date.toLocaleDateString("en-US", { month: "short" });
  });
  const overdueCallsData = overdueCallsTrend.map((item) =>
    item.value === null ? 0 : item.value
  );

  const chartOptions = {
    chart: {
      type: "area",
      height: 400,
      toolbar: {
        show: false,
      },
      animations: {
        enabled: true,
        easing: "easeinout",
        speed: 800,
        animateGradually: {
          enabled: true,
          delay: 150,
        },
        dynamicAnimation: {
          enabled: true,
          speed: 350,
        },
      },
      background: "#ffffff",
    },
    stroke: {
      curve: "smooth",
      width: 3,
    },
    fill: {
      type: "gradient",
      gradient: {
        shadeIntensity: 1,
        opacityFrom: 0.7,
        opacityTo: 0.3,
        stops: [0, 90, 100],
      },
    },
    dataLabels: {
      enabled: true,
      formatter: function (val) {
        return val !== null && val !== undefined ? val.toString() : "0";
      },
      style: {
        fontSize: "12px",
        fontWeight: 600,
        colors: ["#2D3748"],
      },
      background: {
        enabled: true,
        foreColor: "#fff",
        borderRadius: 2,
        padding: 4,
        opacity: 0.9,
        borderWidth: 1,
        borderColor: "#fff",
        dropShadow: {
          enabled: true,
          top: 1,
          left: 1,
          blur: 1,
          color: "#000",
          opacity: 0.45,
        },
      },
    },
    xaxis: {
      categories: months,
      labels: {
        style: {
          fontSize: "14px",
          fontWeight: 600,
          colors: ["#2D3748"],
        },
      },
      axisBorder: {
        show: true,
        color: "#e5e7eb",
      },
      axisTicks: {
        show: true,
        color: "#e5e7eb",
      },
      title: {
        text: "Month",
        style: {
          fontSize: "14px",
          fontWeight: 600,
          color: "#2D3748",
        },
      },
    },
    yaxis: {
      labels: {
        style: {
          fontSize: "14px",
          fontWeight: 600,
          colors: ["#2D3748"],
        },
      },
      title: {
        text: "Number of Overdue Calls",
        style: {
          fontSize: "14px",
          fontWeight: 600,
          color: "#2D3748",
        },
      },
    },
    colors: ["#F47976"], // Red color for overdue calls
    tooltip: {
      enabled: true,
      y: {
        formatter: function (val) {
          return `${val} overdue calls`;
        },
      },
      style: {
        fontSize: "12px",
      },
    },
    grid: {
      borderColor: "#e5e7eb",
      strokeDashArray: 0,
      xaxis: {
        lines: {
          show: false,
        },
      },
      yaxis: {
        lines: {
          show: true,
        },
      },
      padding: {
        top: 0,
        right: 20,
        bottom: 0,
        left: 10,
      },
    },
    responsive: [
      {
        breakpoint: 768,
        options: {
          chart: {
            height: 300,
          },
          dataLabels: {
            style: {
              fontSize: "10px",
            },
          },
        },
      },
    ],
  };

  const series = [
    {
      name: "Overdue Calls",
      data: overdueCallsData,
    },
  ];

  // Filter out null values for calculations
  const validData = overdueCallsData.filter(
    (val) => val !== null && val !== undefined
  );

  // Loading state
  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="mb-6">
          <h3 className="text-[23px] font-semibold text-gray-800 text-center mb-2">
            Overdue Calls Trend
          </h3>
          <p className="text-sm text-gray-600 text-center">
            Past 6 Months Overview
          </p>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-600">Loading chart data...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <div className="mb-6">
        <h3 className="text-[23px] font-semibold text-gray-800 text-center mb-2">
          Overdue Calls Trend
        </h3>
        <p className="text-sm text-gray-600 text-center">
          Past 6 Months Overview
        </p>
      </div>

      <Chart options={chartOptions} series={series} type="area" height={370} />

      {/* Summary stats */}
      <div className="mt-6 grid grid-cols-3 gap-4 pt-4 border-t border-gray-200">
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#F47976" }}>
            {validData.length > 0 ? Math.max(...validData) : 0}
          </div>
          <div className="text-xs text-gray-600">Peak Month</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#F47976" }}>
            {validData.length > 0
              ? Math.round(
                  validData.reduce((sum, val) => sum + val, 0) /
                    validData.length
                )
              : 0}
          </div>
          <div className="text-xs text-gray-600">Average</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#F47976" }}>
            {overdueCallsData[overdueCallsData.length - 1] || 0}
          </div>
          <div className="text-xs text-gray-600">This Month</div>
        </div>
      </div>
    </div>
  );
}

export default OverdueCallsTrendChart;
