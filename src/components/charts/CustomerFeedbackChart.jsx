import React from "react";
import Chart from "react-apexcharts";

function CustomerFeedbackChart({ dashboardData }) {
  // Use passed dashboardData instead of fetching
  const loading = !dashboardData;

  // Extract monthly customer feedback data from API response
  const monthlyFeedback = dashboardData?.monthly_customer_feedback || [
    { name: "Positive", value: 0 },
    { name: "Negative", value: 0 },
  ];

  // Generate colors for feedback categories with 14 unique color codes
  const generateColors = (feedbackData) => {
    // 14 distinct, visually appealing colors for dynamic assignment
    const colorPalette = [
      "#1c5b41", // Dark Green
      "#3B82F6", // Blue
      "#10B981", // Emerald
      "#F59E0B", // Amber
      "#8B5CF6", // Purple
      "#EF4444", // Red
      "#06B6D4", // Cyan
      "#84CC16", // Lime
      "#F97316", // Orange
      "#EC4899", // Pink
      "#6366F1", // Indigo
      "#14B8A6", // Teal
      "#A855F7", // Violet
      "#6B7280", // Gray
    ];

    // Specific color mapping for known feedback types (optional)
    const specificColorMap = {
      "Warm and friendly": "#1c5b41", // Dark Green
      Professional: "#3B82F6", // Blue
      "Cold and harsh": "#6B7280", // Gray instead of red
      Helpful: "#10B981", // Emerald
      Unhelpful: "#F59E0B", // Amber
      Positive: "#1c5b41", // Dark Green
      Negative: "#EF4444", // Red
      Neutral: "#6B7280", // Gray
    };

    return feedbackData.map((item, index) => {
      // First check if there's a specific color for this feedback type
      if (specificColorMap[item.name]) {
        return specificColorMap[item.name];
      }
      // Otherwise, assign colors from palette cyclically
      return colorPalette[index % colorPalette.length];
    });
  };

  // Convert API data to chart format
  const feedbackData = {};
  monthlyFeedback.forEach((item) => {
    feedbackData[item.name.toLowerCase().replace(/\s+/g, "")] = item.value;
  });

  const total = Object.values(feedbackData).reduce(
    (sum, value) => sum + value,
    0
  );

  const chartOptions = {
    chart: {
      type: "pie",
      height: 400,
      animations: {
        enabled: true,
        easing: "easeinout",
        speed: 800,
        animateGradually: {
          enabled: true,
          delay: 150,
        },
      },
    },
    colors: generateColors(monthlyFeedback), // Dynamic colors based on feedback types
    labels: monthlyFeedback.map((item) => item.name),
    dataLabels: {
      enabled: true,
      formatter: function (val, opts) {
        return val.toFixed(1) + "%";
      },
      style: {
        fontSize: "12px",
        fontWeight: 600,
        colors: ["#ffffff"],
      },
      dropShadow: {
        enabled: false,
      },
    },
    legend: {
      show: true,
      position: "bottom",
      horizontalAlign: "center",
      fontSize: "12px",
      fontWeight: 500,
      markers: {
        width: 10,
        height: 10,
        radius: 5,
      },
      itemMargin: {
        horizontal: 8,
        vertical: 3,
      },
      formatter: function (seriesName, opts) {
        const value = opts.w.globals.series[opts.seriesIndex];
        const actualValue = Object.values(feedbackData)[opts.seriesIndex];
        return seriesName + ": " + actualValue;
      },
    },
    tooltip: {
      enabled: true,
      y: {
        formatter: function (val, { seriesIndex }) {
          const actualValue = Object.values(feedbackData)[seriesIndex];
          return `${actualValue} responses (${val.toFixed(1)}%)`;
        },
      },
      style: {
        fontSize: "12px",
      },
    },
    stroke: {
      show: true,
      width: 1,
      colors: ["#ffffff"],
    },
    responsive: [
      {
        breakpoint: 768,
        options: {
          chart: {
            height: 250,
          },
          legend: {
            fontSize: "10px",
            itemMargin: {
              horizontal: 5,
              vertical: 2,
            },
          },
          dataLabels: {
            style: {
              fontSize: "10px",
            },
          },
        },
      },
    ],
  };

  const series = monthlyFeedback.map((item) => item.value);

  // Loading state
  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="mb-4">
          <h3 className="text-[23px] font-semibold text-gray-800 text-center mb-2">
            Monthly Customer Feedback
          </h3>
          <p className="text-sm text-gray-600 text-center">
            Categories This Month
          </p>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-600">Loading chart data...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <div className="mb-4">
        <h3 className="text-[23px] font-semibold text-gray-800 text-center mb-2">
          Monthly Customer Feedback
        </h3>
        <p className="text-sm text-gray-600 text-center">
          Categories This Month
        </p>
      </div>

      <Chart options={chartOptions} series={series} type="pie" height={432} />

      {/* Summary stats */}
      <div className="mt-4 grid grid-cols-2 gap-4 pt-4 border-0 border-gray-200 opacity-0">
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#1c5b41" }}>
            {feedbackData.promiseToReactivate}
          </div>
          <div className="text-xs text-gray-600">Promise to Reactivate</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#3B82F6" }}>
            {feedbackData.notInterested}
          </div>
          <div className="text-xs text-gray-600">Not Interested</div>
        </div>
      </div>
    </div>
  );
}

export default CustomerFeedbackChart;
