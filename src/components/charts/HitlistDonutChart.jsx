import React from "react";
import Chart from "react-apexcharts";

function HitlistDonutChart() {
  // Sample data for hitlist
  const totalHitlist = 247;
  const handledHitlist = 156;
  const remainingHitlist = totalHitlist - handledHitlist;

  const chartOptions = {
    chart: {
      type: "donut",
      height: 374,
      animations: {
        enabled: true,
        easing: "easeinout",
        speed: 800,
        animateGradually: {
          enabled: true,
          delay: 50,
        },
      },
    },
    plotOptions: {
      pie: {
        donut: {
          size: "70%",
          labels: {
            show: true,
            name: {
              show: true,
              fontSize: "16px",
              fontWeight: 600,
              color: "#374151",
              offsetY: -10,
            },
            value: {
              show: true,
              fontSize: "24px",
              fontWeight: 700,
              color: "#1f2937",
              offsetY: 10,
              formatter: function (val) {
                return val + "%";
              },
            },
            total: {
              show: true,
              showAlways: true,
              label: "Contacted",
              fontSize: "14px",
              fontWeight: 600,
              color: "#6b7280",
              formatter: function (w) {
                const percentage = (
                  (handledHitlist / totalHitlist) *
                  100
                ).toFixed(1);
                return percentage + "%";
              },
            },
          },
        },
      },
    },
    colors: ["#1c5b41", "#82C355"], // Dark green for handled, light green for remaining
    labels: ["Contacted", "Remaining"],
    dataLabels: {
      enabled: false,
    },
    legend: {
      show: true,
      position: "bottom",
      horizontalAlign: "center",
      fontSize: "14px",
      fontWeight: 500,
      markers: {
        width: 12,
        height: 12,
        radius: 6,
      },
      itemMargin: {
        horizontal: 15,
        vertical: 5,
      },
    },
    tooltip: {
      enabled: true,
      custom: function ({ series, seriesIndex }) {
        const values = [handledHitlist, remainingHitlist];
        const labels = ["Contacted", "Remaining"];
        const colors = ["#1c5b41", "#000000"];

        return `
          <div style="padding: 8px 12px; background: white; border-radius: 4px; box-shadow: 0 2px 8px rgba(0,0,0,0.15); font-size: 14px;">
            <span style="color: ${colors[seriesIndex]};">
              ${labels[seriesIndex]}: ${values[seriesIndex]} prospects (${series[seriesIndex]}%)
            </span>
          </div>
        `;
      },
    },
    stroke: {
      show: false,
    },
    states: {
      hover: {
        filter: {
          type: "none",
        },
      },
    },
    responsive: [
      {
        breakpoint: 768,
        options: {
          chart: {
            height: 250,
          },
          plotOptions: {
            pie: {
              donut: {
                size: "65%",
              },
            },
          },
        },
      },
    ],
  };

  const series = [
    ((handledHitlist / totalHitlist) * 100).toFixed(1),
    ((remainingHitlist / totalHitlist) * 100).toFixed(1),
  ];

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <div className="mb-4">
        <h3 className="text-[23px] font-semibold text-gray-800 text-center mb-2">
          Monthly Hitlist Progress
        </h3>
        <p className="text-sm text-gray-600 text-center">
          Contacted vs Remaining
        </p>
      </div>

      <Chart
        options={chartOptions}
        series={series.map(Number)}
        type="donut"
        height={374}
      />

      {/* Summary stats */}
      <div className="mt-4 grid grid-cols-2 gap-4 pt-4 border-t border-gray-200">
        <div className="text-center">
          <div className="text-xl font-bold" style={{ color: "#1c5b41" }}>
            {handledHitlist}
          </div>
          <div className="text-xs text-gray-600">Contacted</div>
        </div>
        <div className="text-center">
          <div className="text-xl font-bold text-gray-500">
            {remainingHitlist}
          </div>
          <div className="text-xs text-gray-600">Remaining</div>
        </div>
      </div>
    </div>
  );
}

export default HitlistDonutChart;
