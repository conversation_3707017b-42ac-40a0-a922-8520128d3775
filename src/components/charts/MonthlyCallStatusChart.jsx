import React from "react";
import Chart from "react-apexcharts";

function MonthlyCallStatusChart({ dashboardData }) {
  // Use passed dashboardData instead of fetching
  const loading = !dashboardData;

  // Extract monthly call status data from API response
  const monthlyCallStatus = dashboardData?.monthly_call_status || [
    { name: "Success", value: 0 },
    { name: "No answer", value: 0 },
  ];

  const successCalls =
    monthlyCallStatus.find((status) => status.name === "Success")?.value || 0;
  const noAnswerCalls =
    monthlyCallStatus.find((status) => status.name === "No answer")?.value || 0;
  const totalCalls = successCalls + noAnswerCalls;

  const chartOptions = {
    chart: {
      type: "bar",
      height: 400,
      toolbar: {
        show: false,
      },
      animations: {
        enabled: true,
        easing: "easeinout",
        speed: 800,
        animateGradually: {
          enabled: true,
          delay: 150,
        },
        dynamicAnimation: {
          enabled: true,
          speed: 350,
        },
      },
      background: "#ffffff",
    },
    plotOptions: {
      bar: {
        horizontal: true,
        barHeight: "80%",
        stacked: true,
        endingShape: "flat",
        borderRadius: 0,
        dataLabels: {
          position: "center",
        },
      },
    },
    dataLabels: {
      enabled: true,
      formatter: function (val) {
        return val.toLocaleString();
      },
      offsetX: 10,
      style: {
        fontSize: "15px",
        fontWeight: 600,
        colors: ["#ffffff"],
      },
    },
    stroke: {
      show: false,
      width: 0,
    },
    xaxis: {
      categories: ["This Month"],
      labels: {
        style: {
          fontSize: "14px",
          fontWeight: 600,
          colors: ["#2D3748"],
        },
      },
      axisBorder: {
        show: true,
        color: "#e5e7eb",
      },
      axisTicks: {
        show: true,
        color: "#e5e7eb",
      },
      title: {
        text: "Number of Calls",
        style: {
          fontSize: "14px",
          fontWeight: 600,
          color: "#2D3748",
        },
      },
    },
    yaxis: {
      labels: {
        style: {
          fontSize: "14px",
          fontWeight: 600,
          colors: ["#2D3748"],
        },
      },
      title: {
        // text: "Call Type",
        style: {
          fontSize: "14px",
          fontWeight: 600,
          color: "#2D3748",
        },
        offsetX: -10,
      },
    },
    fill: {
      opacity: 1,
      type: "solid",
    },
    colors: ["#82C355", "#1c5b41"], // Light green for success, Dark green for no answer
    legend: {
      show: true,
      position: "top",
      horizontalAlign: "center",
      fontSize: "14px",
      fontWeight: 600,
      fontFamily: "Inter, system-ui, sans-serif",
      markers: {
        width: 14,
        height: 14,
        radius: 2,
        offsetX: -2,
      },
      itemMargin: {
        horizontal: 20,
        vertical: 0,
      },
      labels: {
        colors: ["#374151"],
      },
    },
    tooltip: {
      enabled: true,
      shared: false,
      intersect: true,
      y: {
        formatter: function (val, { seriesIndex, dataPointIndex }) {
          const percentage = ((val / totalCalls) * 100).toFixed(1);
          return `${val.toLocaleString()} calls (${percentage}%)`;
        },
      },
      style: {
        fontSize: "12px",
      },
    },
    grid: {
      borderColor: "#e5e7eb",
      strokeDashArray: 0,
      xaxis: {
        lines: {
          show: true,
        },
      },
      yaxis: {
        lines: {
          show: false,
        },
      },
      padding: {
        top: 0,
        right: 20,
        bottom: 0,
        left: 10,
      },
    },
    responsive: [
      {
        breakpoint: 768,
        options: {
          chart: {
            height: 300,
          },
          plotOptions: {
            bar: {
              barHeight: "50%",
            },
          },
          dataLabels: {
            style: {
              fontSize: "10px",
            },
          },
          legend: {
            fontSize: "12px",
            itemMargin: {
              horizontal: 10,
              vertical: 3,
            },
          },
        },
      },
    ],
  };

  const series = [
    {
      name: "Success",
      data: [successCalls],
    },
    {
      name: "No Answer",
      data: [noAnswerCalls],
    },
  ];

  // Loading state
  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="mb-6">
          <h3 className="text-[23px] font-semibold text-gray-800 text-center mb-2">
            Monthly Call Status
          </h3>
          <p className="text-sm text-gray-600 text-center">
            Success vs No Answer (This Month)
          </p>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-600">Loading chart data...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <div className="mb-6">
        <h3 className="text-[23px] font-semibold text-gray-800 text-center mb-2">
          Monthly Call Status
        </h3>
        <p className="text-sm text-gray-600 text-center">
          Success vs No Answer (This Month)
        </p>
      </div>

      <Chart options={chartOptions} series={series} type="bar" height={400} />

      {/* Summary stats */}
      <div className="mt-6 grid grid-cols-3 gap-4 pt-4 border-t border-gray-200">
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#2D3748" }}>
            {totalCalls.toLocaleString()}
          </div>
          <div className="text-xs text-gray-600">Total This Month</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#82C355" }}>
            {successCalls.toLocaleString()}
          </div>
          <div className="text-xs text-gray-600">Success Calls</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#1c5b41" }}>
            {noAnswerCalls.toLocaleString()}
          </div>
          <div className="text-xs text-gray-600">No Answer</div>
        </div>
      </div>
    </div>
  );
}

export default MonthlyCallStatusChart;
