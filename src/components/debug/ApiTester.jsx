import React, { useState } from 'react';
import { leadsService } from '../../services/leadsService';
import { activitiesService } from '../../services/activitiesService';
import { callsService } from '../../services/callsService';
import { visitsService } from '../../services/visitsService';

const ApiTester = () => {
  const [results, setResults] = useState({});
  const [loading, setLoading] = useState({});

  const testEndpoint = async (name, testFunction) => {
    setLoading(prev => ({ ...prev, [name]: true }));
    try {
      console.log(`Testing ${name}...`);
      const result = await testFunction();
      console.log(`${name} result:`, result);
      setResults(prev => ({ 
        ...prev, 
        [name]: { 
          success: true, 
          data: result,
          count: result?.data?.length || result?.length || 0
        } 
      }));
    } catch (error) {
      console.error(`${name} error:`, error);
      setResults(prev => ({ 
        ...prev, 
        [name]: { 
          success: false, 
          error: error.message,
          status: error.response?.status
        } 
      }));
    } finally {
      setLoading(prev => ({ ...prev, [name]: false }));
    }
  };

  const tests = [
    {
      name: 'Leads (/leads/all)',
      test: () => leadsService.getAll()
    },
    {
      name: 'Activities - Calls (/activities-by-interaction-type?type=call)',
      test: () => activitiesService.getCalls()
    },
    {
      name: 'Activities - Visits (/activities-by-interaction-type?type=visit)',
      test: () => activitiesService.getVisits()
    },
    {
      name: 'Direct Calls (/calls)',
      test: () => callsService.getAll()
    },
    {
      name: 'Direct Visits (/visits)',
      test: () => visitsService.getAll()
    }
  ];

  const runAllTests = async () => {
    for (const test of tests) {
      await testEndpoint(test.name, test.test);
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">API Endpoint Tester</h1>
      
      <div className="mb-6">
        <button
          onClick={runAllTests}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          Run All Tests
        </button>
      </div>

      <div className="grid gap-4">
        {tests.map((test) => (
          <div key={test.name} className="border border-gray-200 rounded-lg p-4">
            <div className="flex justify-between items-center mb-2">
              <h3 className="font-semibold">{test.name}</h3>
              <button
                onClick={() => testEndpoint(test.name, test.test)}
                disabled={loading[test.name]}
                className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700 disabled:opacity-50"
              >
                {loading[test.name] ? 'Testing...' : 'Test'}
              </button>
            </div>
            
            {results[test.name] && (
              <div className={`p-3 rounded ${
                results[test.name].success 
                  ? 'bg-green-50 border border-green-200' 
                  : 'bg-red-50 border border-red-200'
              }`}>
                {results[test.name].success ? (
                  <div>
                    <p className="text-green-800 font-medium">✅ Success</p>
                    <p className="text-sm text-green-700">
                      Data count: {results[test.name].count}
                    </p>
                    <details className="mt-2">
                      <summary className="cursor-pointer text-sm text-green-600">
                        View Response
                      </summary>
                      <pre className="mt-2 text-xs bg-white p-2 rounded border overflow-auto max-h-40">
                        {JSON.stringify(results[test.name].data, null, 2)}
                      </pre>
                    </details>
                  </div>
                ) : (
                  <div>
                    <p className="text-red-800 font-medium">❌ Failed</p>
                    <p className="text-sm text-red-700">
                      Status: {results[test.name].status || 'Unknown'}
                    </p>
                    <p className="text-sm text-red-700">
                      Error: {results[test.name].error}
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        ))}
      </div>

      <div className="mt-8 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-semibold mb-2">Expected Backend Endpoints:</h3>
        <ul className="text-sm space-y-1">
          <li><code>GET /api/v1/leads/all</code> - Get all leads</li>
          <li><code>GET /api/v1/activities-by-interaction-type?type=call</code> - Get call activities</li>
          <li><code>GET /api/v1/activities-by-interaction-type?type=visit</code> - Get visit activities</li>
          <li><code>GET /api/v1/calls</code> - Get calls (if separate endpoint exists)</li>
          <li><code>GET /api/v1/visits</code> - Get visits (if separate endpoint exists)</li>
        </ul>
      </div>
    </div>
  );
};

export default ApiTester;
