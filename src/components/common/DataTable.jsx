import { useState, useEffect, useRef } from "react";
import {
  Search,
  Plus,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal,
  FileSpreadsheet,
  X,
  FileText,
  Calendar,
  Phone,
  MapPin,
  Upload,
  Download,
  Printer,
  Filter,
  ChevronDown,
  User,
  User<PERSON>heck,
  RotateCcw,
} from "lucide-react";
import Modal from "./Modal";
import ImportModal from "../modals/ImportModal";
import ExportModal from "../modals/ExportModal";
import Pagination from "./Pagination";
import StatisticsSection from "./StatisticsSection";
import { useDataTableShortcuts } from "../../hooks/useKeyboardShortcuts";

const DataTable = ({
  columns = [],
  data = [],
  searchPlaceholder = "Search...",
  addButtonText = "Add New",
  onAdd,
  onView,
  onEdit,
  onDelete,
  onCustomAction,
  customActionLabels = {}, // Labels for custom actions
  onStatusUpdate, // <PERSON>ler for status update actions
  updatingLeadIds = new Set(), // Set of lead IDs currently being updated
  actions = [],
  loading = false,
  highlightField,
  highlightColors = {},
  // Modal props
  createForm,
  editForm,
  deleteForm,
  logOutcomeForm,
  callForm,
  visitForm,
  profileForm,
  createModalTitle = "Create New Item",
  editModalTitle = "Edit Item",
  deleteModalTitle = "Delete Item",
  logOutcomeModalTitle = "Log Outcome",
  callModalTitle = "Make Call",
  visitModalTitle = "Schedule Visit",
  profileModalTitle = "Profile",
  modalSize = "md",
  deleteModalSize = "sm",
  logOutcomeModalSize = "md",
  callModalSize = "sm",
  visitModalSize = "sm",
  profileModalSize = "lg",
  // Import/Export props
  showImportExport = true,
  showImport = true, // New prop to control import button visibility
  showExportPrint = true, // New prop to control export and print buttons visibility
  showCreateButton = true, // New prop to control create button visibility
  onExport,
  onImport,
  onPrint,
  importModalTitle = "Import Data",
  importTemplateFileName = "template.xlsx",
  importAcceptedFileTypes = ".xlsx,.xls,.csv",
  // Enhanced import props for leads
  showAnchorSelection = false,
  anchors = [],
  onDownloadTemplate,
  isLeadsImport = false,
  onImportModalOpen, // Callback when import modal is opened
  // Enhanced import props for hitlists
  showHitlistTypeSelection = false,
  isHitlistImport = false,
  // Export modal configuration
  exportModalTitle = "Export Data",
  exportType = "leads", // "leads", "users", etc.
  exportFunction = null, // Custom export function
  userApi = null, // User API instance for users export
  exportSearchPlaceholder = "Enter search terms to filter data...",
  exportEmptySearchText = "Leave empty to export all data",
  exportButtonText = "Export Data",
  exportSuccessMessage = "Data exported successfully!",
  // Filter props - now more flexible
  showFilters = false,
  filterConfig = [], // Array of filter configurations
  onFilterChange, // Single handler for all filter changes
  onClearFilters,
  // Data count props
  showDataCount = true, // Default to true for all pages
  dataCountLabel = "items",
  // Custom filter logic
  customFilterLogic, // Optional custom filter function
  // Custom header content
  customHeaderContent, // Optional custom content above data count
  // Date range filter props
  showDateRangeFilter = false,
  dateRangeField = "date", // Field in data to filter by
  fromDate = "",
  toDate = "",
  onFromDateChange,
  onToDateChange,
  // Pagination props
  showPagination = false,
  currentPage = 1,
  totalPages = 1,
  totalItems = 0,
  pageSize = 10,
  onPageChange,
  onPageSizeChange,
  pageSizeOptions = [10, 20, 50, 100],
  showPageSizeSelector = true,
  showItemsInfo = true,
  showFirstLast = true,
  maxVisiblePages = 5,
  // Legacy load-more props (for backward compatibility)
  loadingMore = false,
  onLoadMore,
  showLoadMore = false,
  loadMoreText = "Load More",
  // Statistics section props
  showStatistics = false,
  statistics = [],
  activeStatistic = "",
  onStatisticChange,
  // Multiselect props
  allowMultiSelect = true,
  onMultiDelete,
  allowMultiReassign = true,
  reassignForm1,
  reassignForm2,
  reassignFormSelector1, // Function to determine which items use form 1: (item) => item.type === "Dormancy"
  reassignFormSelector2, // Function to determine which items use form 2
  getReassignFormData, // Function to get autofill data: (selectedItems) => ({ field: value })
  onMultiReassign,
  // Custom empty state
  emptyStateComponent,
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [openDropdown, setOpenDropdown] = useState(null);
  const [dropdownPosition, setDropdownPosition] = useState({});
  const dropdownRefs = useRef({});

  // Dynamic filter dropdown states - one for each filter
  const [filterDropdownStates, setFilterDropdownStates] = useState({});

  // Modal states
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isLogOutcomeModalOpen, setIsLogOutcomeModalOpen] = useState(false);
  const [isImportModalOpen, setIsImportModalOpen] = useState(false);
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [isCallModalOpen, setIsCallModalOpen] = useState(false);
  const [isVisitModalOpen, setIsVisitModalOpen] = useState(false);
  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);

  // Multiselect state
  const [selectedRows, setSelectedRows] = useState(new Set());
  const [selectAll, setSelectAll] = useState(false);
  const [isMultiReassignModalOpen, setIsMultiReassignModalOpen] =
    useState(false);
  const [activeReassignForm, setActiveReassignForm] = useState(null);

  // Close dropdown when clicking outside or scrolling
  useEffect(() => {
    const handleClickOutside = (event) => {
      // Close action dropdowns
      if (openDropdown && dropdownRefs.current[openDropdown]) {
        const dropdownElement = dropdownRefs.current[openDropdown];
        if (!dropdownElement.contains(event.target)) {
          setOpenDropdown(null);
        }
      }

      // Close filter dropdowns
      const filterDropdowns = document.querySelectorAll(
        "[data-filter-dropdown]"
      );
      let clickedInsideFilter = false;
      filterDropdowns.forEach((dropdown) => {
        if (dropdown.contains(event.target)) {
          clickedInsideFilter = true;
        }
      });

      if (!clickedInsideFilter) {
        setFilterDropdownStates({});
      }
    };

    const handleScroll = () => {
      // Don't close dropdown on scroll - only close on click outside
      // This allows users to scroll while keeping the dropdown open
    };

    const handleResize = () => {
      // Close dropdown on window resize
      if (openDropdown) {
        setOpenDropdown(null);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    window.addEventListener("scroll", handleScroll, true); // Use capture to catch all scroll events
    window.addEventListener("resize", handleResize);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      window.removeEventListener("scroll", handleScroll, true);
      window.removeEventListener("resize", handleResize);
    };
  }, [openDropdown]);

  // Keyboard shortcuts
  useDataTableShortcuts({
    onExport:
      showImportExport && onExport ? () => setIsExportModalOpen(true) : null,
    onImport:
      showImportExport && showImport && onImport
        ? () => setIsImportModalOpen(true)
        : null,
    onNew: onAdd ? () => setIsCreateModalOpen(true) : null,
  });

  // Filter data based on search term and dynamic filters
  const filteredData = (Array.isArray(data) ? data : []).filter((row) => {
    // Search term filter
    const matchesSearch =
      searchTerm === "" ||
      Object.values(row).some((value) =>
        value?.toString().toLowerCase().includes(searchTerm.toLowerCase())
      );

    // Date range filter
    let matchesDateRange = true;
    if (showDateRangeFilter && (fromDate || toDate)) {
      const rowDate = new Date(row[dateRangeField]);
      if (fromDate) {
        const fromDateObj = new Date(fromDate);
        matchesDateRange = matchesDateRange && rowDate >= fromDateObj;
      }
      if (toDate) {
        const toDateObj = new Date(toDate);
        // Set to end of day for inclusive filtering
        toDateObj.setHours(23, 59, 59, 999);
        matchesDateRange = matchesDateRange && rowDate <= toDateObj;
      }
    }

    // Apply custom filter logic if provided
    if (customFilterLogic) {
      return (
        matchesSearch &&
        matchesDateRange &&
        customFilterLogic(row, filterConfig)
      );
    }

    // Default filter logic - check each filter in filterConfig
    const matchesFilters = filterConfig.every((filter) => {
      const filterValue = filter.selectedValue || "";
      if (filterValue === "") return true; // No filter applied

      // Get the field value from the row
      const rowValue = row[filter.field];
      return rowValue === filterValue;
    });

    return matchesSearch && matchesDateRange && matchesFilters;
  });

  const calculateDropdownPosition = (buttonElement, rowId) => {
    const buttonRect = buttonElement.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const dropdownHeight = 200; // Increased to account for more actions
    const spaceBelow = viewportHeight - buttonRect.bottom;
    const spaceAbove = buttonRect.top;
    const buffer = 20; // Extra buffer to prevent edge cases

    // Show dropdown above if:
    // 1. There's not enough space below (including buffer)
    // 2. AND there's more space above than below
    // 3. OR if we're in the bottom 30% of the viewport
    const isInBottomThird = buttonRect.bottom > viewportHeight * 0.7;
    const showAbove =
      (spaceBelow < dropdownHeight + buffer && spaceAbove > spaceBelow) ||
      (isInBottomThird && spaceAbove > dropdownHeight);

    setDropdownPosition({
      [rowId]: {
        showAbove,
        top: showAbove ? "auto" : "100%",
        bottom: showAbove ? "100%" : "auto",
      },
    });
  };

  const handleDropdownToggle = (rowId, event) => {
    if (openDropdown === rowId) {
      setOpenDropdown(null);
    } else {
      calculateDropdownPosition(event.currentTarget, rowId);
      setOpenDropdown(rowId);
    }
  };

  // Modal handlers
  const handleCreateClick = () => {
    if (createForm) {
      setIsCreateModalOpen(true);
    } else {
      onAdd?.();
    }
  };

  const handleViewClick = (row) => {
    setSelectedItem(row);
    onView?.(row);
  };

  const handleEditClick = (row) => {
    setSelectedItem(row);
    if (editForm) {
      setIsEditModalOpen(true);
    } else {
      onEdit?.(row);
    }
  };

  const handleDeleteClick = (row) => {
    setSelectedItem(row);
    if (deleteForm) {
      setIsDeleteModalOpen(true);
    } else {
      onDelete?.(row);
    }
  };

  const handleLogOutcomeClick = (row) => {
    setSelectedItem(row);
    if (logOutcomeForm) {
      setIsLogOutcomeModalOpen(true);
    }
  };

  const handleCallClick = (row) => {
    setSelectedItem(row);
    if (callForm) {
      setIsCallModalOpen(true);
    }
  };

  const handleVisitClick = (row) => {
    setSelectedItem(row);
    if (visitForm) {
      setIsVisitModalOpen(true);
    }
  };

  const handleProfileClick = (row) => {
    setSelectedItem(row);
    if (profileForm) {
      setIsProfileModalOpen(true);
    }
  };

  // Multiselect handlers
  const handleSelectRow = (rowId) => {
    const newSelectedRows = new Set(selectedRows);
    if (newSelectedRows.has(rowId)) {
      newSelectedRows.delete(rowId);
    } else {
      newSelectedRows.add(rowId);
    }
    setSelectedRows(newSelectedRows);
    setSelectAll(newSelectedRows.size === data.length && data.length > 0);
  };

  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedRows(new Set());
      setSelectAll(false);
    } else {
      const allIds = new Set(data.map((row) => row.id));
      setSelectedRows(allIds);
      setSelectAll(true);
    }
  };

  const handleMultiDelete = () => {
    if (selectedRows.size > 0) {
      // Set up the delete modal for multiple items
      setSelectedItem({
        id: "multi-delete",
        name: `${selectedRows.size} selected records`,
        isMultiDelete: true,
        selectedIds: Array.from(selectedRows),
      });
      setIsDeleteModalOpen(true);
    }
  };

  const handleMultiDeleteConfirm = () => {
    if (selectedItem?.isMultiDelete && onMultiDelete) {
      const selectedIds = selectedItem.selectedIds;
      console.log("Deleting selected records:", selectedIds);
      onMultiDelete(selectedIds);
      setSelectedRows(new Set());
      setSelectAll(false);
      setIsDeleteModalOpen(false);
      setSelectedItem(null);
    }
  };

  const handleMultiReassign = () => {
    if (selectedRows.size > 0 && allowMultiReassign) {
      const selectedItems = data.filter((row) => selectedRows.has(row.id));

      // Determine which form to use based on the selectors
      let formToUse = null;
      if (reassignFormSelector1 && selectedItems.some(reassignFormSelector1)) {
        formToUse = "form1";
      } else if (
        reassignFormSelector2 &&
        selectedItems.some(reassignFormSelector2)
      ) {
        formToUse = "form2";
      } else if (reassignForm1) {
        formToUse = "form1"; // Default to form1 if available
      } else if (reassignForm2) {
        formToUse = "form2"; // Fallback to form2
      }

      if (formToUse) {
        setActiveReassignForm(formToUse);
        setSelectedItem({
          id: "multi-reassign",
          name: `${selectedRows.size} selected records`,
          isMultiReassign: true,
          selectedIds: Array.from(selectedRows),
          selectedItems: selectedItems,
          formData: getReassignFormData
            ? getReassignFormData(selectedItems)
            : {},
        });
        setIsMultiReassignModalOpen(true);
      }
    }
  };

  const handleMultiReassignConfirm = (formData) => {
    if (selectedItem?.isMultiReassign && onMultiReassign) {
      const selectedIds = selectedItem.selectedIds;
      console.log(
        "Reassigning selected records:",
        selectedIds,
        "with data:",
        formData
      );
      onMultiReassign(selectedIds, formData);
      setSelectedRows(new Set());
      setSelectAll(false);
      setIsMultiReassignModalOpen(false);
      setSelectedItem(null);
      setActiveReassignForm(null);
    }
  };

  const closeAllModals = () => {
    setIsCreateModalOpen(false);
    setIsEditModalOpen(false);
    setIsDeleteModalOpen(false);
    setIsLogOutcomeModalOpen(false);
    setIsCallModalOpen(false);
    setIsVisitModalOpen(false);
    setIsProfileModalOpen(false);
    setSelectedItem(null);
  };

  const handleActionClick = (action, row) => {
    setOpenDropdown(null);
    switch (action) {
      case "view":
        handleViewClick(row);
        break;
      case "profile":
        handleProfileClick(row);
        break;
      case "edit":
      case "reschedule":
        handleEditClick(row);
        break;
      case "delete":
      case "cancel":
        handleDeleteClick(row);
        break;
      case "log-outcome":
        handleLogOutcomeClick(row);
        break;
      case "call":
        handleCallClick(row);
        break;
      case "visit":
        handleVisitClick(row);
        break;
      case "set-pending":
        if (onStatusUpdate) {
          onStatusUpdate(row.id, "Pending");
        }
        break;
      case "set-hot":
        if (onStatusUpdate) {
          onStatusUpdate(row.id, "Hot");
        }
        break;
      case "set-warm":
        if (onStatusUpdate) {
          onStatusUpdate(row.id, "Warm");
        }
        break;
      case "set-cold":
        if (onStatusUpdate) {
          onStatusUpdate(row.id, "Cold");
        }
        break;
      case "convert-to-client":
        if (onCustomAction) {
          onCustomAction("convert-to-client", row);
        }
        break;
      case "reassign":
        if (onCustomAction) {
          onCustomAction("reassign", row);
        }
        break;
      default:
        // Handle custom actions
        if (onCustomAction) {
          onCustomAction(action, row);
        }
        break;
    }
  };

  const getActionIcon = (action) => {
    switch (action) {
      case "view":
        return <Eye size={16} />;
      case "profile":
        return <User size={16} />;
      case "edit":
        return <Edit size={16} />;
      case "reschedule":
        return <Calendar size={16} />;
      case "delete":
        return <Trash2 size={16} />;
      case "cancel":
        return <X size={16} />;
      case "log-outcome":
        return <FileText size={16} />;
      case "call":
        return <Phone size={16} />;
      case "visit":
        return <MapPin size={16} />;
      case "set-pending":
        return <span className="w-4 h-4 rounded-full bg-yellow-500"></span>;
      case "set-hot":
        return <span className="w-4 h-4 rounded-full bg-red-500"></span>;
      case "set-warm":
        return <span className="w-4 h-4 rounded-full bg-orange-500"></span>;
      case "set-cold":
        return <span className="w-4 h-4 rounded-full bg-blue-500"></span>;
      case "convert-to-client":
        return <User size={16} />;
      case "reassign":
        return <RotateCcw size={16} />;
      case "view-breakdown":
        return <Eye size={16} />;
      default:
        return null;
    }
  };

  const getActionLabel = (action) => {
    // Check for custom action labels first
    if (customActionLabels[action]) {
      return customActionLabels[action];
    }

    switch (action) {
      case "view":
        return "View";
      case "profile":
        return "Profile";
      case "edit":
        return "Edit";
      case "reschedule":
        return "Reschedule";
      case "delete":
        return "Delete";
      case "cancel":
        return "Cancel";
      case "log-outcome":
        return "Log Outcome";
      case "call":
        return "Call";
      case "visit":
        return "Visit";
      case "set-pending":
        return "Pending";
      case "set-hot":
        return "Hot";
      case "set-warm":
        return "Warm";
      case "set-cold":
        return "Cold";
      case "convert-to-client":
        return "Convert to Client";
      case "reassign":
        return "Reassign";
      default:
        return (
          action.charAt(0).toUpperCase() + action.slice(1).replace(/-/g, " ")
        );
    }
  };

  const renderCellContent = (column, row) => {
    const value = row[column.key];

    if (column.render) {
      // Check if this is the name column and we have a profile form
      if (column.key === "name" && profileForm) {
        return (
          <div
            onClick={() => handleProfileClick(row)}
            className="cursor-pointer"
          >
            {column.render(value, row)}
          </div>
        );
      }
      return column.render(value, row);
    }

    // Apply highlighting if this is the highlight field
    if (highlightField === column.key && highlightColors[value]) {
      return (
        <span
          className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${highlightColors[value]}`}
        >
          {value}
        </span>
      );
    }

    // Make name field clickable if profile form is available
    if (column.key === "name" && profileForm) {
      return (
        <span
          onClick={() => handleProfileClick(row)}
          className="cursor-pointer hover:text-blue-600 transition-colors"
        >
          {value}
        </span>
      );
    }

    return value;
  };

  const anyVisible =
    actions.some((action) => action.is_visible && action.is_visible()) ||
    actions.some((action) => typeof action === "string");
  return (
    <div className="bg-white w-full dark:bg-gray-800 rounded-lg transition-colors duration-200 pb-[2rem]">
      {/* Custom header content */}
      {customHeaderContent && (
        <div className="px-4 pt-4 pb-2">{customHeaderContent}</div>
      )}

      {/* Data count display */}
      {showDataCount && (
        <div className="px-4 pt-4 pb-2">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Showing {filteredData.length} of {data.length} {dataCountLabel}
          </p>
        </div>
      )}

      {/* Header with search and add button - Mobile Responsive */}
      <div className="p-4 ">
        <div className="flex flex-col gap-4">
          {/* Search and Add Button Row */}
          <div className="flex flex-col sm:flex-row gap-3">
            {/* Search */}
            <div className="relative flex-1">
              <Search
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500"
                size={20}
              />
              <input
                type="text"
                placeholder={searchPlaceholder}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-[10px] border border-gray-400 dark:border-gray-500
                 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500
                   dark:placeholder-gray-400 transition-colors duration-200 outline-none text-[14px] md:text-[15px]"
              />
            </div>

            {/* Multiselect Action Buttons */}
            {allowMultiSelect && selectedRows.size > 0 && (
              <div className="flex gap-2">
                {allowMultiReassign && (reassignForm1 || reassignForm2) && (
                  <button
                    onClick={handleMultiReassign}
                    className="inline-flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700
                     text-white transition-colors duration-200 text-[14px] rounded-lg whitespace-nowrap"
                  >
                    <UserCheck size={20} className="mr-2" />
                    <span>Reassign Selected ({selectedRows.size})</span>
                  </button>
                )}
                <button
                  onClick={handleMultiDelete}
                  className="inline-flex items-center justify-center px-4 py-2 bg-red-600 hover:bg-red-700
                   text-white transition-colors duration-200 text-[14px] rounded-lg whitespace-nowrap"
                >
                  <Trash2 size={20} className="mr-2" />
                  <span>Delete Selected ({selectedRows.size})</span>
                </button>
              </div>
            )}

            {/* Add button */}
            {(onAdd || createForm) && showCreateButton && (
              <button
                onClick={handleCreateClick}
                className="inline-flex items-center justify-center px-4 py-2 bg-[#165026] hover:bg-green-700
                 text-white transition-colors duration-200 text-[14px] rounded-lg whitespace-nowrap"
              >
                <Plus size={20} className="mr-2" />
                <span>{addButtonText}</span>
              </button>
            )}
          </div>

          {/* Statistics Section */}
          <StatisticsSection
            statistics={statistics}
            activeTab={activeStatistic}
            onTabChange={onStatisticChange}
            showSection={showStatistics}
          />
        </div>

        {/* Import/Export/Print buttons and Filters - Mobile Responsive */}
        {(showImportExport || showFilters || showDateRangeFilter) && (
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mt-4">
            {/* Import/Export/Print buttons - Mobile Responsive */}
            {showImportExport && (
              <div className="flex flex-wrap items-center gap-2">
                {showImport && onImport && (
                  <button
                    onClick={() => {
                      setIsImportModalOpen(true);
                      // Call the callback to fetch anchors if provided
                      if (onImportModalOpen) {
                        onImportModalOpen();
                      }
                    }}
                    className="inline-flex items-center px-3 py-2 bg-[#66A25E] hover:bg-green-700
                     text-white transition-colors duration-200 text-[13px] sm:text-[14px] border-[#76b36e] border
                     rounded-lg"
                  >
                    <Download size={16} className="mr-1 sm:mr-2" />
                    <span className="hidden sm:inline">Import</span>
                    <span className="sm:hidden">Import</span>
                  </button>
                )}
                {showExportPrint && (
                  <>
                    <button
                      onClick={() => setIsExportModalOpen(true)}
                      className="inline-flex items-center px-3 py-2 bg-[rgba(51,197,92,0.1)] hover:bg-[rgba(51,197,92,0.3)]
                       text-green-700 border border-green-600 transition-colors duration-200 text-[13px] sm:text-[14px]
                       rounded-lg"
                      title="Export Leads (Ctrl+E)"
                    >
                      <FileSpreadsheet size={16} className="mr-1 sm:mr-2" />
                      <span className="hidden sm:inline">Export Excel</span>
                      <span className="sm:hidden">Export</span>
                    </button>
                    <button
                      onClick={onPrint}
                      className="inline-flex items-center px-3 py-2 bg-transparent hover:bg-[#6AA97A]
                       text-green-700 hover:text-white border border-green-700 transition-colors duration-200 text-[13px] sm:text-[14px]
                       rounded-lg"
                    >
                      <Printer size={16} className="mr-1 sm:mr-2" />
                      <span className="hidden sm:inline">Print</span>
                      <span className="sm:hidden">Print</span>
                    </button>
                  </>
                )}
              </div>
            )}

            {/* Filters - Mobile Responsive */}
            {((showFilters && filterConfig.length > 0) ||
              showDateRangeFilter) && (
              <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4">
                {/* Filters label */}
                <div className="flex items-center text-gray-600 dark:text-gray-400">
                  <Filter size={16} className="mr-2" />
                  <span className="text-sm font-medium">Filters:</span>
                </div>

                {/* Date Range Filters - Mobile Responsive */}
                {showDateRangeFilter && (
                  <div className="flex flex-col sm:flex-row gap-2 sm:gap-4">
                    {/* From Date */}
                    <div className="flex items-center gap-2">
                      <label className="text-sm font-medium text-gray-700 dark:text-gray-300 whitespace-nowrap">
                        From
                      </label>
                      <input
                        type="date"
                        value={fromDate}
                        onChange={(e) =>
                          onFromDateChange && onFromDateChange(e.target.value)
                        }
                        className="px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white outline-none focus:border-green-500 hover:border-gray-400 transition-colors duration-200"
                      />
                    </div>

                    {/* To Date */}
                    <div className="flex items-center gap-2">
                      <label className="text-sm font-medium text-gray-700 dark:text-gray-300 whitespace-nowrap">
                        To
                      </label>
                      <input
                        type="date"
                        value={toDate}
                        onChange={(e) =>
                          onToDateChange && onToDateChange(e.target.value)
                        }
                        className="px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white outline-none focus:border-green-500 hover:border-gray-400 transition-colors duration-200"
                      />
                    </div>
                  </div>
                )}

                {/* Dynamic filters - Mobile Responsive */}
                <div className="flex flex-wrap gap-2">
                  {filterConfig.map((filter) => (
                    <div
                      key={filter.key}
                      className="relative"
                      data-filter-dropdown
                    >
                      <button
                        onClick={() => {
                          const newStates = {};
                          newStates[filter.key] =
                            !filterDropdownStates[filter.key];
                          setFilterDropdownStates(newStates);
                        }}
                        className="inline-flex items-center px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-200"
                      >
                        <span className="truncate max-w-[120px] sm:max-w-none">
                          {filter.selectedValue
                            ? filter.options.find(
                                (opt) => opt.value === filter.selectedValue
                              )?.label || filter.selectedValue
                            : filter.placeholder || `All ${filter.label}`}
                        </span>
                        <ChevronDown size={16} className="ml-2 flex-shrink-0" />
                      </button>

                      {filterDropdownStates[filter.key] && (
                        <div className="absolute top-full left-0 mt-1 w-48 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg z-50">
                          <div className="py-1">
                            <button
                              onClick={() => {
                                onFilterChange(filter.key, "");
                                setFilterDropdownStates({});
                              }}
                              className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600"
                            >
                              {filter.placeholder || `All ${filter.label}`}
                            </button>
                            {filter.options.map((option) => (
                              <button
                                key={option.value}
                                onClick={() => {
                                  onFilterChange(filter.key, option.value);
                                  setFilterDropdownStates({});
                                }}
                                className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600"
                              >
                                {option.label}
                              </button>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                {/* Clear filters button */}
                <button
                  onClick={() => {
                    onClearFilters();
                    setFilterDropdownStates({});
                    // Clear date filters if they exist
                    if (showDateRangeFilter) {
                      onFromDateChange && onFromDateChange("");
                      onToDateChange && onToDateChange("");
                    }
                  }}
                  className="inline-flex items-center px-3 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors duration-200 whitespace-nowrap"
                >
                  <X size={14} className="mr-1" />
                  Clear
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Table - Mobile Responsive with Horizontal Scroll */}
      <div className="overflow-x-auto w-full " style={{ minHeight: "400px" }}>
        <table className="w-full min-w-[800px]">
          <thead className="bg-[rgba(243,244,246,0.9)] border border-gray-200 dark:bg-gray-700 ">
            <tr>
              {allowMultiSelect && (
                <th
                  className="px-3 sm:px-6 py-3 text-left text-[12px] sm:text-[14px] text-black dark:text-gray-300
                  uppercase tracking-wider font-semibold whitespace-nowrap w-12"
                >
                  <input
                    type="checkbox"
                    checked={selectAll}
                    onChange={handleSelectAll}
                    className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                  />
                </th>
              )}
              {columns.map((column) => (
                <th
                  key={column.key}
                  className="px-3 sm:px-6 py-3 text-left text-[12px] sm:text-[14px] text-black dark:text-gray-300
                  uppercase tracking-wider font-semibold whitespace-nowrap"
                >
                  {column.title}
                </th>
              ))}
              {actions.length > 0 && anyVisible && (
                <th
                  className="px-2 sm:px-4 py-3 text-left text-[12px] sm:text-[14px] text-black dark:text-gray-300
                  uppercase tracking-wider font-semibold whitespace-nowrap"
                >
                  actions
                </th>
              )}
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600">
            {loading ? (
              <tr>
                <td
                  colSpan={
                    columns.length +
                    (actions.length > 0 ? 1 : 0) +
                    (allowMultiSelect ? 1 : 0)
                  }
                  className="px-6 py-12 text-center"
                >
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
                    <span className="ml-3 text-gray-500 dark:text-gray-400">
                      Loading...
                    </span>
                  </div>
                </td>
              </tr>
            ) : filteredData.length === 0 ? (
              <tr>
                <td
                  colSpan={
                    columns.length +
                    (actions.length > 0 ? 1 : 0) +
                    (allowMultiSelect ? 1 : 0)
                  }
                  className="px-6 py-12 text-center"
                >
                  {emptyStateComponent ? (
                    emptyStateComponent
                  ) : (
                    <div className="text-gray-500 dark:text-gray-400">
                      {searchTerm ? "No results found" : "No data available"}
                    </div>
                  )}
                </td>
              </tr>
            ) : (
              filteredData.map((row, index) => {
                const isUpdating = updatingLeadIds.has(row.id);
                return (
                  <tr
                    key={row.uniqueKey || row.id || `row-${index}`}
                    className={`${
                      index % 2 === 0
                        ? "bg-gray-50/60 dark:bg-gray-800/50"
                        : "bg-white dark:bg-gray-800"
                    } hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 ${
                      isUpdating ? "opacity-60 pointer-events-none" : ""
                    }`}
                  >
                    {allowMultiSelect && (
                      <td className="px-3 sm:px-6 py-3 sm:py-4 w-12">
                        <input
                          type="checkbox"
                          checked={selectedRows.has(row.id)}
                          onChange={() => handleSelectRow(row.id)}
                          className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                        />
                      </td>
                    )}
                    {columns.map((column) => (
                      <td
                        key={column.key}
                        className="px-3 sm:px-6 py-3 sm:py-4 text-xs sm:text-sm text-gray-900 dark:text-white"
                      >
                        <div
                          className="truncate max-w-[120px] sm:max-w-none"
                          title={
                            typeof renderCellContent(column, row) === "string"
                              ? renderCellContent(column, row)
                              : ""
                          }
                        >
                          {renderCellContent(column, row)}
                        </div>
                      </td>
                    ))}
                    {actions.length > 0 && (
                      <td
                        className={`${
                          anyVisible ? "" : "hidden"
                        } px-2 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-left text-sm font-medium`}
                      >
                        <div
                          className="relative"
                          ref={(el) => {
                            if (el) dropdownRefs.current[row.id] = el;
                          }}
                        >
                          {anyVisible ? (
                            <button
                              onClick={(event) =>
                                handleDropdownToggle(row.id, event)
                              }
                              className="text-[#1c5b41] bg-[rgba(54,201,95,0.1)] border-[rgba(54,201,95,0.1)]
                          transition-colors duration-200 cursor-pointer p-1 sm:p-2 border-[1px] rounded-[5px]"
                            >
                              <MoreHorizontal className="w-4 h-4 sm:w-5 sm:h-5" />
                            </button>
                          ) : (
                            "No actions"
                          )}

                          {openDropdown === row.id && (
                            <div
                              className="absolute right-0 w-48 bg-white dark:bg-gray-700 rounded-md shadow-lg z-50 border border-gray-200 dark:border-gray-600"
                              style={{
                                top: dropdownPosition[row.id]?.top || "100%",
                                bottom:
                                  dropdownPosition[row.id]?.bottom || "auto",
                                marginTop: dropdownPosition[row.id]?.showAbove
                                  ? "-0.5rem"
                                  : "0.5rem",
                                marginBottom: dropdownPosition[row.id]
                                  ?.showAbove
                                  ? "0.5rem"
                                  : "0",
                                boxShadow: dropdownPosition[row.id]?.showAbove
                                  ? "0 -4px 6px -1px rgba(0, 0, 0, 0.1), 0 -2px 4px -1px rgba(0, 0, 0, 0.06)"
                                  : "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
                              }}
                            >
                              <div className="py-1">
                                {actions
                                  .filter((action) => {
                                    // Handle both old string format and new object format
                                    if (typeof action === "string") {
                                      return true; // Show all string actions for backward compatibility
                                    }
                                    // For object format, check is_visible function
                                    return action.is_visible
                                      ? action.is_visible()
                                      : true;
                                  })
                                  .map((action, idx) => {
                                    // Get action name - handle both string and object formats
                                    const actionName =
                                      typeof action === "string"
                                        ? action
                                        : action.name;

                                    return (
                                      <div key={actionName}>
                                        {/* Add divider after delete */}
                                        {actionName === "reassign" &&
                                          actions.some(
                                            (a) =>
                                              (typeof a === "string"
                                                ? a
                                                : a.name) === "delete"
                                          ) && (
                                            <div className="border-t border-gray-200 dark:border-gray-600 my-1"></div>
                                          )}
                                        <button
                                          onClick={() =>
                                            handleActionClick(actionName, row)
                                          }
                                          className={`flex items-center w-full px-4 py-2 text-sm text-left transition-colors duration-200 ${
                                            actionName === "delete" ||
                                            actionName === "cancel"
                                              ? "text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/20"
                                              : "hover:bg-gray-50"
                                          }`}
                                          style={{
                                            color:
                                              actionName === "delete" ||
                                              actionName === "cancel"
                                                ? undefined
                                                : "#7e7e7e",
                                            transition: "color 0.3s ease",
                                          }}
                                          onMouseEnter={(e) => {
                                            if (
                                              actionName !== "delete" &&
                                              actionName !== "cancel"
                                            ) {
                                              e.currentTarget.style.color =
                                                "#4a4a4a";
                                              const icon =
                                                e.currentTarget.querySelector(
                                                  "span"
                                                );
                                              if (icon) {
                                                icon.style.color = "#4a4a4a";
                                                icon.style.transition =
                                                  "color 0.3s ease";
                                              }
                                            }
                                          }}
                                          onMouseLeave={(e) => {
                                            if (
                                              actionName !== "delete" &&
                                              actionName !== "cancel"
                                            ) {
                                              e.currentTarget.style.color =
                                                "#7e7e7e";
                                              const icon =
                                                e.currentTarget.querySelector(
                                                  "span"
                                                );
                                              if (icon)
                                                icon.style.color = "#7e7e7e";
                                            }
                                          }}
                                        >
                                          <span
                                            className="mr-3"
                                            style={{
                                              color:
                                                actionName === "delete" ||
                                                actionName === "cancel"
                                                  ? undefined
                                                  : "#7e7e7e",
                                              transition: "color 0.3s ease",
                                            }}
                                          >
                                            {getActionIcon(actionName)}
                                          </span>
                                          {getActionLabel(actionName)}
                                        </button>
                                      </div>
                                    );
                                  })}
                              </div>
                            </div>
                          )}
                        </div>
                      </td>
                    )}
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {showPagination && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalItems={totalItems}
          itemsPerPage={pageSize}
          onPageChange={onPageChange}
          onPageSizeChange={onPageSizeChange}
          showPageSizeSelector={showPageSizeSelector}
          pageSizeOptions={pageSizeOptions}
          showItemsInfo={showItemsInfo}
          showFirstLast={showFirstLast}
          maxVisiblePages={maxVisiblePages}
          loading={loading}
        />
      )}

      {/* Modals */}
      {createForm && (
        <Modal
          isOpen={isCreateModalOpen}
          onClose={closeAllModals}
          title={createModalTitle}
          size={modalSize}
        >
          {typeof createForm === "function"
            ? createForm({ onClose: closeAllModals })
            : createForm}
        </Modal>
      )}

      {editForm && (
        <Modal
          isOpen={isEditModalOpen}
          onClose={closeAllModals}
          title={editModalTitle}
          size={modalSize}
        >
          {typeof editForm === "function"
            ? editForm({ item: selectedItem, onClose: closeAllModals })
            : editForm}
        </Modal>
      )}

      {deleteForm && (
        <Modal
          isOpen={isDeleteModalOpen}
          onClose={closeAllModals}
          title={
            selectedItem?.isMultiDelete
              ? "Delete Selected Records"
              : deleteModalTitle
          }
          size={deleteModalSize}
        >
          {typeof deleteForm === "function"
            ? deleteForm({
                item: selectedItem,
                onClose: closeAllModals,
                onSubmit: selectedItem?.isMultiDelete
                  ? handleMultiDeleteConfirm
                  : undefined,
              })
            : deleteForm}
        </Modal>
      )}

      {logOutcomeForm && (
        <Modal
          isOpen={isLogOutcomeModalOpen}
          onClose={closeAllModals}
          title={logOutcomeModalTitle}
          size={logOutcomeModalSize}
        >
          {typeof logOutcomeForm === "function"
            ? logOutcomeForm({ item: selectedItem, onClose: closeAllModals })
            : logOutcomeForm}
        </Modal>
      )}

      {callForm && (
        <Modal
          isOpen={isCallModalOpen}
          onClose={closeAllModals}
          title={callModalTitle}
          size={callModalSize || modalSize}
        >
          {typeof callForm === "function"
            ? callForm({ item: selectedItem, onClose: closeAllModals })
            : callForm}
        </Modal>
      )}

      {visitForm && (
        <Modal
          isOpen={isVisitModalOpen}
          onClose={closeAllModals}
          title={visitModalTitle}
          size={visitModalSize || modalSize}
        >
          {typeof visitForm === "function"
            ? visitForm({ item: selectedItem, onClose: closeAllModals })
            : visitForm}
        </Modal>
      )}

      {profileForm && (
        <Modal
          isOpen={isProfileModalOpen}
          onClose={closeAllModals}
          title={profileModalTitle}
          size={profileModalSize || modalSize}
        >
          {typeof profileForm === "function"
            ? profileForm({ item: selectedItem, onClose: closeAllModals })
            : profileForm}
        </Modal>
      )}

      {/* Import Modal */}
      {showImportExport && (
        <ImportModal
          isOpen={isImportModalOpen}
          onClose={() => setIsImportModalOpen(false)}
          title={importModalTitle}
          templateFileName={importTemplateFileName}
          onImport={onImport}
          acceptedFileTypes={importAcceptedFileTypes}
          showAnchorSelection={showAnchorSelection}
          anchors={anchors}
          onDownloadTemplate={onDownloadTemplate}
          isLeadsImport={isLeadsImport}
          showHitlistTypeSelection={showHitlistTypeSelection}
          isHitlistImport={isHitlistImport}
        />
      )}

      {/* Export Modal */}
      {showImportExport && (
        <ExportModal
          isOpen={isExportModalOpen}
          onClose={() => setIsExportModalOpen(false)}
          title={exportModalTitle}
          currentSearchQuery={searchTerm}
          exportType={exportType}
          exportFunction={exportFunction}
          userApi={userApi}
          searchPlaceholder={exportSearchPlaceholder}
          emptySearchText={exportEmptySearchText}
          exportButtonText={exportButtonText}
          successMessage={exportSuccessMessage}
        />
      )}

      {/* Multi-Reassign Modal */}
      {allowMultiReassign && (reassignForm1 || reassignForm2) && (
        <Modal
          isOpen={isMultiReassignModalOpen}
          onClose={() => {
            setIsMultiReassignModalOpen(false);
            setSelectedItem(null);
            setActiveReassignForm(null);
          }}
          title="Reassign Selected Records"
          size="lg"
        >
          {activeReassignForm === "form1" &&
            reassignForm1 &&
            (typeof reassignForm1 === "function"
              ? reassignForm1({
                  item: selectedItem,
                  onClose: () => {
                    setIsMultiReassignModalOpen(false);
                    setSelectedItem(null);
                    setActiveReassignForm(null);
                  },
                  onSubmit: handleMultiReassignConfirm,
                })
              : reassignForm1)}
          {activeReassignForm === "form2" &&
            reassignForm2 &&
            (typeof reassignForm2 === "function"
              ? reassignForm2({
                  item: selectedItem,
                  onClose: () => {
                    setIsMultiReassignModalOpen(false);
                    setSelectedItem(null);
                    setActiveReassignForm(null);
                  },
                  onSubmit: handleMultiReassignConfirm,
                })
              : reassignForm2)}
        </Modal>
      )}
    </div>
  );
};

export default DataTable;
