import { usePermissions } from '../../contexts/PermissionContext';

/**
 * PermissionGate component that conditionally renders children based on user permissions
 * 
 * @param {Array} perms_required - Array of permission IDs required to render children
 *                                If last element is "OR", uses OR logic (any permission)
 *                                Otherwise uses AND logic (all permissions)
 * @param {ReactNode} children - Components to render if permissions are satisfied
 * @param {ReactNode} fallback - Optional fallback component to render if permissions fail
 * @returns {ReactNode|null} Children if permissions satisfied, fallback or null otherwise
 * 
 * @example
 * // AND logic - user must have both permissions
 * <PermissionGate perms_required={["edit.customer", "create.customer"]}>
 *   <button>Edit Customer</button>
 * </PermissionGate>
 * 
 * @example
 * // OR logic - user must have at least one permission
 * <PermissionGate perms_required={["edit.customer", "create.customer", "OR"]}>
 *   <button>Manage Customer</button>
 * </PermissionGate>
 * 
 * @example
 * // With fallback component
 * <PermissionGate 
 *   perms_required={["admin.access"]} 
 *   fallback={<span>Access Denied</span>}
 * >
 *   <AdminPanel />
 * </PermissionGate>
 */
const PermissionGate = ({ perms_required = [], children, fallback = null }) => {
  const { hasPermissions, isLoadingPermissions } = usePermissions();

  // Show loading state while permissions are being fetched
  if (isLoadingPermissions) {
    return fallback;
  }

  // If no permissions required, always render children
  if (!perms_required || perms_required.length === 0) {
    return children;
  }

  // Check if user has required permissions
  const hasRequiredPermissions = hasPermissions(perms_required);

  // Render children if permissions are satisfied, otherwise render fallback
  return hasRequiredPermissions ? children : fallback;
};

export default PermissionGate;
