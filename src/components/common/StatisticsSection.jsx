import { useState } from "react";

const StatisticsSection = ({
  statistics = [],
  activeTab = "",
  onTabChange = () => {},
  showSection = false,
}) => {
  if (!showSection || !statistics.length) {
    return null;
  }

  return (
    <div className="bg-white dark:bg-gray-800 border-t border-b border-gray-200 dark:border-gray-700 mt-0">
      <div className="px-4 py-4">
        <div className="flex flex-wrap gap-8 overflow-x-auto">
          {statistics.map((stat, index) => {
            const isActive = activeTab === stat.key;
            const isClickable = stat.key !== "total";
            return (
              <div
                key={stat.key || index}
                className={`flex flex-col items-center min-w-0 transition-all duration-200 relative ${
                  isClickable ? "cursor-pointer" : "cursor-default"
                } ${
                  isActive
                    ? "text-yellow-600 dark:text-yellow-400"
                    : "text-gray-600 dark:text-gray-400"
                } ${
                  isClickable && !isActive
                    ? "hover:text-gray-800 dark:hover:text-gray-200"
                    : ""
                }`}
                onClick={() => isClickable && onTabChange(stat.key)}
              >
                <div
                  className={`text-xl font-bold mb-1 ${
                    isActive
                      ? "text-yellow-600 dark:text-yellow-400"
                      : "text-gray-700 dark:text-gray-300"
                  }`}
                >
                  {stat.value?.toLocaleString() || "0"}
                </div>
                <div
                  className={`text-sm text-center whitespace-nowrap pb-3 relative ${
                    isActive
                      ? "text-yellow-600 dark:text-yellow-400 font-medium"
                      : "text-gray-600 dark:text-gray-400"
                  }`}
                >
                  {stat.label}
                  {isActive && (
                    <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-yellow-400 rounded-full"></div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default StatisticsSection;
