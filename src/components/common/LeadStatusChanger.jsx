import { useState } from "react";
import { ChevronDown, Check, Loader2 } from "lucide-react";

const LeadStatusChanger = ({ 
  currentStatus = "Pending", 
  leadId, 
  leadName = "Lead",
  onStatusChange,
  disabled = false,
  size = "sm" // sm, md, lg
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  // Status options with colors and
  const statusOptions = [
    {
      value: "Hot",
      label: "Hot",
      color: "bg-[#ff0000]",
      bgColor: "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400",

    },
    {
      value: "Warm",
      label: "Warm", 
      color: "bg-[#369dc9]",
      bgColor: "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400",

    },
    {
      value: "Pending",
      label: "Pending",
      color: "bg-[#ffb800]",
      bgColor: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",

    },
    {
      value: "Cold",
      label: "Cold",
      color: "bg-[#1c5b41]",
      bgColor: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400", 

    }
  ];

  // Get current status info
  const currentStatusInfo = statusOptions.find(option => option.value === currentStatus) || statusOptions[2];

  // Size configurations
  const sizeConfig = {
    sm: {
      button: "px-2 py-1 text-xs",
      dropdown: "w-48",
      icon: "w-3 h-3",
      text: "text-xs"
    },
    md: {
      button: "px-3 py-2 text-sm",
      dropdown: "w-56", 
      icon: "w-4 h-4",
      text: "text-sm"
    },
    lg: {
      button: "px-4 py-3 text-base",
      dropdown: "w-64",
      icon: "w-5 h-5", 
      text: "text-base"
    }
  };

  const config = sizeConfig[size];

  const handleStatusSelect = async (newStatus) => {
    if (newStatus === currentStatus || disabled || isUpdating) return;

    setIsUpdating(true);
    try {
      if (onStatusChange) {
        await onStatusChange(leadId, newStatus);
      }
      setIsOpen(false);
    } catch (error) {
      console.error("Error updating status:", error);
      // Keep dropdown open on error so user can try again
    } finally {
      setIsUpdating(false);
    }
  };

  const handleKeyDown = (e, status) => {
    if (e.key === "Enter" || e.key === " ") {
      e.preventDefault();
      handleStatusSelect(status);
    } else if (e.key === "Escape") {
      setIsOpen(false);
    }
  };

  return (
    <div className="relative inline-block">
      {/* Status Button */}
      <button
        onClick={() => !disabled && !isUpdating && setIsOpen(!isOpen)}
        disabled={disabled || isUpdating}
        className={`
          inline-flex items-center gap-2 rounded-full font-medium transition-all duration-200
          ${config.button} ${currentStatusInfo.bgColor}
          ${disabled || isUpdating 
            ? 'opacity-50 cursor-not-allowed' 
            : 'hover:opacity-80 cursor-pointer'
          }
        `}
        aria-label={`Change status for ${leadName}. Current status: ${currentStatus}`}
        aria-expanded={isOpen}
        aria-haspopup="listbox"
      >
        {/* Status indicator dot */}
        <span className={`${config.icon} rounded-full ${currentStatusInfo.color}`}></span>
        
        {/* Status text */}
        <span className={config.text}>{currentStatus}</span>
        
        {/* Loading or dropdown icon */}
        {isUpdating ? (
          <Loader2 className={`${config.icon} animate-spin`} />
        ) : (
          <ChevronDown 
            className={`${config.icon} transition-transform duration-200 ${
              isOpen ? 'rotate-180' : ''
            }`} 
          />
        )}
      </button>

      {/* Dropdown Menu */}
      {isOpen && !disabled && !isUpdating && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
            aria-hidden="true"
          />
          
          {/* Dropdown */}
          <div
            className={`
              absolute bottom-full left-0 mb-1 ${config.dropdown}
              bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700
              z-20 py-2 max-h-64 overflow-y-auto
            `}
            role="listbox"
            aria-label="Status options"
          >
            {/* Header */}
            <div className="px-3 py-2 border-b border-gray-200 dark:border-gray-700">
              <p className={`font-medium text-gray-900 dark:text-white ${config.text}`}>
                Change Status
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                {leadName}
              </p>
            </div>

            {/* Status Options */}
            <div className="py-1">
              {statusOptions.map((option) => (
                <button
                  key={option.value}
                  onClick={() => handleStatusSelect(option.value)}
                  onKeyDown={(e) => handleKeyDown(e, option.value)}
                  className={`
                    w-full px-3 py-2 text-left hover:bg-gray-50 dark:hover:bg-gray-700 
                    transition-colors duration-150 focus:outline-none focus:bg-gray-50 dark:focus:bg-gray-700
                    ${option.value === currentStatus ? 'bg-gray-50 dark:bg-gray-700' : ''}
                  `}
                  role="option"
                  aria-selected={option.value === currentStatus}
                >
                  <div className="flex items-center gap-3">
                    {/* Status indicator */}
                    <span className={`w-3 h-3 rounded-full ${option.color}`}></span>
                    
                    {/* Status info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <span className={`font-medium text-gray-900 dark:text-white ${config.text}`}>
                          {option.label}
                        </span>
                        {option.value === currentStatus && (
                          <Check className="w-3 h-3 text-green-600 dark:text-green-400" />
                        )}
                      </div>

                    </div>
                  </div>
                </button>
              ))}
            </div>

            {/* Footer */}
            {/*<div className="px-3 py-2 border-t border-gray-200 dark:border-gray-700">*/}
            {/*  <p className="text-xs text-gray-500 dark:text-gray-400">*/}
            {/*    Click to update lead status*/}
            {/*  </p>*/}
            {/*</div>*/}
          </div>
        </>
      )}
    </div>
  );
};

export default LeadStatusChanger;
