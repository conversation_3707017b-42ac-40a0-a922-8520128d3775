import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import Sidebar from "../Sidebar";
import MobileSidebar from "../MobileSidebar";
import Navbar from "../Navbar";
import { usePermissions } from "../../contexts/PermissionContext";
import { Shield, ArrowLeft } from "lucide-react";

function PrivateLayout({ children, perm_required = [] }) {
  const navigate = useNavigate();
  const { hasPermissions, isLoadingPermissions } = usePermissions();
  const [showUnauthorized, setShowUnauthorized] = useState(false);

  // Check permissions when component mounts or permissions change
  useEffect(() => {
    // Skip permission check if no permissions required or still loading
    if (perm_required.length === 0 || isLoadingPermissions) {
      setShowUnauthorized(false);
      return;
    }

    // Check if user has required permissions
    const hasRequiredPermissions = hasPermissions(perm_required);

    if (!hasRequiredPermissions) {
      // Show unauthorized content instead of redirecting
      setShowUnauthorized(true);
    } else {
      setShowUnauthorized(false);
    }
  }, [perm_required, hasPermissions, isLoadingPermissions]);

  // Unauthorized content component
  const UnauthorizedContent = () => {
    const handleGoBack = () => {
      navigate(-1); // Go back to previous page
    };

    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="max-w-lg w-full">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-8 text-center shadow-lg">
            {/* Icon */}
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 dark:bg-red-900/20 mb-6">
              <Shield className="h-8 w-8 text-red-600 dark:text-red-400" />
            </div>

            {/* Title */}
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Access Denied
            </h1>

            {/* Message */}
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              You don't have permission to access this page.
            </p>

            {/* Action Buttons */}
            <div className="flex justify-center">
              <button
                onClick={handleGoBack}
                className="flex items-center justify-center px-6 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors duration-200"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Go Back
              </button>
            </div>

            {/* Additional Info */}
            <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
              <p className="text-xs text-gray-500 dark:text-gray-400">
                If you believe this is an error, please contact your
                administrator.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Show loading or nothing while permissions are being checked
  // if (isLoadingPermissions && perm_required.length > 0) {
  //   return (
  //     <div className="h-screen bg-gray-50 dark:bg-black flex items-center justify-center">
  //       <div className="text-gray-600 dark:text-gray-400">Loading...</div>
  //     </div>
  //   );
  // }
  return (
    <div className="h-screen bg-gray-50 dark:bg-black flex overflow-hidden transition-colors duration-200">
      {/* Sidebar Components */}
      <Sidebar />
      <MobileSidebar />

      {/* Main content area */}
      <div className="flex-1 overflow-hidden flex flex-col h-full">
        {/* Navbar Component */}
        <Navbar />

        {/* Page content - Scrollable */}
        <main className="flex-1 overflow-y-auto p-3 md:p-6 bg-[rgba(54,201,95,0.04)] dark:bg-gray-900 transition-colors duration-200 scrollbar-thin">
          {showUnauthorized ? <UnauthorizedContent /> : children}

          {/* Footer */}
          <footer className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
            <div className="text-center text-sm text-gray-600 dark:text-gray-400">
              Copyright © Designed & Developed by{" "}
              <a
                href="https://minesoftwares.com/"
                className="text-[#1c5b41] font-medium"
              >
                Mine Softwares Ltd
              </a>{" "}
              2025
            </div>
          </footer>
        </main>
      </div>
    </div>
  );
}

export default PrivateLayout;
