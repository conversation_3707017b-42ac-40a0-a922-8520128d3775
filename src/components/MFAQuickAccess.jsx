import { useNavigate } from 'react-router-dom';
import { Button } from 'primereact/button';
import { Shield } from 'lucide-react';
import { hasMFAMethodsStored, navigateToMFASelection } from '../utils/mfaUtils';

/**
 * Component that provides quick access to MFA method selection
 * Shows only if MFA methods are stored (user is in MFA flow)
 */
const MFAQuickAccess = ({ className = '', variant = 'button' }) => {
  const navigate = useNavigate();

  // Only show if MFA methods are available
  if (!hasMFAMethodsStored()) {
    return null;
  }

  const handleMFAAccess = () => {
    const success = navigateToMFASelection(navigate);
    if (!success) {
      // Fallback to login if no data available
      navigate('/login');
    }
  };

  if (variant === 'link') {
    return (
      <button
        onClick={handleMFAAccess}
        className={`inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium transition-colors duration-200 ${className}`}
      >
        <Shield className="h-4 w-4" />
        Continue MFA Setup
      </button>
    );
  }

  return (
    <Button
      label="Continue MFA Setup"
      icon={<Shield className="h-4 w-4" />}
      onClick={handleMFAAccess}
      severity="info"
      outlined
      className={className}
    />
  );
};

export default MFAQuickAccess;
