import { useState, useEffect } from "react";

//NOTE: if an item is passed in, it's an edit form
const AnchorForm = ({ item, onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone_number: "",
    account_id: "",
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Populate form with item data for editing
  useEffect(() => {
    if (item) {
      setFormData({
        name: item.name || "",
        email: item.email || "",
        phone_number: item.phoneNumber || item.phone_number || "",
        account_id: item.account_id || item.accountId || "",
      });
    }
  }, [item]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = "Anchor name is required";
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Email is invalid";
    }

    if (!formData.phone_number.trim()) {
      newErrors.phone_number = "Phone number is required";
    }

    if (!formData.account_id.trim()) {
      newErrors.account_id = "Account ID is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare data for API integration - use the exact format expected by the backend
      const apiData = {
        name: formData.name.trim(),
        email: formData.email.trim(),
        phone_number: formData.phone_number.trim(),
        account_id: formData.account_id.trim(),
      };

      console.log("API data prepared:", apiData);

      if (onSubmit) {
        await onSubmit(apiData, item);
      }

      console.log(item ? "Updated anchor:" : "Created anchor:", apiData);
      onClose();
    } catch (error) {
      console.error("Error submitting anchor form:", error);
      // Here you would typically show an error notification to the user
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-4">
        {/* Name */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Name *
          </label>
          <input
            type="text"
            name="name"
            value={formData.name}
            onChange={handleChange}
            className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 outline-none transition-colors duration-200 ${
              errors.name
                ? "border-red-500 dark:border-red-400"
                : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
            }`}
            placeholder="Enter anchor name"
          />
          {errors.name && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.name}
            </p>
          )}
        </div>

        {/* Account ID */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Account ID *
          </label>
          <input
            type="text"
            name="account_id"
            value={formData.account_id}
            onChange={handleChange}
            className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 outline-none transition-colors duration-200 ${
              errors.account_id
                ? "border-red-500 dark:border-red-400"
                : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
            }`}
            placeholder="Enter account ID"
          />
          {errors.account_id && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.account_id}
            </p>
          )}
        </div>

        {/* Email */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Email *
          </label>
          <input
            type="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 outline-none transition-colors duration-200 ${
              errors.email
                ? "border-red-500 dark:border-red-400"
                : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
            }`}
            placeholder="Enter email address"
          />
          {errors.email && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.email}
            </p>
          )}
        </div>

        {/* Phone Number */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Phone Number *
          </label>
          <input
            type="tel"
            name="phone_number"
            value={formData.phone_number}
            onChange={handleChange}
            className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 outline-none transition-colors duration-200 ${
              errors.phone_number
                ? "border-red-500 dark:border-red-400"
                : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
            }`}
            placeholder="Enter phone number"
          />
          {errors.phone_number && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.phone_number}
            </p>
          )}
        </div>


      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
        <button
          type="button"
          onClick={onClose}
          className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-200"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isSubmitting}
          className="px-4 py-2 text-sm font-medium text-white bg-[#165026] hover:bg-green-700 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSubmitting
            ? "Saving..."
            : item
            ? "Update Anchor"
            : "Create Anchor"}
        </button>
      </div>
    </form>
  );
};

export default AnchorForm;
