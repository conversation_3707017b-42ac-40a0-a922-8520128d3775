import { useState, useEffect } from "react";
import {
  Phone,
  PhoneMissed,
  Mic,
  MicOff,
  Pause,
  Play,
  Upload,
  X,
} from "lucide-react";
import Select from "react-select";
import {
  purposesService,
  formatPurposesForTable,
} from "../../services/purposesService";
import { customerServiceService } from "../../services/customerServiceService";
import { validateLoanActivityData } from "../../utils/validationUtils";

const LoanCallForm = ({ item, onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    callType: "",
    callStatus: "",
    purposeId: "",
    loanAccountNo: "",
    loanBalance: "",
    arrearsDays: "",
    customerFeedbackCategoryId: "",
    notes: "",
    followUpDate: "",
    followUpTime: "",
    attachments: [],
    // Additional fields for new API
    interactionType: "call", // Default to call
    callDurationMinutes: "",
    followupStatus: "",
    viaApi: false,
    apiCallReference: "",
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [lastSubmissionTime, setLastSubmissionTime] = useState(0);

  // Purposes state
  const [purposes, setPurposes] = useState([]);
  const [purposesLoading, setPurposesLoading] = useState(false);

  // Customer feedback categories state
  const [feedbackCategories, setFeedbackCategories] = useState([]);
  const [feedbackCategoriesLoading, setFeedbackCategoriesLoading] = useState(false);

  // Call functionality states
  const [isCallActive, setIsCallActive] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [callTimer, setCallTimer] = useState(0);
  const [isMuted, setIsMuted] = useState(false);
  const [isOnHold, setIsOnHold] = useState(false);

  // Fetch purposes on component mount
  const fetchPurposes = async () => {
    try {
      setPurposesLoading(true);
      const response = await purposesService.getAll();
      const formattedPurposes = formatPurposesForTable(response);

      // Convert to React Select format
      const purposeOptions = formattedPurposes.map((purpose) => ({
        value: purpose.id,
        label: purpose.name,
        purpose: purpose,
      }));

      setPurposes(purposeOptions);
    } catch (error) {
      console.error("Error fetching purposes:", error);
      setPurposes([]);
    } finally {
      setPurposesLoading(false);
    }
  };

  // Fetch customer feedback categories
  const fetchFeedbackCategories = async () => {
    try {
      setFeedbackCategoriesLoading(true);
      const response = await customerServiceService.getCustomerFeedbackCategories();

      // Convert to React Select format
      const categoryOptions = response.map((category) => ({
        value: category.id,
        label: category.name,
      }));

      setFeedbackCategories(categoryOptions);
    } catch (error) {
      console.error("Error fetching feedback categories:", error);
      setFeedbackCategories([]);
    } finally {
      setFeedbackCategoriesLoading(false);
    }
  };



  useEffect(() => {
    fetchPurposes();
    fetchFeedbackCategories();
  }, []);

  useEffect(() => {
    console.log("=== LOANCALLFORM USEEFFECT TRIGGERED ===");
    console.log("LoanCallForm received item:", item);
    console.log("item?.id:", item?.id);
    console.log("item?.leadId:", item?.leadId);
    console.log("item?.name:", item?.name);
    console.log("====================================");

    if (item) {
      // Only update form data if the item has changed, and preserve existing form values
      setFormData(prevFormData => ({
        callType: item.callType || prevFormData.callType || "",
        callStatus: item.callStatus || prevFormData.callStatus || "",
        purposeId: item.purposeId || prevFormData.purposeId || "",
        customerFeedbackCategoryId: item.customerFeedbackCategoryId || prevFormData.customerFeedbackCategoryId || "",
        notes: item.notes || prevFormData.notes || "",
        followUpDate: item.followUpDate || prevFormData.followUpDate || "",
        followUpTime: item.followUpTime || prevFormData.followUpTime || "",
        attachments: item.attachments || prevFormData.attachments || [],
        // Preserve other fields that might have been set by user
        loanAccountNo: prevFormData.loanAccountNo || "",
        loanBalance: prevFormData.loanBalance || "",
        arrearsDays: prevFormData.arrearsDays || "",
        interactionType: prevFormData.interactionType || "call",
        callDurationMinutes: prevFormData.callDurationMinutes || "",
        followupStatus: prevFormData.followupStatus || "pending",
        viaApi: prevFormData.viaApi || false,
        apiCallReference: prevFormData.apiCallReference || "",
      }));
    }
  }, [item]);

  const handleChange = (e) => {
    const { name, value } = e.target;

    console.log(`=== HANDLE CHANGE ===`);
    console.log(`Field: ${name}, Value: ${value}`);

    let processedValue = value;

    // Special handling for loan account number - only allow digits and limit to 13 characters
    if (name === "loanAccountNo") {
      // Remove any non-digit characters
      processedValue = value.replace(/\D/g, "");
      // Limit to 13 digits
      processedValue = processedValue.slice(0, 13);
    }

    setFormData((prev) => {
      const newData = {
        ...prev,
        [name]: processedValue,
      };
      console.log(`Updated formData for ${name}:`, newData[name]);
      return newData;
    });

    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  // Handle React Select changes
  const handleSelectChange = (selectedOption, actionMeta) => {
    const { name } = actionMeta;
    const value = selectedOption ? selectedOption.value : "";

    console.log(`=== HANDLE SELECT CHANGE ===`);
    console.log(`Field: ${name}, Selected Option:`, selectedOption);
    console.log(`Value: ${value}`);

    setFormData((prev) => {
      const newData = {
        ...prev,
        [name]: value,
      };
      console.log(`Updated formData for ${name}:`, newData[name]);
      console.log(`Full formData:`, newData);
      return newData;
    });

    // Clear error when user makes selection
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  // Handle file upload
  const handleFileUpload = (e) => {
    const files = Array.from(e.target.files);
    const newAttachments = files.map((file) => ({
      id: Date.now() + Math.random(), // Temporary ID
      file: file,
      name: file.name,
      size: file.size,
      type: file.type,
    }));

    setFormData((prev) => ({
      ...prev,
      attachments: [...prev.attachments, ...newAttachments],
    }));

    // Clear the input
    e.target.value = "";
  };

  // Remove attachment
  const removeAttachment = (attachmentId) => {
    setFormData((prev) => ({
      ...prev,
      attachments: prev.attachments.filter((att) => att.id !== attachmentId),
    }));
  };



  const validateForm = () => {
    console.log("=== VALIDATE FORM START ===");
    console.log("formData.callType:", formData.callType);
    console.log("formData.callStatus:", formData.callStatus);
    console.log("formData.purposeId:", formData.purposeId);

    const newErrors = {};
    if (!formData.callType) {
      console.log("Call type validation failed");
      newErrors.callType = "Call type is required";
    }
    if (!formData.callStatus) {
      console.log("Call status validation failed");
      newErrors.callStatus = "Call status is required";
    }
    if (!formData.purposeId) {
      console.log("Purpose validation failed");
      newErrors.purposeId = "Purpose is required";
    }
    
    // Validate loan account number (13 digits)
    if (formData.loanAccountNo && formData.loanAccountNo.trim()) {
      if (!/^\d{13}$/.test(formData.loanAccountNo.trim())) {
        newErrors.loanAccountNo = "Loan account number must be exactly 13 digits";
      }
    }
    
    // Validate loan balance (must be a positive number)
    if (formData.loanBalance && formData.loanBalance.trim()) {
      const balance = parseFloat(formData.loanBalance);
      if (isNaN(balance) || balance < 0) {
        newErrors.loanBalance = "Loan balance must be a positive number";
      }
    }
    
    // Validate arrears days (must be a non-negative integer)
    if (formData.arrearsDays && formData.arrearsDays.trim()) {
      const days = parseInt(formData.arrearsDays);
      if (isNaN(days) || days < 0 || !Number.isInteger(days)) {
        newErrors.arrearsDays = "Arrears days must be a non-negative integer";
      }
    }
    
    console.log("New errors found:", newErrors);
    console.log("Setting errors to:", newErrors);
    setErrors(newErrors);
    const isValid = Object.keys(newErrors).length === 0;
    console.log("Form is valid:", isValid);
    console.log("=== VALIDATE FORM END ===");
    return isValid;
  };

  // Call functionality handlers
  const handleStartCall = async () => {
    setIsConnecting(true);

    try {
      // Simulate API call with 2 second delay
      await new Promise((resolve) => setTimeout(resolve, 2000));

      setIsCallActive(true);
      setCallTimer(0);
      setIsMuted(false);
      setIsOnHold(false);
    } catch (error) {
      console.error("Failed to initiate call:", error);
    } finally {
      setIsConnecting(false);
    }
  };

  const handleEndCall = async () => {
    try {
      setIsCallActive(false);
      setIsConnecting(false);
      setCallTimer(0);
      setIsMuted(false);
      setIsOnHold(false);
    } catch (error) {
      console.error("Failed to end call:", error);
    }
  };

  const handleToggleMute = () => {
    setIsMuted(!isMuted);
  };

  const handleToggleHold = () => {
    setIsOnHold(!isOnHold);
  };

  // Timer effect for call duration
  useEffect(() => {
    let interval;
    if (isCallActive && !isOnHold) {
      interval = setInterval(() => {
        setCallTimer((prev) => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isCallActive, isOnHold]);

  // Format timer display
  const formatTimer = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    console.log("=== FORM SUBMISSION STARTED ===");
    console.log("Current formData:", formData);
    console.log("Current errors:", errors);
    console.log("===============================");

    // Prevent double submission with debounce (1 second)
    const currentTime = Date.now();
    if (isSubmitting || (currentTime - lastSubmissionTime < 1000)) {
      console.log("Form is already being submitted or submitted too recently, ignoring duplicate submission");
      return;
    }

    console.log("=== VALIDATING FORM ===");
    const isValid = validateForm();
    console.log("Form validation result:", isValid);
    console.log("Errors after validation:", errors);
    console.log("======================");

    if (!isValid) return;

    setIsSubmitting(true);
    setLastSubmissionTime(currentTime);
    try {
      // Combine date and time into a single datetime
      let followUpDateTime = null;
      if (formData.followUpDate && formData.followUpTime) {
        // Create datetime string in ISO format
        const dateTimeString = `${formData.followUpDate}T${formData.followUpTime}:00.000Z`;
        followUpDateTime = dateTimeString;
      } else if (formData.followUpDate) {
        // If only date is provided, set time to current time
        const currentTime = new Date().toTimeString().slice(0, 8);
        const dateTimeString = `${formData.followUpDate}T${currentTime}.000Z`;
        followUpDateTime = dateTimeString;
      }

      // Get current user ID from localStorage
      const currentUser = JSON.parse(localStorage.getItem("logged_in_user") || "{}");
      const rmUserId = currentUser.id;

      if (!rmUserId) {
        alert("Error: Unable to get current user information. Please log in again.");
        return;
      }

      // Debug form data before validation
      console.log("=== FORM DATA BEFORE VALIDATION ===");
      console.log("formData.callType:", formData.callType);
      console.log("formData.callStatus:", formData.callStatus);
      console.log("formData.purposeId:", formData.purposeId);
      console.log("rmUserId:", rmUserId);
      console.log("item?.id:", item?.id);
      console.log("===================================");

      // First, validate using the local form validation
      const formValidationErrors = {};
      if (!formData.callType) formValidationErrors.callType = "Call type is required";
      if (!formData.callStatus) formValidationErrors.callStatus = "Call status is required";
      if (!formData.purposeId) formValidationErrors.purposeId = "Purpose is required";

      if (Object.keys(formValidationErrors).length > 0) {
        console.error("Form validation errors:", formValidationErrors);
        setErrors(formValidationErrors);
        alert(`Please fill in required fields: ${Object.values(formValidationErrors).join(', ')}`);
        return;
      }

      // Validate form data using the new validation utility (but skip UUID validation for now)
      const validationData = {
        rmUserId: rmUserId,
        loanClientId: item?.id || item?.leadId,
        loanAccountNumber: formData.loanAccountNo,
        purposeId: formData.purposeId,
        loanBalance: formData.loanBalance,
        arrearsDays: formData.arrearsDays ? parseInt(formData.arrearsDays) : null,
        comment: formData.notes,
        interactionType: formData.interactionType || "call",
        callStatus: formData.callStatus,
        callDurationMinutes: formData.callDurationMinutes ? parseInt(formData.callDurationMinutes) : null,
        nextFollowupDate: followUpDateTime,
        followupStatus: formData.followupStatus,
        viaApi: formData.viaApi,
        apiCallReference: formData.apiCallReference,
      };

      console.log("=== VALIDATION DATA ===");
      console.log("validationData:", validationData);
      console.log("=======================");

      // Skip the strict validation for now to avoid UUID issues
      // const validation = validateLoanActivityData(validationData);
      // if (!validation.isValid) {
      //   console.error("Validation errors:", validation.errors);
      //   alert(`Validation failed: ${validation.errors.join(', ')}`);
      //   return;
      // }

      console.log("=== CREATING LOAN ACTIVITY DATA ===");
      console.log("item at submission time:", item);
      console.log("item?.id:", item?.id);
      console.log("Validation passed successfully");
      console.log("===================================");

      // Create FormData for multipart/form-data submission
      const formDataToSend = new FormData();

      // Add required field (current user ID)
      formDataToSend.append("rm_user_id", rmUserId);

      // Add optional fields only if they have values
      if (item?.id || item?.leadId) {
        formDataToSend.append("loan_client_id", item.id || item.leadId);
      }
      if (formData.loanAccountNo && formData.loanAccountNo.trim()) {
        formDataToSend.append("loan_account_number", formData.loanAccountNo.trim());
      }
      if (formData.purposeId) {
        formDataToSend.append("purpose_id", formData.purposeId);
      }
      if (formData.loanBalance && formData.loanBalance.trim()) {
        formDataToSend.append("loan_balance", formData.loanBalance.trim());
      }
      if (formData.arrearsDays && formData.arrearsDays.trim()) {
        formDataToSend.append("arrears_days", formData.arrearsDays.trim());
      }
      if (formData.notes && formData.notes.trim()) {
        formDataToSend.append("comment", formData.notes.trim());
      }
      if (formData.viaApi !== undefined) {
        formDataToSend.append("via_api", formData.viaApi.toString());
      }
      if (formData.apiCallReference && formData.apiCallReference.trim()) {
        formDataToSend.append("api_call_reference", formData.apiCallReference.trim());
      }
      if (formData.interactionType) {
        formDataToSend.append("interaction_type", formData.interactionType);
      }
      if (formData.callStatus) {
        formDataToSend.append("call_status", formData.callStatus);
      }
      if (formData.callDurationMinutes && formData.callDurationMinutes.trim()) {
        formDataToSend.append("call_duration_minutes", formData.callDurationMinutes.trim());
      }
      if (followUpDateTime) {
        formDataToSend.append("next_followup_date", followUpDateTime);
      }
      if (formData.followupStatus) {
        formDataToSend.append("followup_status", formData.followupStatus);
      }

      // Add file attachments
      if (formData.attachments && formData.attachments.length > 0) {
        formData.attachments.forEach(attachment => {
          formDataToSend.append('attachments', attachment.file);
        });
      }

      // Console log the prepared data
      console.log("=== LOAN ACTIVITY FORM SUBMISSION DATA ===");
      console.log("FormData being sent:");
      console.log("- rm_user_id:", rmUserId);
      console.log("- loan_client_id:", item?.id || item?.leadId);
      console.log("- loan_account_number:", formData.loanAccountNo || "not provided");
      console.log("- purpose_id:", formData.purposeId || "not provided");
      console.log("- loan_balance:", formData.loanBalance || "not provided");
      console.log("- arrears_days:", formData.arrearsDays || "not provided");
      console.log("- comment:", formData.notes || "not provided");
      console.log("- interaction_type:", formData.interactionType);
      console.log("- call_status:", formData.callStatus || "not provided");
      console.log("- call_duration_minutes:", formData.callDurationMinutes || "not provided");
      console.log("- next_followup_date:", followUpDateTime || "not provided");
      console.log("- followup_status:", formData.followupStatus || "not provided");
      console.log("- via_api:", formData.viaApi);
      console.log("- api_call_reference:", formData.apiCallReference || "not provided");
      console.log("- attachment files:", formData.attachments.length, "files");

      // Log FormData contents (for debugging)
      console.log("FormData contents:");
      for (let [key, value] of formDataToSend.entries()) {
        if (value instanceof File) {
          console.log(
            `${key}: [File] ${value.name} (${value.size} bytes, ${value.type})`
          );
        } else {
          console.log(`${key}: ${value}`);
        }
      }
      console.log("=====================================");

      // Call the parent component's onSubmit callback with the form data
      // The parent component will handle the actual API call to prevent duplicates
      try {
        console.log("Calling parent onSubmit callback with form data...");

        // Convert FormData to a regular object for the parent component
        const callData = {
          rm_user_id: rmUserId,
          loan_client_id: item?.id || item?.leadId,
          loan_account_number: formData.loanAccountNo?.trim() || null,
          purpose_id: formData.purposeId || null,
          loan_balance: formData.loanBalance?.trim() || null,
          arrears_days: formData.arrearsDays?.trim() ? parseInt(formData.arrearsDays) : null,
          comment: formData.notes?.trim() || null,
          via_api: formData.viaApi,
          api_call_reference: formData.apiCallReference?.trim() || null,
          interaction_type: formData.interactionType || "call",
          call_status: formData.callStatus || null,
          call_duration_minutes: formData.callDurationMinutes?.trim() ? parseInt(formData.callDurationMinutes) : null,
          next_followup_date: followUpDateTime || null,
          followup_status: formData.followupStatus || null,
        };

        // Extract files for upload
        const files = formData.attachments.map(att => att.file);

        console.log("Prepared call data for parent:", callData);
        console.log("Files to upload:", files.length);

        // Call the parent's onSubmit function
        const result = await onSubmit?.(callData, item, files);

        if (result !== false) {
          // Only close if the parent component indicates success
          onClose();
        }
      } catch (error) {
        console.error("Error in parent onSubmit callback:", error);
        alert(error.message || "Failed to create loan activity");
      }
    } catch (err) {
      console.error("Failed to submit loan call dialog form:", err);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Custom styles for React Select
  const selectStyles = {
    control: (provided, state) => ({
      ...provided,
      minHeight: "48px",
      borderColor: state.isFocused
        ? "#10b981"
        : errors[state.selectProps.name]
        ? "#ef4444"
        : "#d1d5db",
      boxShadow: "none",
      "&:hover": {
        borderColor: state.isFocused ? "#10b981" : "#9ca3af",
      },
    }),
    option: (provided, state) => ({
      ...provided,
      backgroundColor: state.isSelected
        ? "#10b981"
        : state.isFocused
        ? "#f3f4f6"
        : "white",
      color: state.isSelected ? "white" : "#374151",
    }),
    menu: (provided) => ({
      ...provided,
      zIndex: 9999,
    }),
  };

  return (
    <div className="flex flex-col h-[80vh]">
      <form onSubmit={handleSubmit} className="flex flex-col h-full">
        {/* Scrollable Content */}
        <div className="flex-1 overflow-y-auto overflow-x-hidden scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 px-1">
          <div className="flex flex-col gap-6 pb-4">
      {/* Call Section - Always show at the top */}
      <div className="border-b border-gray-200 dark:border-gray-600 min-h-[100px] flex items-center">
        {!isCallActive ? (
          // Call Button - Aligned to left
          <div className="flex justify-start w-full">
            <button
              type="button"
              onClick={handleStartCall}
              disabled={isConnecting}
              className="inline-flex items-center px-6 py-3 bg-green-500 hover:bg-green-600 disabled:bg-green-400 text-white font-medium rounded-lg transition-colors duration-200 shadow-sm disabled:cursor-not-allowed"
            >
              <Phone size={20} className="mr-2" />
              {isConnecting ? "Calling..." : "Call"}
            </button>
          </div>
        ) : (
          // Call Controls
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 py-0 w-full">
            {/* Current User Display */}
            <div className="mb-3 text-sm" style={{ color: "#7e7e7e" }}>
              Currently talking to:{" "}
              <span className="font-medium" style={{ color: "#7e7e7e" }}>
                {item?.name || "Client"}
              </span>
            </div>

            <div className="flex items-center justify-between">
              {/* Timer */}
              <div className="text-lg font-mono" style={{ color: "#7e7e7e" }}>
                {formatTimer(callTimer)}
              </div>

              {/* Call Control Buttons */}
              <div className="flex items-center space-x-3">
                {/* End Call Button */}
                <button
                  type="button"
                  onClick={handleEndCall}
                  className="inline-flex items-center px-6 py-3 font-medium rounded-lg transition-colors duration-200"
                  style={{ backgroundColor: "#f46b68" }}
                  onMouseEnter={(e) =>
                    (e.target.style.backgroundColor = "#e55a57")
                  }
                  onMouseLeave={(e) =>
                    (e.target.style.backgroundColor = "#f46b68")
                  }
                >
                  <PhoneMissed size={16} className="mr-1 text-white" />
                  <span className="text-white">End</span>
                </button>

                {/* Mute/Unmute Button */}
                <button
                  type="button"
                  onClick={handleToggleMute}
                  className={`inline-flex items-center px-6 py-3 font-medium rounded-lg transition-colors duration-200 ${
                    isMuted ? "bg-transparent" : ""
                  }`}
                  style={!isMuted ? { backgroundColor: "#369dc9" } : {}}
                  onMouseEnter={(e) => {
                    if (!isMuted) {
                      e.target.style.backgroundColor = "#2a7ba7";
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isMuted) {
                      e.target.style.backgroundColor = "#369dc9";
                    }
                  }}
                >
                  {isMuted ? (
                    <>
                      <Mic size={16} className="mr-1 text-gray-500" />
                      <span className="text-gray-500">Unmute</span>
                    </>
                  ) : (
                    <>
                      <MicOff size={16} className="mr-1 text-white" />
                      <span className="text-white">Mute</span>
                    </>
                  )}
                </button>

                {/* Hold/Unhold Button */}
                <button
                  type="button"
                  onClick={handleToggleHold}
                  className={`inline-flex items-center px-6 py-3 font-medium rounded-lg transition-colors duration-200 ${
                    isOnHold ? "bg-transparent" : ""
                  }`}
                  style={!isOnHold ? { backgroundColor: "#ffb800" } : {}}
                  onMouseEnter={(e) => {
                    if (!isOnHold) {
                      e.target.style.backgroundColor = "#e6a600";
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isOnHold) {
                      e.target.style.backgroundColor = "#ffb800";
                    }
                  }}
                >
                  {isOnHold ? (
                    <>
                      <Play size={16} className="mr-1 text-gray-500" />
                      <span className="text-gray-500">Unhold</span>
                    </>
                  ) : (
                    <>
                      <Pause size={16} className="mr-1 text-white" />
                      <span className="text-white">Hold</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Call Type */}
      <div className="flex items-center gap-4">
        <label
          className="text-sm font-medium w-32 flex-shrink-0"
          style={{ color: "#7e7e7e" }}
        >
          Call Type *
        </label>
        <div className="flex-1">
          <select
            name="callType"
            value={formData.callType}
            onChange={handleChange}
            className={`w-full px-4 py-3 border rounded-lg bg-white outline-none transition-colors duration-200 ${
              errors.callType
                ? "border-red-500"
                : "border-gray-300 focus:border-green-500 hover:border-gray-400"
            }`}
            style={{ color: "#7e7e7e" }}
          >
            <option value="">--select--</option>
            <option value="First Contact">First Contact</option>
            <option value="Follow Up">Follow Up</option>
          </select>
          {errors.callType && (
            <p className="mt-1 text-sm text-red-600">{errors.callType}</p>
          )}
        </div>
      </div>

      {/* Call Status */}
      <div className="flex items-center gap-4">
        <label
          className="text-sm font-medium w-32 flex-shrink-0"
          style={{ color: "#7e7e7e" }}
        >
          Call Status *
        </label>
        <div className="flex-1">
          <select
            name="callStatus"
            value={formData.callStatus}
            onChange={handleChange}
            className={`w-full px-4 py-3 border rounded-lg bg-white outline-none transition-colors duration-200 ${
              errors.callStatus
                ? "border-red-500"
                : "border-gray-300 focus:border-green-500 hover:border-gray-400"
            }`}
            style={{ color: "#7e7e7e" }}
          >
            <option value="">--select--</option>
            <option value="Success">Success</option>
            <option value="Declined">Declined</option>
          </select>
          {errors.callStatus && (
            <p className="mt-1 text-sm text-red-600">{errors.callStatus}</p>
          )}
        </div>
      </div>

      {/* Purpose */}
      <div className="flex items-center gap-4">
        <label
          className="text-sm font-medium w-32 flex-shrink-0"
          style={{ color: "#7e7e7e" }}
        >
          Purpose *
        </label>
        <div className="flex-1">
          <Select
            name="purposeId"
            value={purposes.find(
              (option) => option.value === formData.purposeId
            )}
            onChange={handleSelectChange}
            options={purposes}
            styles={selectStyles}
            placeholder={
              purposesLoading ? "Loading purposes..." : "Select purpose"
            }
            isSearchable
            isLoading={purposesLoading}
            isDisabled={purposesLoading}
            className="react-select-container"
            classNamePrefix="react-select"
          />
          {errors.purposeId && (
            <p className="mt-1 text-sm text-red-600">{errors.purposeId}</p>
          )}
        </div>
      </div>

      {/* Loan Account No. */}
      <div className="flex items-center gap-4">
        <label
          className="text-sm font-medium w-32 flex-shrink-0"
          style={{ color: "#7e7e7e" }}
        >
          Loan Account No.
        </label>
        <div className="flex-1">
          <input
            type="text"
            name="loanAccountNo"
            value={formData.loanAccountNo}
            onChange={handleChange}
            className={`w-full px-4 py-3 border rounded-lg bg-white outline-none transition-colors duration-200 ${
              errors.loanAccountNo
                ? "border-red-500"
                : "border-gray-300 focus:border-green-500 hover:border-gray-400"
            }`}
            style={{ color: "#7e7e7e" }}
            placeholder="Enter 13-digit account number"
            maxLength="13"
          />
          <div className="flex justify-between items-center mt-1">
            {errors.loanAccountNo && (
              <p className="text-sm text-red-600">{errors.loanAccountNo}</p>
            )}
            <span className={`text-xs ml-auto ${
              formData.loanAccountNo?.length === 13 
                ? "text-green-600" 
                : formData.loanAccountNo?.length > 0 
                  ? "text-blue-600" 
                  : "text-gray-400"
            }`}>
              {formData.loanAccountNo?.length}/13 digits
            </span>
          </div>
        </div>
      </div>

      {/* Loan Balance */}
      <div className="flex items-center gap-4">
        <label
          className="text-sm font-medium w-32 flex-shrink-0"
          style={{ color: "#7e7e7e" }}
        >
          Loan Balance
        </label>
        <div className="flex-1">
          <input
            type="number"
            name="loanBalance"
            value={formData.loanBalance}
            onChange={handleChange}
            className={`w-full px-4 py-3 border rounded-lg bg-white outline-none transition-colors duration-200 ${
              errors.loanBalance
                ? "border-red-500"
                : "border-gray-300 focus:border-green-500 hover:border-gray-400"
            }`}
            style={{ color: "#7e7e7e" }}
            placeholder="e.g., 50000.00"
            min="0"
            step="0.01"
          />
          {errors.loanBalance && (
            <p className="mt-1 text-sm text-red-600">{errors.loanBalance}</p>
          )}
          
        </div>
      </div>

      {/* Arrears Days */}
      <div className="flex items-center gap-4">
        <label
          className="text-sm font-medium w-32 flex-shrink-0"
          style={{ color: "#7e7e7e" }}
        >
          Arrears Days
        </label>
        <div className="flex-1">
          <input
            type="number"
            name="arrearsDays"
            value={formData.arrearsDays}
            onChange={handleChange}
            className={`w-full px-4 py-3 border rounded-lg bg-white outline-none transition-colors duration-200 ${
              errors.arrearsDays
                ? "border-red-500"
                : "border-gray-300 focus:border-green-500 hover:border-gray-400"
            }`}
            style={{ color: "#7e7e7e" }}
            placeholder="e.g., 30"
            min="0"
            step="1"
          />
          {errors.arrearsDays && (
            <p className="mt-1 text-sm text-red-600">{errors.arrearsDays}</p>
          )}
          
        </div>
      </div>

     

   

      {/* Notes */}
      <div className="flex gap-4">
        <label
          className="text-sm font-medium w-32 flex-shrink-0 pt-3"
          style={{ color: "#7e7e7e" }}
        >
          Comments
        </label>
        <div className="flex-1">
          <textarea
            name="notes"
            value={formData.notes}
            onChange={handleChange}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-white outline-none transition-colors duration-200 focus:border-green-500 hover:border-gray-400 resize-none"
            style={{ color: "#7e7e7e" }}
            placeholder="Add your comments here ..."
            rows="4"
          />
        </div>
      </div>

      {/* Follow-up Date & Time */}
      <div className="flex items-center gap-4">
        <label
          className="text-sm font-medium w-32 flex-shrink-0"
          style={{ color: "#7e7e7e" }}
        >
          Follow up date
        </label>
        <div className="flex-1 flex gap-3">
          <input
            type="date"
            name="followUpDate"
            value={formData.followUpDate}
            onChange={handleChange}
            className="flex-1 px-4 py-3 border border-gray-300 rounded-lg bg-white outline-none transition-colors duration-200 focus:border-green-500 hover:border-gray-400"
            style={{ color: "#7e7e7e" }}
          />
          <input
            type="time"
            name="followUpTime"
            value={formData.followUpTime}
            onChange={handleChange}
            className="flex-1 px-4 py-3 border border-gray-300 rounded-lg bg-white outline-none transition-colors duration-200 focus:border-green-500 hover:border-gray-400"
            style={{ color: "#7e7e7e" }}
          />
        </div>
      </div>


      {/* Attachments */}
      <div className="flex gap-4">
        <label
          className="text-sm font-medium w-32 flex-shrink-0 pt-3"
          style={{ color: "#7e7e7e" }}
        >
          Attachments
        </label>
        <div className="flex-1">
          {/* File Upload Area */}
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-400 transition-colors">
            <input
              type="file"
              multiple
              onChange={handleFileUpload}
              className="hidden"
              id="file-upload"
              accept="*/*"
            />
            <label
              htmlFor="file-upload"
              className="cursor-pointer flex flex-col items-center gap-2"
            >
              <Upload size={24} style={{ color: "#7e7e7e" }} />
              <span className="text-sm" style={{ color: "#7e7e7e" }}>
                Click to upload files or drag and drop
              </span>
              <span className="text-xs" style={{ color: "#9ca3af" }}>
                Any file format supported
              </span>
            </label>
          </div>

          {/* Uploaded Files List */}
          {formData.attachments.length > 0 && (
            <div className="mt-3 space-y-2">
              {formData.attachments.map((attachment) => (
                <div
                  key={attachment.id}
                  className="flex items-center justify-between p-2 bg-gray-50 rounded-lg border"
                >
                  <div className="flex items-center gap-2">
                    <div className="text-sm" style={{ color: "#7e7e7e" }}>
                      <div className="font-medium">{attachment.name}</div>
                      <div className="text-xs" style={{ color: "#9ca3af" }}>
                        {(attachment.size / 1024).toFixed(1)} KB •{" "}
                        {attachment.type || "Unknown type"}
                      </div>
                    </div>
                  </div>
                  <button
                    type="button"
                    onClick={() => removeAttachment(attachment.id)}
                    className="p-1 text-red-500 hover:text-red-700 transition-colors"
                  >
                    <X size={16} />
                  </button>
                </div>
              ))}
            </div>
          )}
          </div>
        </div>
        </div>

        {/* Fixed Buttons at Bottom */}
        <div className="flex-shrink-0 border-t border-gray-200 dark:border-gray-600 pt-4 mt-4">
          <div className="flex justify-end gap-3">
            <button
          type="button"
          onClick={onClose}
          className="px-7 py-3 text-sm font-medium rounded-lg transition-colors"
          style={{
            color: "#f46b68",
            backgroundColor: "#fef7f7",
          }}
          onMouseEnter={(e) => {
            e.target.style.backgroundColor = "#f46b68";
            e.target.style.color = "white";
          }}
          onMouseLeave={(e) => {
            e.target.style.backgroundColor = "#fef7f7";
            e.target.style.color = "#f46b68";
          }}
        >
          Close
            </button>
            <button
          type="submit"
          disabled={isSubmitting}
          className="px-7 py-3 text-white text-sm font-medium rounded-lg disabled:opacity-50 transition-colors"
          style={{ backgroundColor: "#1c5b41" }}
          onMouseEnter={(e) => {
            if (!isSubmitting) {
              e.target.style.backgroundColor = "#2bc155";
            }
          }}
          onMouseLeave={(e) => {
            if (!isSubmitting) {
              e.target.style.backgroundColor = "#1c5b41";
            }
          }}
        >
          {isSubmitting ? "Saving..." : "Submit"}
            </button>
          </div>
        </div>
        </div>
      </form>
    </div>
  );
};

export default LoanCallForm;
