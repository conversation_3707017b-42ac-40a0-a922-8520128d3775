import { useState, useEffect } from "react";
import {
  Paperclip,
  Download,
  Eye,
  FileText,
  Image,
  FileVideo,
  FileAudio,
  File,
  Calendar,
  User,
  Loader2,
  ExternalLink,
  Phone,
  Footprints,
  Search,
  Filter,
  BarChart3
} from "lucide-react";
import { leadsService } from "../../../services/leadsService";
import { loanActivitiesService } from "../../../services/loanActivitiesService";

const AttachmentsTab = ({ onClose, item, entityId, entityType = "lead", title = "Attachments", emptyMessage = "No attachments found." }) => {
  // State for attachments data
  const [attachmentsData, setAttachmentsData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [viewingAttachment, setViewingAttachment] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState("all"); // all, call, visit
  const [summary, setSummary] = useState(null);
  const [dateRange, setDateRange] = useState(null);

  // Fetch attachments from API
  const fetchAttachments = async () => {
    const id = entityId || item?.id;
    if (!id) {
      setError(`No ${entityType} ID provided`);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      console.log(`Fetching attachments for ${entityType} ID: ${id}`);

      let response;
      if (entityType === "loan_client") {
        // For loan clients, get attachments from loan activities
        response = await loanActivitiesService.getAll(1, 100, { loan_client_id: id });
        // Extract attachments from all activities
        const allAttachments = [];
        if (response.data) {
          response.data.forEach(activity => {
            if (activity.attachments && activity.attachments.length > 0) {
              activity.attachments.forEach(attachment => {
                allAttachments.push({
                  ...attachment,
                  activity_id: activity.id,
                  activity_type: activity.interaction_type,
                  activity_date: activity.created_at,
                  officer_name: activity.rm_user?.name || "Unknown"
                });
              });
            }
          });
        }
        response = { data: allAttachments };
      } else {
        response = await leadsService.getAttachments(id);
      }
      console.log("Attachments API response:", response);

      // Extract data from the API response structure
      const { attachments = [], summary: apiSummary, date_range } = response;

      // Set summary and date range
      setSummary(apiSummary);
      setDateRange(date_range);

      // Transform API data to match UI format
      const transformedData = attachments.map((attachment, index) => {
        const fileName = extractFileNameFromUrl(attachment.file_url);
        const fileType = getFileType(fileName);

        return {
          id: attachment.id || `attachment_${index + 1}`,
          fileName: fileName,
          fileSize: 'Unknown', // Backend doesn't provide file size yet
          fileType: fileType,
          uploadedBy: 'System', // Backend doesn't provide uploader info yet
          uploadedDate: formatDate(attachment.created_at),
          interactionType: attachment.activity?.interaction_type || 'call',
          interactionDate: formatDate(attachment.activity?.created_at),
          activityType: attachment.activity?.activity_type || 'Unknown',
          description: attachment.activity?.notes || 'No description available',
          downloadUrl: attachment.file_url,
          previewUrl: isImageFile(fileName) ? attachment.file_url : null,
          rawData: attachment,
        };
      });

      // Sort by creation date (newest first)
      const sortedData = transformedData.sort((a, b) =>
        new Date(b.rawData.created_at) - new Date(a.rawData.created_at)
      );

      setAttachmentsData(sortedData);
    } catch (error) {
      console.error("Error fetching attachments:", error);
      setError(
        error.response?.data?.message || "Failed to load attachments"
      );
    } finally {
      setLoading(false);
    }
  };

  // Extract filename from URL
  const extractFileNameFromUrl = (url) => {
    if (!url) return 'unknown_file';
    try {
      const urlParts = url.split('/');
      return urlParts[urlParts.length - 1] || 'unknown_file';
    } catch (error) {
      return 'unknown_file';
    }
  };

  // Check if file is an image
  const isImageFile = (fileName) => {
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'];
    const extension = fileName.split('.').pop()?.toLowerCase();
    return imageExtensions.includes(extension);
  };

  // Filter and search attachments
  const getFilteredAttachments = () => {
    let filtered = attachmentsData;

    // Filter by interaction type
    if (filterType !== "all") {
      filtered = filtered.filter(attachment =>
        attachment.interactionType === filterType
      );
    }

    // Search by filename or description
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(attachment =>
        attachment.fileName.toLowerCase().includes(searchLower) ||
        attachment.description.toLowerCase().includes(searchLower) ||
        attachment.activityType.toLowerCase().includes(searchLower)
      );
    }

    return filtered;
  };

  const filteredAttachments = getFilteredAttachments();

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Get file type from filename
  const getFileType = (fileName) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg'].includes(extension)) return 'image';
    if (['mp4', 'avi', 'mov', 'wmv', 'flv'].includes(extension)) return 'video';
    if (['mp3', 'wav', 'flac', 'aac'].includes(extension)) return 'audio';
    if (['pdf'].includes(extension)) return 'pdf';
    if (['doc', 'docx', 'txt', 'rtf'].includes(extension)) return 'document';
    return 'file';
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return "Not set";

    const date = new Date(dateString);
    const options = {
      day: "2-digit",
      month: "short",
      year: "numeric",
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    };

    return date.toLocaleDateString("en-GB", options);
  };

  // Get file icon based on type
  const getFileIcon = (fileType) => {
    switch (fileType) {
      case 'image': return Image;
      case 'video': return FileVideo;
      case 'audio': return FileAudio;
      case 'pdf': return FileText;
      case 'document': return FileText;
      default: return File;
    }
  };

  // Get file type color
  const getFileTypeColor = (fileType) => {
    switch (fileType) {
      case 'image': return '#10b981'; // green-500
      case 'video': return '#8b5cf6'; // violet-500
      case 'audio': return '#f59e0b'; // amber-500
      case 'pdf': return '#ef4444'; // red-500
      case 'document': return '#3b82f6'; // blue-500
      default: return '#6b7280'; // gray-500
    }
  };

  // Get interaction type color
  const getInteractionTypeColor = (type) => {
    return type === 'call' ? '#f97316' : '#8b5cf6'; // orange for call, violet for visit
  };

  // Handle file preview
  const handlePreview = (attachment) => {
    if (attachment.previewUrl || attachment.fileType === 'image') {
      setViewingAttachment(attachment);
    } else {
      // For non-previewable files, trigger download
      handleDownload(attachment);
    }
  };

  // Handle file download
  const handleDownload = (attachment) => {
    console.log('Downloading attachment:', attachment.fileName);
    // In a real implementation, this would trigger the actual download
    window.open(attachment.downloadUrl, '_blank');
  };

  // Fetch data on component mount
  useEffect(() => {
    fetchAttachments();
  }, [item?.id, entityId, entityType]);

  return (
    <div className="px-6 -mx-6">
      <div className="pl-[7%] pr-[10%] py-6">
        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="w-8 h-8 animate-spin text-gray-400" />
            <span className="ml-2 text-gray-600">Loading attachments...</span>
          </div>
        )}

        {/* Error State */}
        {error && !loading && (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="text-red-500 mb-2">⚠️ Error</div>
              <div className="text-gray-600">{error}</div>
              <button
                onClick={fetchAttachments}
                className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
              >
                Retry
              </button>
            </div>
          </div>
        )}

        {/* Summary Statistics */}
        {!loading && !error && summary && (
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-2 mb-3">
              <BarChart3 size={20} className="text-gray-600" />
              <h3 className="text-sm font-medium text-gray-900">Summary</h3>
            </div>
            <div className="grid grid-cols-3 gap-4 text-sm">
              <div className="text-center">
                <div className="text-lg font-semibold text-blue-600">
                  {attachmentsData.length}
                </div>
                <div className="text-gray-600">Total Attachments</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-orange-600">
                  {summary.call_activities || 0}
                </div>
                <div className="text-gray-600">Call Activities</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-purple-600">
                  {summary.visit_activities || 0}
                </div>
                <div className="text-gray-600">Visit Activities</div>
              </div>
            </div>
            {dateRange && (
              <div className="mt-3 pt-3 border-t border-gray-200 text-xs text-gray-500">
                Date Range: {formatDate(dateRange.earliest)} - {formatDate(dateRange.latest)}
              </div>
            )}
          </div>
        )}

        {/* Search and Filter */}
        {!loading && !error && attachmentsData.length > 0 && (
          <div className="mb-6 space-y-3">
            {/* Search */}
            <div className="relative">
              <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search attachments..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
              />
            </div>

            {/* Filter */}
            <div className="flex items-center gap-2">
              <Filter size={16} className="text-gray-400" />
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
              >
                <option value="all">All Activities</option>
                <option value="call">Call Activities</option>
                <option value="visit">Visit Activities</option>
              </select>
              {(searchTerm || filterType !== "all") && (
                <button
                  onClick={() => {
                    setSearchTerm("");
                    setFilterType("all");
                  }}
                  className="px-3 py-1 text-xs text-gray-600 bg-gray-100 rounded hover:bg-gray-200 transition-colors"
                >
                  Clear
                </button>
              )}
            </div>
          </div>
        )}

        {/* Empty State */}
        {!loading && !error && attachmentsData.length === 0 && (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="text-gray-400 mb-2">📎</div>
              <div className="text-gray-600">{emptyMessage}</div>
              <div className="text-sm text-gray-500 mt-1">
                {entityType === "loan_client"
                  ? "Attachments from loan activities will appear here"
                  : "Attachments from calls and visits will appear here"
                }
              </div>
            </div>
          </div>
        )}

        {/* No Results State */}
        {!loading && !error && attachmentsData.length > 0 && filteredAttachments.length === 0 && (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="text-gray-400 mb-2">🔍</div>
              <div className="text-gray-600">No attachments match your search</div>
              <div className="text-sm text-gray-500 mt-1">
                Try adjusting your search terms or filters
              </div>
            </div>
          </div>
        )}

        {/* Attachments Grid */}
        {!loading && !error && filteredAttachments.length > 0 && (
          <div className="space-y-4">
            {filteredAttachments.map((attachment) => {
              const FileIcon = getFileIcon(attachment.fileType);
              
              return (
                <div
                  key={attachment.id}
                  className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start gap-4">
                    {/* File Icon */}
                    <div
                      className="flex-shrink-0 p-3 rounded-lg"
                      style={{ backgroundColor: `${getFileTypeColor(attachment.fileType)}20` }}
                    >
                      <FileIcon
                        size={24}
                        style={{ color: getFileTypeColor(attachment.fileType) }}
                      />
                    </div>

                    {/* File Details */}
                    <div className="flex-1 min-w-0">
                      {/* File Name and Size */}
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="text-sm font-medium text-gray-900 truncate">
                          {attachment.fileName}
                        </h4>
                        <span className="text-xs text-gray-500 ml-2">
                          {attachment.fileSize}
                        </span>
                      </div>

                      {/* Activity Type Badge */}
                      <div className="mb-2">
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {attachment.activityType}
                        </span>
                      </div>

                      {/* Description */}
                      <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                        {attachment.description}
                      </p>

                      {/* Metadata */}
                      <div className="space-y-1 text-xs text-gray-500 mb-3">
                        <div className="flex items-center gap-1">
                          <Calendar size={12} />
                          <span>Uploaded: {attachment.uploadedDate}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          {attachment.interactionType === 'call' ? (
                            <Phone size={12} />
                          ) : (
                            <Footprints size={12} />
                          )}
                          <span className="capitalize">{attachment.interactionType}</span>
                          <span>on {attachment.interactionDate}</span>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => handlePreview(attachment)}
                          className="flex items-center gap-1 px-3 py-1.5 text-xs font-medium text-blue-600 bg-blue-50 rounded hover:bg-blue-100 transition-colors"
                        >
                          <Eye size={12} />
                          {attachment.previewUrl || attachment.fileType === 'image' ? 'Preview' : 'View'}
                        </button>
                        <button
                          onClick={() => handleDownload(attachment)}
                          className="flex items-center gap-1 px-3 py-1.5 text-xs font-medium text-green-600 bg-green-50 rounded hover:bg-green-100 transition-colors"
                        >
                          <Download size={12} />
                          Download
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Preview Modal */}
      {viewingAttachment && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg max-w-4xl max-h-[90vh] overflow-hidden">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-4 border-b">
              <h3 className="text-lg font-medium">{viewingAttachment.fileName}</h3>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => handleDownload(viewingAttachment)}
                  className="p-2 text-gray-500 hover:text-gray-700 rounded"
                >
                  <Download size={20} />
                </button>
                <button
                  onClick={() => setViewingAttachment(null)}
                  className="p-2 text-gray-500 hover:text-gray-700 rounded"
                >
                  ×
                </button>
              </div>
            </div>

            {/* Modal Content */}
            <div className="p-4 max-h-[70vh] overflow-auto">
              {viewingAttachment.previewUrl ? (
                <img
                  src={viewingAttachment.previewUrl}
                  alt={viewingAttachment.fileName}
                  className="max-w-full h-auto rounded"
                />
              ) : (
                <div className="text-center py-12">
                  <FileIcon size={48} className="mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-600">Preview not available</p>
                  <button
                    onClick={() => handleDownload(viewingAttachment)}
                    className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                  >
                    Download to View
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Close Button */}
      <div className="flex justify-end pt-8 pb-6 pr-[10%]">
        <button
          onClick={onClose}
          className="px-7 py-3 text-sm font-medium rounded-lg transition-colors"
          style={{
            color: "#f46b68",
            backgroundColor: "#fef7f7",
          }}
          onMouseEnter={(e) => {
            e.target.style.backgroundColor = "#f46b68";
            e.target.style.color = "white";
          }}
          onMouseLeave={(e) => {
            e.target.style.backgroundColor = "#fef7f7";
            e.target.style.color = "#f46b68";
          }}
        >
          Close
        </button>
      </div>
    </div>
  );
};

export default AttachmentsTab;
