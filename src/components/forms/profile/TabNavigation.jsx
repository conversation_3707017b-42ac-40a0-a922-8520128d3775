import { User, History, Paperclip } from "lucide-react";

const TabNavigation = ({ activeTab, setActiveTab }) => {
  return (
    <div className="flex border-b border-gray-200 -mx-6 px-6 pl-[10%]">
      <button
        onClick={() => setActiveTab("profile")}
        className={`flex items-center gap-2 px-0 py-3 mr-8 font-medium border-b-2 transition-colors ${
          activeTab === "profile"
            ? "border-[#1c5b41] text-[#1c5b41] hover:text-[#0f3d2a]"
            : "border-transparent text-gray-400 hover:text-gray-700"
        }`}
      >
        <User size={20} />
        Profile
      </button>
      <button
        onClick={() => setActiveTab("history")}
        className={`flex items-center gap-2 px-0 py-3 mr-8 font-medium border-b-2 transition-colors ${
          activeTab === "history"
            ? "border-[#1c5b41] text-[#1c5b41] hover:text-[#0f3d2a]"
            : "border-transparent text-gray-400 hover:text-gray-700"
        }`}
      >
        <History size={20} />
        History
      </button>
      <button
        onClick={() => setActiveTab("attachments")}
        className={`flex items-center gap-2 px-0 py-3 font-medium border-b-2 transition-colors ${
          activeTab === "attachments"
            ? "border-[#1c5b41] text-[#1c5b41] hover:text-[#0f3d2a]"
            : "border-transparent text-gray-400 hover:text-gray-700"
        }`}
      >
        <Paperclip size={20} />
        Attachments
      </button>
    </div>
  );
};

export default TabNavigation;
