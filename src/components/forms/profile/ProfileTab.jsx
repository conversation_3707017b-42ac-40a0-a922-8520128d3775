import { Minus } from "lucide-react";

const ProfileTab = ({ item, leadDetails, onClose, formatValue, data, fields, formatCurrency }) => {
  // Use data if provided (for loan clients), otherwise use leadDetails or item (for leads)
  const profileData = data || leadDetails || item;

  // Get first contact person if available
  const firstContactPerson =
    profileData.contact_persons && profileData.contact_persons.length > 0
      ? profileData.contact_persons[0]
      : null;

  // Helper function to get nested property value
  const getNestedValue = (obj, path) => {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  };

  // Helper function to format field value
  const formatFieldValue = (value, field) => {
    if (!value && value !== 0) return <Minus size={16} className="text-gray-400" />;

    if (field.format === "currency" && formatCurrency) {
      return formatCurrency(value);
    } else if (field.format === "date") {
      return new Date(value).toLocaleDateString();
    } else if (field.suffix) {
      return `${value}${field.suffix}`;
    }

    return value;
  };

  // If fields structure is provided (for loan clients), use it
  if (fields && Array.isArray(fields)) {
    return (
      <div className="px-6 -mx-6">
        <div className="space-y-6 pl-[7%] pr-[10%] py-6">
          {fields.map((section, sectionIndex) => (
            <div key={sectionIndex} className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                {section.section}
              </h3>
              <div className="space-y-3">
                {section.fields.map((field, fieldIndex) => {
                  const value = getNestedValue(profileData, field.key) ||
                               (field.fallback ? getNestedValue(profileData, field.fallback) : null);

                  return (
                    <div key={fieldIndex} className="flex gap-[26%] items-center py-2">
                      <span
                        className="font-medium w-[200px] whitespace-nowrap"
                        style={{ color: "#7e7e7e" }}
                      >
                        {field.label}
                      </span>
                      <span style={{ color: "#7e7e7e" }}>
                        {formatFieldValue(value, field)}
                      </span>
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Original leads format (fallback)
  return (
    <div className="px-6 -mx-6">
      {/* HIT Section */}
      <div className="mb-0 pl-[7%]">
        <div className="flex gap-[26%] items-center py-4">
          <span className="text-lg font-semibold w-[50px]">HIT</span>
          <span className="text-lg font-semibold">
            {profileData.customer_name || profileData.customerName || profileData.lead_name || profileData.name || (
              <Minus size={16} className="text-gray-400" />
            )}
          </span>
        </div>
      </div>

      {/* Profile Details - Single Column Layout */}
      <div className="space-y-0 pl-[7%]">
        {/* Lead Type */}
        <div className="flex gap-[26%] items-center py-4 border-b border-gray-100">
          <span
            className="font-semibold w-[50px] whitespace-nowrap"
            style={{ color: "#7e7e7e" }}
          >
            Lead Type
          </span>
          <span style={{ color: "#7e7e7e" }}>
            {formatValue(profileData.type_of_lead || profileData.typeOfLead)}
          </span>
        </div>

        {/* Mobile */}
        <div className="flex gap-[26%] items-center py-4 border-b border-gray-100">
          <span
            className="font-semibold w-[50px] whitespace-nowrap"
            style={{ color: "#7e7e7e" }}
          >
            Mobile
          </span>
          <span style={{ color: "#7e7e7e" }}>
            {formatValue(profileData.phone_number || profileData.phoneNumber)}
          </span>
        </div>

        {/* Client ID */}
        <div className="flex gap-[26%] items-center py-4 border-b border-gray-100">
          <span
            className="font-semibold w-[50px] whitespace-nowrap"
            style={{ color: "#7e7e7e" }}
          >
            Client ID
          </span>
          <span style={{ color: "#7e7e7e" }}>
            {formatValue(profileData.client_id)}
          </span>
        </div>

        {/* Customer Category */}
        <div className="flex gap-[26%] items-center py-4 border-b border-gray-100">
          <span
            className="font-semibold w-[50px] whitespace-nowrap"
            style={{ color: "#7e7e7e" }}
          >
            Category
          </span>
          <span style={{ color: "#7e7e7e" }}>
            {formatValue(profileData.customer_category?.name || profileData.customerCategory?.name)}
          </span>
        </div>

        {/* Branch */}
        <div className="flex gap-[26%] items-center py-4 border-b border-gray-100">
          <span
            className="font-semibold w-[50px] whitespace-nowrap"
            style={{ color: "#7e7e7e" }}
          >
            Branch
          </span>
          <span style={{ color: "#7e7e7e" }}>
            {formatValue(profileData.branch?.name)}
          </span>
        </div>

        {/* Added by */}
        <div className="flex gap-[26%] items-center py-4 border-b border-gray-100">
          <span
            className="font-semibold w-[50px] whitespace-nowrap"
            style={{ color: "#7e7e7e" }}
          >
            Added by
          </span>
          <span style={{ color: "#7e7e7e" }}>
            {formatValue(profileData.rm_user?.name || profileData.rmUser?.name)}
          </span>
        </div>

        {/* Sector */}
        <div className="flex gap-[26%] items-center py-4 border-b border-gray-100">
          <span
            className="font-semibold w-[50px] whitespace-nowrap"
            style={{ color: "#7e7e7e" }}
          >
            Sector
          </span>
          <span style={{ color: "#7e7e7e" }}>
            {formatValue(profileData.isic_sector?.name || profileData.isicSector?.name)}
          </span>
        </div>

        {/* Region */}
        <div className="flex gap-[26%] items-center py-4 border-b border-gray-100">
          <span
            className="font-semibold w-[50px] whitespace-nowrap"
            style={{ color: "#7e7e7e" }}
          >
            Region
          </span>
          <span style={{ color: "#7e7e7e" }}>
            {formatValue(profileData.branch?.region?.name)}
          </span>
        </div>

        {/* Contact Person */}
        <div className="flex gap-[26%] items-center py-4 border-b border-gray-100">
          <span
            className="font-semibold w-[50px] whitespace-nowrap"
            style={{ color: "#7e7e7e" }}
          >
            Contact Person
          </span>
          <span style={{ color: "#7e7e7e" }}>
            {formatValue(firstContactPerson?.name)}
          </span>
        </div>

        {/* Contact Phone */}
        <div className="flex gap-[26%] items-center py-4">
          <span
            className="font-semibold w-[50px] whitespace-nowrap"
            style={{ color: "#7e7e7e" }}
          >
            Contact Phone
          </span>
          <span style={{ color: "#7e7e7e" }}>
            {formatValue(firstContactPerson?.phone_number)}
          </span>
        </div>
      </div>

      {/* Close Button */}
      <div className="flex justify-end pt-8 pb-6 pr-[10%]">
        <button
          onClick={onClose}
          className="px-7 py-3 text-sm font-medium rounded-lg transition-colors"
          style={{
            color: "#f46b68",
            backgroundColor: "#fef7f7",
          }}
          onMouseEnter={(e) => {
            e.target.style.backgroundColor = "#f46b68";
            e.target.style.color = "white";
          }}
          onMouseLeave={(e) => {
            e.target.style.backgroundColor = "#fef7f7";
            e.target.style.color = "#f46b68";
          }}
        >
          Close
        </button>
      </div>
    </div>
  );
};

export default ProfileTab;
