import { useState, useEffect } from "react";
import { Minus } from "lucide-react";
import {
  ProfileHeader,
  TabNavigation,
  ProfileTab,
  HistoryTab,
  AttachmentsTab,
} from "./profile";
import { loanService } from "../../services/loanService";

const LoanClientProfile = ({ item, onClose }) => {
  const [activeTab, setActiveTab] = useState("profile");
  const [loanClientDetails, setLoanClientDetails] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch detailed loan client data when component mounts
  useEffect(() => {
    const fetchLoanClientDetails = async () => {
      if (!item?.id) {
        setError("No loan client ID provided");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        console.log("Fetching loan client details for profile:", item.id);
        const details = await loanService.getById(item.id);
        console.log("Loan client details fetched for profile:", details);
        setLoanClientDetails(details);
        setError(null);
      } catch (error) {
        console.error("Error fetching loan client details for profile:", error);
        setError("Failed to load loan client details");
      } finally {
        setLoading(false);
      }
    };

    fetchLoanClientDetails();
  }, [item?.id]);

  if (!item) return null;

  if (loading) {
    return (
      <div className="flex items-center justify-center h-[80vh]">
        <div className="flex items-center space-x-2">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="text-gray-500">Loading profile...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-[80vh]">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            Close
          </button>
        </div>
      </div>
    );
  }

  // Helper function to get status color for loan clients
  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case "active":
        return "#22c55e"; // green
      case "inactive":
        return "#ef4444"; // red
      case "pending":
        return "#f59e0b"; // amber
      case "suspended":
        return "#6b7280"; // gray
      default:
        return "#7e7e7e";
    }
  };

  // Helper function to format field values
  const formatValue = (value) => {
    if (!value || value === "")
      return <Minus size={16} className="text-gray-400" />;
    return value;
  };

  // Helper function to format currency
  const formatCurrency = (amount) => {
    if (!amount) return <Minus size={16} className="text-gray-400" />;
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  return (
    <div className="flex flex-col h-[80vh]">
      {/* Fixed Header */}
      <div className="flex-shrink-0">
        <ProfileHeader
          title={loanClientDetails?.customerName || item.customerName || item.name || "Loan Client"}
          subtitle={`Account: ${loanClientDetails?.accountNumber || item.accountNumber || 'N/A'}`}
          status={loanClientDetails?.leadStatus || item.leadStatus || "Active"}
          statusColor={getStatusColor(loanClientDetails?.leadStatus || item.leadStatus)}
          onClose={onClose}
        />
        <TabNavigation
          activeTab={activeTab}
          onTabChange={setActiveTab}
          tabs={[
            { id: "profile", label: "Profile" },
            { id: "history", label: "History" },
            { id: "attachments", label: "Attachments" },
          ]}
        />
      </div>

      {/* Scrollable Content */}
      <div className="flex-1 overflow-y-auto">
        {activeTab === "profile" && (
          <ProfileTab
            data={loanClientDetails}
            formatValue={formatValue}
            formatCurrency={formatCurrency}
            fields={[
              {
                section: "Basic Information",
                fields: [
                  { label: "Customer Name", key: "customerName" },
                  { label: "Phone Number", key: "phoneNumber" },
                  { label: "Email", key: "email" },
                  { label: "Account Number", key: "accountNumber" },
                  { label: "Lead Status", key: "leadStatus" },
                  { label: "Type of Lead", key: "typeOfLead" },
                ]
              },
              {
                section: "Financial Information",
                fields: [
                  { label: "Loan Balance", key: "loan_balance", format: "currency" },
                  { label: "Credit Limit", key: "credit_limit", format: "currency" },
                  { label: "Interest Rate", key: "interest_rate", suffix: "%" },
                  { label: "Arrears Days", key: "arrears_days" },
                  { label: "Last Payment Date", key: "last_payment_date", format: "date" },
                ]
              },
              {
                section: "Business Information",
                fields: [
                  { label: "Customer Category", key: "customerCategory.name" },
                  { label: "ISIC Sector", key: "isicSector.name" },
                  { label: "ISIC Code", key: "isicSector.code" },
                  { label: "Branch", key: "branch.name" },
                ]
              },
              {
                section: "Contact Information",
                fields: [
                  { label: "Contact Person", key: "contactPersonName" },
                  { label: "Contact Phone", key: "contactPersonPhone" },
                  { label: "Address", key: "address" },
                ]
              },
              {
                section: "Relationship Information",
                fields: [
                  { label: "Anchor", key: "anchor.name" },
                  { label: "Anchor Email", key: "anchor.email" },
                  { label: "Anchor Phone", key: "anchor.phoneNumber" },
                  { label: "Anchor Relationship", key: "anchorRelationship.name" },
                ]
              },
              {
                section: "System Information",
                fields: [
                  { label: "Created Date", key: "createdAt", format: "date" },
                  { label: "Updated Date", key: "updatedAt", format: "date" },
                  { label: "Total Activities", key: "totalLoanActivities" },
                  { label: "Last Activity Date", key: "lastLoanActivityDate", format: "date" },
                  { label: "RM User", key: "rmUser.name" },
                  { label: "RM Code", key: "rmUser.rmCode" },
                ]
              }
            ]}
          />
        )}

        {activeTab === "history" && (
          <HistoryTab
            entityId={loanClientDetails?.id}
            entityType="loan_client"
            title="Loan Activity History"
            emptyMessage="No loan activities found for this client."
          />
        )}

        {activeTab === "attachments" && (
          <AttachmentsTab
            entityId={loanClientDetails?.id}
            entityType="loan_client"
            title="Loan Client Attachments"
            emptyMessage="No attachments found for this loan client."
          />
        )}
      </div>
    </div>
  );
};

export default LoanClientProfile;
