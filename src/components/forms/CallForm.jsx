import { useState, useEffect } from "react";
import {
  Phone,
  PhoneMissed,
  Mic,
  MicOff,
  Pause,
  Play,
  Upload,
  X,
} from "lucide-react";
import Select from "react-select";
import {
  purposesService,
  formatPurposesForTable,
} from "../../services/purposesService";
import { customerServiceService } from "../../services/customerServiceService";
import { leadsService } from "../../services/leadsService";
import LeadStatusChanger from "../common/LeadStatusChanger";
import { toast } from "react-toastify";
import instance from "../../axios/instance";

const CallForm = ({ item, onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    callType: "",
    callStatus: "",
    purposeId: "",
    customerFeedbackCategoryId: "",
    notes: "",
    followUpDate: "",
    followUpTime: "",
    attachments: [],
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Purposes state
  const [purposes, setPurposes] = useState([]);
  const [purposesLoading, setPurposesLoading] = useState(false);

  // Customer feedback categories state
  const [feedbackCategories, setFeedbackCategories] = useState([]);
  const [feedbackCategoriesLoading, setFeedbackCategoriesLoading] = useState(false);

  // Call functionality states
  const [isCallActive, setIsCallActive] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [callTimer, setCallTimer] = useState(0);
  const [isMuted, setIsMuted] = useState(false);
  const [isOnHold, setIsOnHold] = useState(false);

  // Fetch purposes on component mount
  const fetchPurposes = async () => {
    try {
      setPurposesLoading(true);
      const response = await purposesService.getAll();
      const formattedPurposes = formatPurposesForTable(response);

      // Convert to React Select format
      const purposeOptions = formattedPurposes.map((purpose) => ({
        value: purpose.id,
        label: purpose.name,
        purpose: purpose,
      }));

      setPurposes(purposeOptions);
    } catch (error) {
      console.error("Error fetching purposes:", error);
      setPurposes([]);
    } finally {
      setPurposesLoading(false);
    }
  };

  // Fetch customer feedback categories
  const fetchFeedbackCategories = async () => {
    try {
      setFeedbackCategoriesLoading(true);
      const response = await customerServiceService.getCustomerFeedbackCategories();

      // Convert to React Select format
      const categoryOptions = response.map((category) => ({
        value: category.id,
        label: category.name,
      }));

      setFeedbackCategories(categoryOptions);
    } catch (error) {
      console.error("Error fetching feedback categories:", error);
      setFeedbackCategories([]);
    } finally {
      setFeedbackCategoriesLoading(false);
    }
  };

  useEffect(() => {
    fetchPurposes();
    fetchFeedbackCategories();
  }, []);

  useEffect(() => {
    console.log("=== CALLFORM USEEFFECT TRIGGERED ===");
    console.log("CallForm received item:", item);
    console.log("item?.id:", item?.id);
    console.log("item?.leadId:", item?.leadId);
    console.log("item?.name:", item?.name);
    console.log("====================================");

    if (item) {
      setFormData({
        callType: item.callType || "",
        callStatus: item.callStatus || "",
        purposeId: item.purposeId || "",
        customerFeedbackCategoryId: item.customerFeedbackCategoryId || "",
        notes: item.notes || "",
        followUpDate: item.followUpDate || "",
        followUpTime: item.followUpTime || "",
        attachments: item.attachments || [],
      });
    }
  }, [item]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  // Handle React Select changes
  const handleSelectChange = (selectedOption, actionMeta) => {
    const { name } = actionMeta;
    const value = selectedOption ? selectedOption.value : "";

    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when user makes selection
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  // Handle file upload
  const handleFileUpload = (e) => {
    const files = Array.from(e.target.files);
    const newAttachments = files.map((file) => ({
      id: Date.now() + Math.random(), // Temporary ID
      file: file,
      name: file.name,
      size: file.size,
      type: file.type,
    }));

    setFormData((prev) => ({
      ...prev,
      attachments: [...prev.attachments, ...newAttachments],
    }));

    // Clear the input
    e.target.value = "";
  };

  // Remove attachment
  const removeAttachment = (attachmentId) => {
    setFormData((prev) => ({
      ...prev,
      attachments: prev.attachments.filter((att) => att.id !== attachmentId),
    }));
  };

  // Handle lead status change
  const handleLeadStatusChange = async (leadId, newStatus) => {
    try {
      console.log(`Updating lead ${leadId} status to ${newStatus}`);
      await leadsService.updateStatus(leadId, newStatus);

      // Update the item status locally if needed
      if (item && item.id === leadId) {
        item.status = newStatus;
      }

      // Force re-render by updating a state variable
      setFormData(prev => ({...prev}));

      toast.success(`Lead status updated to ${newStatus}`);
      console.log(`Lead status updated successfully to ${newStatus}`);
    } catch (error) {
      console.error("Error updating lead status:", error);
      toast.error(error.message || "Failed to update lead status");
      throw error; // Re-throw to let the component handle the error display
    }
  };

  const validateForm = () => {
    const newErrors = {};
    if (!formData.callType) newErrors.callType = "Call type is required";
    if (!formData.callStatus) newErrors.callStatus = "Call status is required";
    if (!formData.purposeId) newErrors.purposeId = "Purpose is required";
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Call functionality handlers
  const handleStartCall = async () => {
    setIsConnecting(true);

    try {
      // Simulate API call with 2 second delay
      await new Promise((resolve) => setTimeout(resolve, 2000));

      setIsCallActive(true);
      setCallTimer(0);
      setIsMuted(false);
      setIsOnHold(false);
    } catch (error) {
      console.error("Failed to initiate call:", error);
    } finally {
      setIsConnecting(false);
    }
  };

  const handleEndCall = async () => {
    try {
      setIsCallActive(false);
      setIsConnecting(false);
      setCallTimer(0);
      setIsMuted(false);
      setIsOnHold(false);
    } catch (error) {
      console.error("Failed to end call:", error);
    }
  };

  const handleToggleMute = () => {
    setIsMuted(!isMuted);
  };

  const handleToggleHold = () => {
    setIsOnHold(!isOnHold);
  };

  // Timer effect for call duration
  useEffect(() => {
    let interval;
    if (isCallActive && !isOnHold) {
      interval = setInterval(() => {
        setCallTimer((prev) => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isCallActive, isOnHold]);

  // Format timer display
  const formatTimer = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      // Combine date and time into a single datetime
      let followUpDateTime = null;
      if (formData.followUpDate && formData.followUpTime) {
        // Create datetime string in ISO format
        const dateTimeString = `${formData.followUpDate}T${formData.followUpTime}:00.000Z`;
        followUpDateTime = dateTimeString;
      } else if (formData.followUpDate) {
        // If only date is provided, set time to current time
        const currentTime = new Date().toTimeString().slice(0, 8);
        const dateTimeString = `${formData.followUpDate}T${currentTime}.000Z`;
        followUpDateTime = dateTimeString;
      }

      // Create the data structure expected by backend
      console.log("=== CREATING API DATA ===");
      console.log("item at submission time:", item);
      console.log("item?.id:", item?.id);
      console.log("========================");

      const apiData = {
        call_type: formData.callType,
        call_status: formData.callStatus,
        purpose_id: formData.purposeId,
        customer_feedback_category_id: formData.customerFeedbackCategoryId,
        notes: formData.notes,
        leadID: item?.id || item?.leadId || "test-lead-id", // Include the lead ID with fallbacks
        ...(followUpDateTime && { follow_up_date: followUpDateTime }),
        attachments: [], // Always include attachments array, even if empty
      };

      console.log("apiData.leadID:", apiData.leadID);

      // Create FormData for file uploads
      const formDataToSend = new FormData();

      // Add each field individually to FormData
      formDataToSend.append("call_type", formData.callType);
      formDataToSend.append("call_status", formData.callStatus);
      formDataToSend.append("purpose_id", formData.purposeId);
      if (formData.customerFeedbackCategoryId) {
        formDataToSend.append("customer_feedback_category_id", formData.customerFeedbackCategoryId);
      }
      formDataToSend.append("notes", formData.notes);
      formDataToSend.append("leadID", item?.id || "");

      // Add follow-up date if provided
      if (followUpDateTime) {
        formDataToSend.append("follow_up_date", followUpDateTime);
      }

      // Add actual files to FormData
      formData.attachments.forEach((attachment, index) => {
        formDataToSend.append(`attachments`, attachment.file);
      });

      // For logging purposes - show what the backend will receive
      const apiDataForLogging = {
        ...apiData,
        attachments_files: formData.attachments.map((att) => ({
          name: att.name,
          size: att.size,
          type: att.type,
          file: `[File Object: ${att.name}]`,
        })),
      };

      // Console log the prepared data
      console.log("=== CALL FORM SUBMISSION DATA ===");
      console.log("Backend expects individual FormData fields (not JSON)");
      console.log("FormData being sent:");
      console.log("- call_type:", formData.callType);
      console.log("- call_status:", formData.callStatus);
      console.log("- purpose_id:", formData.purposeId);
      console.log("- notes:", formData.notes);
      console.log("- leadID:", item?.id);
      console.log("- follow_up_date:", followUpDateTime || "not provided");
      console.log("- attachment files:", formData.attachments.length, "files");

      // Log FormData contents (for debugging)
      console.log("FormData contents:");
      for (let [key, value] of formDataToSend.entries()) {
        if (value instanceof File) {
          console.log(
            `${key}: [File] ${value.name} (${value.size} bytes, ${value.type})`
          );
        } else {
          console.log(`${key}: ${value}`);
        }
      }
      console.log("=====================================");

      // Make API call to /activities/call-activities
      try {
        console.log("Sending data to /activities/call-activities endpoint...");
        const response = await instance.post(
          "/activities/call-activities",
          formDataToSend,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );

        console.log("API Response:", response.data);
        console.log("Call activity created successfully!");

        // Call the onSubmit callback with the response data
        onSubmit?.(response.data, item);
        onClose();
      } catch (apiError) {
        console.error("API Error:", apiError);
        console.error("Error response:", apiError.response?.data);

        // Still call onSubmit for now, but with error info
        onSubmit?.(formDataToSend, item, apiError);

        // You might want to show an error message to the user here
        // For now, we'll still close the modal
        onClose();
      }
    } catch (err) {
      console.error("Failed to submit call dialog form:", err);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Custom styles for React Select
  const selectStyles = {
    control: (provided, state) => ({
      ...provided,
      minHeight: "48px",
      borderColor: state.isFocused
        ? "#10b981"
        : errors[state.selectProps.name]
        ? "#ef4444"
        : "#d1d5db",
      boxShadow: "none",
      "&:hover": {
        borderColor: state.isFocused ? "#10b981" : "#9ca3af",
      },
    }),
    option: (provided, state) => ({
      ...provided,
      backgroundColor: state.isSelected
        ? "#10b981"
        : state.isFocused
        ? "#f3f4f6"
        : "white",
      color: state.isSelected ? "white" : "#374151",
    }),
    menu: (provided) => ({
      ...provided,
      zIndex: 9999,
    }),
  };

  return (
    <div className="flex flex-col h-[80vh]">
      <form onSubmit={handleSubmit} className="flex flex-col h-full">
        {/* Scrollable Content */}
        <div className="flex-1 overflow-y-auto overflow-x-hidden scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 px-1">
          <div className="flex flex-col gap-6 pb-4">
      {/* Call Section - Always show at the top */}
      <div className="border-b border-gray-200 dark:border-gray-600 min-h-[100px] flex items-center">
        {!isCallActive ? (
          // Call Button - Aligned to left
          <div className="flex justify-start w-full">
            <button
              type="button"
              onClick={handleStartCall}
              disabled={isConnecting}
              className="inline-flex items-center px-6 py-3 bg-green-500 hover:bg-green-600 disabled:bg-green-400 text-white font-medium rounded-lg transition-colors duration-200 shadow-sm disabled:cursor-not-allowed"
            >
              <Phone size={20} className="mr-2" />
              {isConnecting ? "Calling..." : "Call"}
            </button>
          </div>
        ) : (
          // Call Controls
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 py-0 w-full">
            {/* Current User Display */}
            <div className="mb-3 text-sm" style={{ color: "#7e7e7e" }}>
              Currently talking to:{" "}
              <span className="font-medium" style={{ color: "#7e7e7e" }}>
                {item?.name || "Client"}
              </span>
            </div>

            <div className="flex items-center justify-between">
              {/* Timer */}
              <div className="text-lg font-mono" style={{ color: "#7e7e7e" }}>
                {formatTimer(callTimer)}
              </div>

              {/* Call Control Buttons */}
              <div className="flex items-center space-x-3">
                {/* End Call Button */}
                <button
                  type="button"
                  onClick={handleEndCall}
                  className="inline-flex items-center px-6 py-3 font-medium rounded-lg transition-colors duration-200"
                  style={{ backgroundColor: "#f46b68" }}
                  onMouseEnter={(e) =>
                    (e.target.style.backgroundColor = "#e55a57")
                  }
                  onMouseLeave={(e) =>
                    (e.target.style.backgroundColor = "#f46b68")
                  }
                >
                  <PhoneMissed size={16} className="mr-1 text-white" />
                  <span className="text-white">End</span>
                </button>

                {/* Mute/Unmute Button */}
                <button
                  type="button"
                  onClick={handleToggleMute}
                  className={`inline-flex items-center px-6 py-3 font-medium rounded-lg transition-colors duration-200 ${
                    isMuted ? "bg-transparent" : ""
                  }`}
                  style={!isMuted ? { backgroundColor: "#369dc9" } : {}}
                  onMouseEnter={(e) => {
                    if (!isMuted) {
                      e.target.style.backgroundColor = "#2a7ba7";
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isMuted) {
                      e.target.style.backgroundColor = "#369dc9";
                    }
                  }}
                >
                  {isMuted ? (
                    <>
                      <Mic size={16} className="mr-1 text-gray-500" />
                      <span className="text-gray-500">Unmute</span>
                    </>
                  ) : (
                    <>
                      <MicOff size={16} className="mr-1 text-white" />
                      <span className="text-white">Mute</span>
                    </>
                  )}
                </button>

                {/* Hold/Unhold Button */}
                <button
                  type="button"
                  onClick={handleToggleHold}
                  className={`inline-flex items-center px-6 py-3 font-medium rounded-lg transition-colors duration-200 ${
                    isOnHold ? "bg-transparent" : ""
                  }`}
                  style={!isOnHold ? { backgroundColor: "#ffb800" } : {}}
                  onMouseEnter={(e) => {
                    if (!isOnHold) {
                      e.target.style.backgroundColor = "#e6a600";
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isOnHold) {
                      e.target.style.backgroundColor = "#ffb800";
                    }
                  }}
                >
                  {isOnHold ? (
                    <>
                      <Play size={16} className="mr-1 text-gray-500" />
                      <span className="text-gray-500">Unhold</span>
                    </>
                  ) : (
                    <>
                      <Pause size={16} className="mr-1 text-white" />
                      <span className="text-white">Hold</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Call Type */}
      <div className="flex items-center gap-4">
        <label
          className="text-sm font-medium w-32 flex-shrink-0"
          style={{ color: "#7e7e7e" }}
        >
          Call Type *
        </label>
        <div className="flex-1">
          <select
            name="callType"
            value={formData.callType}
            onChange={handleChange}
            className={`w-full px-4 py-3 border rounded-lg bg-white outline-none transition-colors duration-200 ${
              errors.callType
                ? "border-red-500"
                : "border-gray-300 focus:border-green-500 hover:border-gray-400"
            }`}
            style={{ color: "#7e7e7e" }}
          >
            <option value="">--select--</option>
            <option value="First Contact">First Contact</option>
            <option value="Follow Up">Follow Up</option>
          </select>
          {errors.callType && (
            <p className="mt-1 text-sm text-red-600">{errors.callType}</p>
          )}
        </div>
      </div>

      {/* Call Status */}
      <div className="flex items-center gap-4">
        <label
          className="text-sm font-medium w-32 flex-shrink-0"
          style={{ color: "#7e7e7e" }}
        >
          Call Status *
        </label>
        <div className="flex-1">
          <select
            name="callStatus"
            value={formData.callStatus}
            onChange={handleChange}
            className={`w-full px-4 py-3 border rounded-lg bg-white outline-none transition-colors duration-200 ${
              errors.callStatus
                ? "border-red-500"
                : "border-gray-300 focus:border-green-500 hover:border-gray-400"
            }`}
            style={{ color: "#7e7e7e" }}
          >
            <option value="">--select--</option>
            <option value="Success">Success</option>
            <option value="Declined">Declined</option>
          </select>
          {errors.callStatus && (
            <p className="mt-1 text-sm text-red-600">{errors.callStatus}</p>
          )}
        </div>
      </div>

      {/* Purpose */}
      <div className="flex items-center gap-4">
        <label
          className="text-sm font-medium w-32 flex-shrink-0"
          style={{ color: "#7e7e7e" }}
        >
          Purpose *
        </label>
        <div className="flex-1">
          <Select
            name="purposeId"
            value={purposes.find(
              (option) => option.value === formData.purposeId
            )}
            onChange={handleSelectChange}
            options={purposes}
            styles={selectStyles}
            placeholder={
              purposesLoading ? "Loading purposes..." : "Select purpose"
            }
            isSearchable
            isLoading={purposesLoading}
            isDisabled={purposesLoading}
            className="react-select-container"
            classNamePrefix="react-select"
          />
          {errors.purposeId && (
            <p className="mt-1 text-sm text-red-600">{errors.purposeId}</p>
          )}
        </div>
      </div>

      {/*/!* Customer Feedback Category *!/*/}
      {/*<div className="flex items-center gap-4">*/}
      {/*  <label*/}
      {/*    className="text-sm font-medium w-32 flex-shrink-0"*/}
      {/*    style={{ color: "#7e7e7e" }}*/}
      {/*  >*/}
      {/*    Customer Feedback*/}
      {/*  </label>*/}
      {/*  <div className="flex-1">*/}
      {/*    <Select*/}
      {/*      name="customerFeedbackCategoryId"*/}
      {/*      value={feedbackCategories.find(*/}
      {/*        (option) => option.value === formData.customerFeedbackCategoryId*/}
      {/*      )}*/}
      {/*      onChange={handleSelectChange}*/}
      {/*      options={feedbackCategories}*/}
      {/*      styles={selectStyles}*/}
      {/*      placeholder={*/}
      {/*        feedbackCategoriesLoading */}
      {/*          ? "Loading categories..." */}
      {/*          : "Select feedback category"*/}
      {/*      }*/}
      {/*      isSearchable*/}
      {/*      isLoading={feedbackCategoriesLoading}*/}
      {/*      isDisabled={feedbackCategoriesLoading}*/}
      {/*      className="react-select-container"*/}
      {/*      classNamePrefix="react-select"*/}
      {/*    />*/}
      {/*    {errors.customerFeedbackCategoryId && (*/}
      {/*      <p className="mt-1 text-sm text-red-600">{errors.customerFeedbackCategoryId}</p>*/}
      {/*    )}*/}
      {/*  </div>*/}
      {/*</div>*/}

      {/* Notes */}
      <div className="flex gap-4">
        <label
          className="text-sm font-medium w-32 flex-shrink-0 pt-3"
          style={{ color: "#7e7e7e" }}
        >
          Notes
        </label>
        <div className="flex-1">
          <textarea
            name="notes"
            value={formData.notes}
            onChange={handleChange}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-white outline-none transition-colors duration-200 focus:border-green-500 hover:border-gray-400 resize-none"
            style={{ color: "#7e7e7e" }}
            placeholder="Add your notes..."
            rows="4"
          />
        </div>
      </div>

      {/* Follow-up Date & Time */}
      <div className="flex items-center gap-4">
        <label
          className="text-sm font-medium w-32 flex-shrink-0"
          style={{ color: "#7e7e7e" }}
        >
          Follow up date
        </label>
        <div className="flex-1 flex gap-3">
          <input
            type="date"
            name="followUpDate"
            value={formData.followUpDate}
            onChange={handleChange}
            className="flex-1 px-4 py-3 border border-gray-300 rounded-lg bg-white outline-none transition-colors duration-200 focus:border-green-500 hover:border-gray-400"
            style={{ color: "#7e7e7e" }}
          />
          <input
            type="time"
            name="followUpTime"
            value={formData.followUpTime}
            onChange={handleChange}
            className="flex-1 px-4 py-3 border border-gray-300 rounded-lg bg-white outline-none transition-colors duration-200 focus:border-green-500 hover:border-gray-400"
            style={{ color: "#7e7e7e" }}
          />
        </div>
      </div>

      {/* Attachments */}
      <div className="flex gap-4">
        <label
          className="text-sm font-medium w-32 flex-shrink-0 pt-3"
          style={{ color: "#7e7e7e" }}
        >
          Attachments
        </label>
        <div className="flex-1">
          {/* File Upload Area */}
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-400 transition-colors">
            <input
              type="file"
              multiple
              onChange={handleFileUpload}
              className="hidden"
              id="file-upload"
              accept="*/*"
            />
            <label
              htmlFor="file-upload"
              className="cursor-pointer flex flex-col items-center gap-2"
            >
              <Upload size={24} style={{ color: "#7e7e7e" }} />
              <span className="text-sm" style={{ color: "#7e7e7e" }}>
                Click to upload files or drag and drop
              </span>
              <span className="text-xs" style={{ color: "#9ca3af" }}>
                Any file format supported
              </span>
            </label>
          </div>

          {/* Uploaded Files List */}
          {formData.attachments.length > 0 && (
            <div className="mt-3 space-y-2">
              {formData.attachments.map((attachment) => (
                <div
                  key={attachment.id}
                  className="flex items-center justify-between p-2 bg-gray-50 rounded-lg border"
                >
                  <div className="flex items-center gap-2">
                    <div className="text-sm" style={{ color: "#7e7e7e" }}>
                      <div className="font-medium">{attachment.name}</div>
                      <div className="text-xs" style={{ color: "#9ca3af" }}>
                        {(attachment.size / 1024).toFixed(1)} KB •{" "}
                        {attachment.type || "Unknown type"}
                      </div>
                    </div>
                  </div>
                  <button
                    type="button"
                    onClick={() => removeAttachment(attachment.id)}
                    className="p-1 text-red-500 hover:text-red-700 transition-colors"
                  >
                    <X size={16} />
                  </button>
                </div>
              ))}
            </div>
          )}
          </div>
        </div>

        {/* Lead Status Update Section */}
        {item && (
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-800">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <h3 className="text-sm font-medium text-blue-900 dark:text-blue-100">
                    Update Lead Status
                  </h3>
                </div>
                

              </div>
              <div className="flex-shrink-0 ml-6">
                <LeadStatusChanger
                  currentStatus={item.status || "Pending"}
                  leadId={item.id}
                  leadName={item.name || item.customerName || "Lead"}
                  onStatusChange={handleLeadStatusChange}
                  size="md"
                />
              </div>
            </div>
          </div>
        )}
        </div>

        {/* Fixed Buttons at Bottom */}
        <div className="flex-shrink-0 border-t border-gray-200 dark:border-gray-600 pt-4 mt-4">
          <div className="flex justify-end gap-3">
            <button
          type="button"
          onClick={onClose}
          className="px-7 py-3 text-sm font-medium rounded-lg transition-colors"
          style={{
            color: "#f46b68",
            backgroundColor: "#fef7f7",
          }}
          onMouseEnter={(e) => {
            e.target.style.backgroundColor = "#f46b68";
            e.target.style.color = "white";
          }}
          onMouseLeave={(e) => {
            e.target.style.backgroundColor = "#fef7f7";
            e.target.style.color = "#f46b68";
          }}
        >
          Close
            </button>
            <button
          type="submit"
          disabled={isSubmitting}
          className="px-7 py-3 text-white text-sm font-medium rounded-lg disabled:opacity-50 transition-colors"
          style={{ backgroundColor: "#1c5b41" }}
          onMouseEnter={(e) => {
            if (!isSubmitting) {
              e.target.style.backgroundColor = "#2bc155";
            }
          }}
          onMouseLeave={(e) => {
            if (!isSubmitting) {
              e.target.style.backgroundColor = "#1c5b41";
            }
          }}
        >
          {isSubmitting ? "Saving..." : "Submit"}
            </button>
          </div>
        </div>
        </div>
      </form>
    </div>
  );
};

export default CallForm;
