import { useState, useEffect } from "react";
import { Minus } from "lucide-react";
import {
  ProfileHeader,
  TabNavigation,
  ProfileTab,
  HistoryTab,
} from "./profile";
import { followUpsService } from "../../services/followUpsService";

const FollowUpLeadProfile = ({ item, onClose }) => {
  const [activeTab, setActiveTab] = useState("profile");
  const [leadDetails, setLeadDetails] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch lead details from follow-up specific endpoint
  useEffect(() => {
    const fetchLeadDetails = async () => {
      if (!item?.id) {
        setError("No follow-up ID provided");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        
        console.log("=== FETCHING FOLLOW-UP LEAD DETAILS ===");
        console.log("Follow-up ID:", item.id);
        
        const response = await followUpsService.getLeadDetails(item.id);
        
        console.log("Lead details response:", response);
        setLeadDetails(response);
        
      } catch (error) {
        console.error("Error fetching follow-up lead details:", error);
        setError(error.response?.data?.message || "Failed to load lead details");
      } finally {
        setLoading(false);
      }
    };

    fetchLeadDetails();
  }, [item?.id]);

  const formatValue = (value) => {
    if (value === null || value === undefined || value === "") {
      return <span className="text-gray-400">-</span>;
    }
    return value;
  };

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
      onClick={handleBackdropClick}
    >
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            Lead Profile - {item?.customer_name || "Unknown Customer"}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <Minus size={24} />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          {loading ? (
            <div className="flex items-center justify-center h-full">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
              <span className="ml-2 text-gray-600">Loading lead details...</span>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="text-red-500 text-lg mb-2">Error</div>
                <div className="text-gray-600">{error}</div>
                <button
                  onClick={() => window.location.reload()}
                  className="mt-4 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  Retry
                </button>
              </div>
            </div>
          ) : (
            <div className="h-full flex flex-col">
              {/* Profile Header */}
              <ProfileHeader 
                leadDetails={leadDetails} 
                item={item}
                formatValue={formatValue}
              />

              {/* Tab Navigation - Only Profile and History */}
              <div className="flex border-b border-gray-200 -mx-6 px-6 pl-[10%]">
                <button
                  onClick={() => setActiveTab("profile")}
                  className={`flex items-center gap-2 px-0 py-3 mr-8 font-medium border-b-2 transition-colors ${
                    activeTab === "profile"
                      ? "border-[#1c5b41] text-[#1c5b41] hover:text-[#0f3d2a]"
                      : "border-transparent text-gray-400 hover:text-gray-700"
                  }`}
                >
                  Profile
                </button>
                <button
                  onClick={() => setActiveTab("history")}
                  className={`flex items-center gap-2 px-0 py-3 mr-8 font-medium border-b-2 transition-colors ${
                    activeTab === "history"
                      ? "border-[#1c5b41] text-[#1c5b41] hover:text-[#0f3d2a]"
                      : "border-transparent text-gray-400 hover:text-gray-700"
                  }`}
                >
                  History
                </button>
              </div>

              {/* Tab Content */}
              <div className="flex-1 overflow-y-auto">
                {activeTab === "profile" && (
                  <ProfileTab
                    item={item}
                    leadDetails={leadDetails}
                    onClose={onClose}
                    formatValue={formatValue}
                  />
                )}
                {activeTab === "history" && (
                  <HistoryTab
                    onClose={onClose}
                    item={item}
                    entityId={item?.id}
                    entityType="follow-up"
                    title="Interaction History"
                    emptyMessage="No interactions found for this follow-up."
                    // Use follow-up specific endpoint
                    customEndpoint={`/follow-ups/${item?.id}/interaction-history`}
                  />
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FollowUpLeadProfile;
