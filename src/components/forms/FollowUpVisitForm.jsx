import { useState, useEffect } from "react";
import { Upload, X } from "lucide-react";
import Select from "react-select";
import {
  purposesService,
  formatPurposesForTable,
} from "../../services/purposesService";
import { followUpsService } from "../../services/followUpsService";
import LeadStatusChanger from "../common/LeadStatusChanger";
import { toast } from "react-toastify";

const FollowUpVisitForm = ({ item, onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    visitStatus: "",
    purposeId: "",
    notes: "",
    attachments: [],
  });

  const [purposes, setPurposes] = useState([]);
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showLeadStatusChanger, setShowLeadStatusChanger] = useState(false);

  // Fetch purposes for the dropdown
  const fetchPurposes = async () => {
    try {
      const response = await purposesService.getAll();
      const formattedPurposes = formatPurposesForTable(response);
      setPurposes(formattedPurposes);
    } catch (error) {
      console.error("Error fetching purposes:", error);
      toast.error("Failed to load purposes");
    }
  };

  useEffect(() => {
    fetchPurposes();
  }, []);

  useEffect(() => {
    if (item) {
      setFormData({
        visitStatus: item.visitStatus || "",
        purposeId: item.purposeId || "",
        notes: item.notes || "",
        attachments: item.attachments || [],
      });
    }
  }, [item]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  const handlePurposeChange = (selectedOption) => {
    setFormData((prev) => ({
      ...prev,
      purposeId: selectedOption ? selectedOption.value : "",
    }));
    if (errors.purposeId) {
      setErrors((prev) => ({ ...prev, purposeId: "" }));
    }
  };

  const handleFileUpload = (e) => {
    const files = Array.from(e.target.files);
    const newAttachments = files.map((file) => ({
      id: Date.now() + Math.random(),
      file,
      name: file.name,
      size: file.size,
    }));

    setFormData((prev) => ({
      ...prev,
      attachments: [...prev.attachments, ...newAttachments],
    }));
  };

  const removeAttachment = (attachmentId) => {
    setFormData((prev) => ({
      ...prev,
      attachments: prev.attachments.filter((att) => att.id !== attachmentId),
    }));
  };

  const validateForm = () => {
    const newErrors = {};
    if (!formData.visitStatus) newErrors.visitStatus = "Visit status is required";
    if (!formData.purposeId) newErrors.purposeId = "Purpose is required";
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Create FormData for file uploads
      const formDataToSend = new FormData();

      // Add form fields
      formDataToSend.append("visit_status", formData.visitStatus);
      formDataToSend.append("purpose_id", formData.purposeId);
      formDataToSend.append("notes", formData.notes);

      // Add actual files to FormData
      formData.attachments.forEach((attachment) => {
        formDataToSend.append(`attachments`, attachment.file);
      });

      console.log("=== SUBMITTING FOLLOW-UP VISIT ===");
      console.log("Follow-up ID:", item?.id);
      console.log("FormData contents:", Object.fromEntries(formDataToSend));

      // Call the follow-up service
      const response = await followUpsService.makeVisitOrCall(item?.id, formDataToSend);

      console.log("Follow-up visit response:", response);

      // Call the onSubmit callback with success
      onSubmit?.(response, item);
      onClose();
      toast.success("Visit recorded successfully!");

    } catch (error) {
      console.error("Error submitting follow-up visit:", error);
      toast.error(error.response?.data?.message || "Failed to record visit");
      onSubmit?.(null, item, error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Format purposes for react-select
  const purposeOptions = purposes.map((purpose) => ({
    value: purpose.id,
    label: purpose.name,
  }));

  const selectedPurpose = purposeOptions.find(
    (option) => option.value === formData.purposeId
  );

  return (
    <div className="flex flex-col h-[80vh]">
      <form onSubmit={handleSubmit} className="flex flex-col h-full">
        {/* Scrollable Content */}
        <div className="flex-1 overflow-y-auto overflow-x-hidden scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 px-1">
          <div className="flex flex-col gap-4 pb-4">
            
            {/* Customer Info Display */}
            <div className="bg-gray-50 p-3 rounded-lg mb-4">
              <p className="text-sm text-gray-600">Customer</p>
              <p className="font-medium text-gray-900">
                {item?.customer_name || item?.name || "Unknown Customer"}
              </p>
            </div>

            {/* Visit Status */}
            <div className="flex items-center gap-4 mt-7">
              <label
                className="text-sm font-medium w-32 flex-shrink-0"
                style={{ color: "#7e7e7e" }}
              >
                Visit Status *
              </label>
              <div className="flex-1">
                <select
                  name="visitStatus"
                  value={formData.visitStatus}
                  onChange={handleChange}
                  className={`w-full px-4 py-3 border rounded-lg bg-white outline-none transition-colors duration-200 ${
                    errors.visitStatus
                      ? "border-red-500"
                      : "border-gray-300 focus:border-green-500 hover:border-gray-400"
                  }`}
                  style={{ color: "#7e7e7e" }}
                >
                  <option value="">--select--</option>
                  <option value="Successful">Successful</option>
                  <option value="Customer not available">Customer not available</option>
                  <option value="Premises closed">Premises closed</option>
                  <option value="Wrong address">Wrong address</option>
                  <option value="Rescheduled">Rescheduled</option>
                </select>
                {errors.visitStatus && (
                  <p className="mt-1 text-sm text-red-600">{errors.visitStatus}</p>
                )}
              </div>
            </div>

            {/* Purpose */}
            <div className="flex items-center gap-4">
              <label
                className="text-sm font-medium w-32 flex-shrink-0"
                style={{ color: "#7e7e7e" }}
              >
                Purpose *
              </label>
              <div className="flex-1">
                <Select
                  value={selectedPurpose}
                  onChange={handlePurposeChange}
                  options={purposeOptions}
                  placeholder="--select--"
                  isClearable
                  className={`react-select-container ${
                    errors.purposeId ? "react-select-error" : ""
                  }`}
                  classNamePrefix="react-select"
                  styles={{
                    control: (provided, state) => ({
                      ...provided,
                      borderColor: errors.purposeId
                        ? "#ef4444"
                        : state.isFocused
                        ? "#10b981"
                        : "#d1d5db",
                      "&:hover": {
                        borderColor: errors.purposeId
                          ? "#ef4444"
                          : state.isFocused
                          ? "#10b981"
                          : "#9ca3af",
                      },
                      boxShadow: "none",
                      minHeight: "48px",
                    }),
                    placeholder: (provided) => ({
                      ...provided,
                      color: "#7e7e7e",
                    }),
                    singleValue: (provided) => ({
                      ...provided,
                      color: "#7e7e7e",
                    }),
                  }}
                />
                {errors.purposeId && (
                  <p className="mt-1 text-sm text-red-600">{errors.purposeId}</p>
                )}
              </div>
            </div>

            {/* Notes */}
            <div className="flex items-start gap-4">
              <label
                className="text-sm font-medium w-32 flex-shrink-0 pt-3"
                style={{ color: "#7e7e7e" }}
              >
                Notes
              </label>
              <div className="flex-1">
                <textarea
                  name="notes"
                  value={formData.notes}
                  onChange={handleChange}
                  rows={4}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-white outline-none transition-colors duration-200 focus:border-green-500 hover:border-gray-400 resize-none"
                  style={{ color: "#7e7e7e" }}
                  placeholder="Enter any additional notes..."
                />
              </div>
            </div>

            {/* Attachments */}
            <div className="flex items-start gap-4">
              <label
                className="text-sm font-medium w-32 flex-shrink-0 pt-3"
                style={{ color: "#7e7e7e" }}
              >
                Attachments
              </label>
              <div className="flex-1">
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 hover:border-gray-400 transition-colors duration-200">
                  <input
                    type="file"
                    multiple
                    onChange={handleFileUpload}
                    className="hidden"
                    id="file-upload-visit"
                    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif"
                  />
                  <label
                    htmlFor="file-upload-visit"
                    className="flex flex-col items-center cursor-pointer"
                  >
                    <Upload size={24} className="text-gray-400 mb-2" />
                    <span className="text-sm text-gray-600">
                      Click to upload files
                    </span>
                    <span className="text-xs text-gray-400 mt-1">
                      PDF, DOC, DOCX, JPG, PNG, GIF (Max 10MB each)
                    </span>
                  </label>
                </div>

                {/* Display uploaded files */}
                {formData.attachments.length > 0 && (
                  <div className="mt-3 space-y-2">
                    {formData.attachments.map((attachment) => (
                      <div
                        key={attachment.id}
                        className="flex items-center justify-between bg-gray-50 p-2 rounded border"
                      >
                        <span className="text-sm text-gray-700 truncate">
                          {attachment.name}
                        </span>
                        <button
                          type="button"
                          onClick={() => removeAttachment(attachment.id)}
                          className="text-red-500 hover:text-red-700 ml-2"
                        >
                          <X size={16} />
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Update Lead Status */}
            <div className="flex items-center gap-4">
              <label
                className="text-sm font-medium w-32 flex-shrink-0"
                style={{ color: "#7e7e7e" }}
              >
                Update Lead Status
              </label>
              <div className="flex-1">
                <button
                  type="button"
                  onClick={() => setShowLeadStatusChanger(true)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
                >
                  Change Status
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Fixed Footer */}
        <div className="border-t bg-white p-4 flex justify-end gap-3">
          <button
            type="button"
            onClick={onClose}
            disabled={isSubmitting}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Recording...
              </>
            ) : (
              "Record Visit"
            )}
          </button>
        </div>
      </form>

      {/* Lead Status Changer Modal */}
      {showLeadStatusChanger && (
        <LeadStatusChanger
          leadId={item?.id}
          currentStatus={item?.status}
          onClose={() => setShowLeadStatusChanger(false)}
          onStatusUpdate={(newStatus) => {
            console.log("Lead status updated:", newStatus);
            setShowLeadStatusChanger(false);
          }}
        />
      )}
    </div>
  );
};

export default FollowUpVisitForm;
