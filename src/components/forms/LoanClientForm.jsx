import { useState, useEffect } from "react";
import { Info } from "lucide-react";
import Select from "react-select";
import {
  isicSectorsService,
  formatSectorsResponse,
} from "../../services/isicSectorsService";
import {
  customerCategoriesService,
  formatCategoriesResponse,
} from "../../services/customerCategoriesService";
import {
  branchesService,
  formatBranchesResponse,
} from "../../services/branchesService";
import { loanService } from "../../services/loanService";
import { anchorsService, formatAnchorsForDropdown } from "../../services/anchorsService";
import instance from "../../axios/instance.jsx";

//NOTE: if an item is passed in, it's an edit form
const LoanClientForm = ({
  item,
  onClose,
  onSubmit,
  initialAnchorRelationship,
  initialAnchor,
  initialCustomerCategory,
  initialIsicSector,
  initialBranch,
}) => {
  console.log("=== LOAN CLIENT FORM PROPS ===");
  console.log("Item:", item);
  console.log("Item customer_category:", item?.customer_category);
  console.log("Item isic_sector:", item?.isic_sector);
  console.log("Item branch:", item?.branch);
  console.log("Item anchor_relationship_id:", item?.anchor_relationship_id);
  console.log("Item parent_lead_id:", item?.parent_lead_id);
  console.log("Initial Customer Category:", initialCustomerCategory);
  console.log("Initial ISIC Sector:", initialIsicSector);
  console.log("Initial Branch:", initialBranch);
  console.log("Initial Anchor Relationship:", initialAnchorRelationship);
  console.log("===============================");
  const [formData, setFormData] = useState({
    anchorId: "", // New field for anchor selection
    anchorRelationshipId: "", // New field for anchor relationship
    customerName: "",
    phoneNumber: "",
    customerCategoryId: "", // Changed to store ID instead of name
    isicSectorId: "", // Changed to store ID instead of name
    branchId: "", // Changed to store ID instead of name
    contactPersonName: "",
    contactPersonPhone: "",
    employerId: "", // Changed to store ID instead of name
    employerName: "", // New field for employer name
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [anchors, setAnchors] = useState([]);
  const [anchorsLoading, setAnchorsLoading] = useState(true);
  const [anchorRelationships, setAnchorRelationships] = useState([]);
  const [anchorRelationshipsLoading, setAnchorRelationshipsLoading] =
    useState(true);
  const [isicSectors, setIsicSectors] = useState([]);
  const [isicSectorsLoading, setIsicSectorsLoading] = useState(true);
  const [customerCategories, setCustomerCategories] = useState([]);
  const [customerCategoriesLoading, setCustomerCategoriesLoading] =
    useState(true);

  const [branches, setBranches] = useState([]);
  const [branchesLoading, setBranchesLoading] = useState(true);

  // Track if form has been initialized to prevent re-initialization
  const [isFormInitialized, setIsFormInitialized] = useState(false);

  // Fetch all dropdown data on component mount
  useEffect(() => {
    fetchAnchors();
    fetchAnchorRelationships();
    fetchIsicSectors();
    fetchCustomerCategories();
    fetchBranches();
  }, []);

  // Initialize form for new loan clients (when no item is provided)
  useEffect(() => {
    if (!item && !isFormInitialized) {
      console.log("Checking if form can be initialized for new loan client...");
      console.log("Customer categories loaded:", customerCategories.length);
      console.log("ISIC sectors loaded:", isicSectors.length);
      console.log("Branches loaded:", branches.length);
      
      // For new loan clients, wait for dropdown options to load, then initialize
      // We need at least customer categories and branches to be functional
      if (customerCategories.length > 0 && branches.length > 0) {
        console.log("Initializing form for new loan client - required options loaded");
        setIsFormInitialized(true);
      } else {
        console.log("Waiting for required options to load before initializing form...");
      }
    }
  }, [item, isFormInitialized, customerCategories.length, isicSectors.length, branches.length]);

  // Alternative initialization: Initialize form when user starts filling it out
  useEffect(() => {
    if (!item && !isFormInitialized && formData.customerName && formData.phoneNumber) {
      console.log("Form being filled out manually, checking if we can initialize...");
      if (customerCategories.length > 0 && branches.length > 0) {
        console.log("Initializing form based on manual input");
        setIsFormInitialized(true);
      }
    }
  }, [item, isFormInitialized, formData.customerName, formData.phoneNumber, customerCategories.length, branches.length]);

  const fetchAnchors = async () => {
    try {
      setAnchorsLoading(true);
      console.log("Fetching anchors from dedicated anchors API...");

      // Use the new dedicated anchors service
      const response = await anchorsService.getAll();

      // Convert to React Select format using the new formatter
      const anchorOptions = formatAnchorsForDropdown(response).map(anchor => ({
        ...anchor,
        value: String(anchor.value) // Ensure value is string
      }));

      console.log(`Successfully loaded ${anchorOptions.length} anchors`);
      setAnchors(anchorOptions);
    } catch (error) {
      console.error("Error fetching anchors:", error);

      // Fallback to loan service if anchors API is not available
      console.warn("Falling back to loan service for anchors...");
      try {
        const fallbackResponse = await loanService.getAll();
        const fallbackOptions = fallbackResponse.data.map((loanClient) => {
          // Create label with format: {customer_name} - {account_id} (fallback case)
          const accountId = loanClient.account_id || loanClient.account_number || "No Account ID";
          const label = `${loanClient.lead_name || loanClient.customer_name} - ${accountId}`;

          return {
            value: String(loanClient.id), // Convert ID to string for consistency
            label: label,
            name: loanClient.lead_name || loanClient.customer_name, // Keep original name for reference
            accountId: accountId, // Keep account ID for reference
            email: loanClient.email || "No email",
            phoneNumber: loanClient.phoneNumber || "No phone",
            leadsCount: 0,
            isInUse: false,
          };
        });

        console.log(`Fallback: loaded ${fallbackOptions.length} loan clients as anchors`);
        setAnchors(fallbackOptions);
      } catch (fallbackError) {
        console.error("Error in fallback anchor fetching:", fallbackError);
        setAnchors([]);
      }
    } finally {
      setAnchorsLoading(false);
    }
  };

  const fetchAnchorRelationships = async () => {
    try {
      setAnchorRelationshipsLoading(true);
      // Make API call to fetch anchor relationships using axios instance
      const response = await instance.get("/anchor-relationships");
      console.log("Anchor relationships response:", response.data);

      // Convert to React Select format
      const relationshipOptions = response.data.data.map((relationship) => ({
        value: String(relationship.id), // Convert ID to string for consistency
        label: relationship.name,
        relationship: relationship,
      }));

      setAnchorRelationships(relationshipOptions);
      console.log("Processed anchor relationships:", relationshipOptions);
    } catch (error) {
      console.error("Error fetching anchor relationships:", error);
      setAnchorRelationships([]);
    } finally {
      setAnchorRelationshipsLoading(false);
    }
  };

  const fetchIsicSectors = async () => {
    try {
      setIsicSectorsLoading(true);
      const response = await isicSectorsService.getAll();
      const formattedSectors = formatSectorsResponse(response);

      // Convert to React Select format
      const sectorOptions = formattedSectors.map((sector) => ({
        value: String(sector.id), // Convert ID to string for consistency
        label: `${sector.code ? `${sector.code} - ` : ""}${sector.name}`, // Show code and name
        sector: sector, // Keep full sector data for reference
      }));

      setIsicSectors(sectorOptions);
      console.log("ISIC Sectors loaded:", sectorOptions.length, "options");
      if (sectorOptions.length > 0) {
        console.log("Sample sector option:", sectorOptions[0]);
      }
    } catch (error) {
      console.error("Error fetching ISIC sectors:", error);
      setIsicSectors([]); // Set empty array on error
    } finally {
      setIsicSectorsLoading(false);
    }
  };

  const fetchCustomerCategories = async () => {
    try {
      setCustomerCategoriesLoading(true);
      const response = await customerCategoriesService.getAll();
      const formattedCategories = formatCategoriesResponse(response);

      // Convert to React Select format
      const categoryOptions = formattedCategories.map((category) => ({
        value: String(category.id), // Convert ID to string for consistency
        label: category.name,
        category: category, // Keep full category data for reference
      }));

      setCustomerCategories(categoryOptions);
      console.log("Customer Categories loaded:", categoryOptions.length, "options");
      if (categoryOptions.length > 0) {
        console.log("Sample category option:", categoryOptions[0]);
      }
    } catch (error) {
      console.error("Error fetching customer categories:", error);
      setCustomerCategories([]); // Set empty array on error
    } finally {
      setCustomerCategoriesLoading(false);
    }
  };

  const fetchBranches = async () => {
    try {
      setBranchesLoading(true);
      const response = await branchesService.getAll();
      const formattedBranches = formatBranchesResponse(response);

      // Convert to React Select format
      const branchOptions = formattedBranches.map((branch) => ({
        value: String(branch.id), // Convert ID to string for consistency
        label: `${branch.name}`, // Show code and name
        branch: branch, // Keep full branch data for reference
      }));

      setBranches(branchOptions);
      console.log("Branches loaded:", branchOptions.length, "options");
      if (branchOptions.length > 0) {
        console.log("Sample branch option:", branchOptions[0]);
      }
    } catch (error) {
      console.error("Error fetching branches:", error);
      setBranches([]); // Set empty array on error
    } finally {
      setBranchesLoading(false);
    }
  };

  // Populate form with loan client data for editing using passed props or API fallback
  useEffect(() => {
    const populateFormData = async () => {
      if (!item || isFormInitialized) return;

      console.log("Populating form for editing with item:", item);
      console.log("Initial customer category:", initialCustomerCategory);
      console.log("Initial ISIC sector:", initialIsicSector);
      console.log("Initial branch:", initialBranch);
      console.log("Initial anchor relationship:", initialAnchorRelationship);
      console.log("Anchor ID from item:", item.parent_lead_id || item.anchorId);
      console.log(
        "Anchor Relationship ID from item:",
        item.anchor_relationship_id || item.anchorRelationshipId
      );
      console.log(
        "Anchor Relationship Name from item:",
        item.anchor_relationship_name
      );
      console.log("Parent Lead Name from item:", item.parent_lead_name);
      console.log("Employer Name from item:", item.employerName);

      // Always use the initial props if available, otherwise fall back to item data
      const formDataToSet = {
        anchorId: item.parent_lead_id || item.anchorId || "",
        anchorRelationshipId:
          (initialAnchorRelationship && initialAnchorRelationship.id) ||
          item.anchor_relationship_id ||
          item.anchorRelationshipId ||
          "",
        customerName: item.lead_name || item.name || "",
        phoneNumber: item.phoneNumber || "",
        customerCategoryId:
          (initialCustomerCategory && initialCustomerCategory.id) ||
          item.customer_category?.id ||
          "",
        isicSectorId:
          (initialIsicSector && initialIsicSector.id) ||
          item.isic_sector?.id ||
          "",
        branchId:
          (initialBranch && initialBranch.id) ||
          item.branch?.id ||
          "",
        contactPersonName: item.contactPersonName || "",
        contactPersonPhone: item.contactPersonPhone || "",
        employerId: item.employerId || "",
        employerName: item.employerName || "",
      };

      // Convert IDs to strings to ensure type consistency with React Select
      if (formDataToSet.customerCategoryId) {
        formDataToSet.customerCategoryId = String(formDataToSet.customerCategoryId);
      }
      if (formDataToSet.isicSectorId) {
        formDataToSet.isicSectorId = String(formDataToSet.isicSectorId);
      }
      if (formDataToSet.branchId) {
        formDataToSet.branchId = String(formDataToSet.branchId);
      }
      if (formDataToSet.anchorId) {
        formDataToSet.anchorId = String(formDataToSet.anchorId);
      }
      if (formDataToSet.anchorRelationshipId) {
        formDataToSet.anchorRelationshipId = String(formDataToSet.anchorRelationshipId);
      }

      // If we don't have initial props but have item.id, try to fetch details from API
      if (!initialCustomerCategory && !initialIsicSector && !initialBranch && item.id) {
        try {
          console.log("Fetching loan client details for editing:", item.id);
          const loanClientDetails = await loanService.getDetails(item.id);
          console.log("Loan client details fetched:", loanClientDetails);

          // Get first contact person if available
          const firstContactPerson =
            loanClientDetails.contact_persons &&
            loanClientDetails.contact_persons.length > 0
              ? loanClientDetails.contact_persons[0]
              : null;

          // Update form data with API response
          formDataToSet.customerCategoryId = loanClientDetails.customer_category?.id || formDataToSet.customerCategoryId;
          formDataToSet.isicSectorId = loanClientDetails.isic_sector?.id || formDataToSet.isicSectorId;
          formDataToSet.branchId = loanClientDetails.branch?.id || formDataToSet.branchId;
          formDataToSet.anchorId = loanClientDetails.parent_lead_id || loanClientDetails.anchorId || formDataToSet.anchorId;
          formDataToSet.anchorRelationshipId = loanClientDetails.anchor_relationship_id || loanClientDetails.anchorRelationshipId || formDataToSet.anchorRelationshipId;
          formDataToSet.customerName = loanClientDetails.customer_name || formDataToSet.customerName;
          formDataToSet.phoneNumber = loanClientDetails.phone_number || formDataToSet.phoneNumber;
          formDataToSet.contactPersonName = firstContactPerson?.name || formDataToSet.contactPersonName;
          formDataToSet.contactPersonPhone = firstContactPerson?.phone_number || formDataToSet.contactPersonPhone;
          formDataToSet.employerId = loanClientDetails.employer?.id || formDataToSet.employerId;
          formDataToSet.employerName = loanClientDetails.employerName || formDataToSet.employerName;

          console.log("Form data populated from API:", formDataToSet);
        } catch (error) {
          console.error("Error fetching loan client details:", error);
          // Keep the basic form data if API call fails
        }
      }

      console.log("Final form data to set:", formDataToSet);
      console.log("Available customer categories:", customerCategories.length);
      console.log("Available ISIC sectors:", isicSectors.length);
      console.log("Available branches:", branches.length);
      console.log("Available anchors:", anchors.length);
      console.log("Available anchor relationships:", anchorRelationships.length);
      
      // Debug: Check if the IDs exist in the dropdown options
      if (formDataToSet.customerCategoryId) {
        const foundCategory = customerCategories.find(cat => cat.value === formDataToSet.customerCategoryId);
        console.log("Customer Category lookup:", formDataToSet.customerCategoryId, "Found:", !!foundCategory);
      }
      if (formDataToSet.isicSectorId) {
        const foundSector = isicSectors.find(sector => sector.value === formDataToSet.isicSectorId);
        console.log("ISIC Sector lookup:", formDataToSet.isicSectorId, "Found:", !!foundSector);
      }
      if (formDataToSet.branchId) {
        const foundBranch = branches.find(branch => branch.value === formDataToSet.branchId);
        console.log("Branch lookup:", formDataToSet.branchId, "Found:", !!foundBranch);
      }
      if (formDataToSet.anchorId) {
        const foundAnchor = anchors.find(anchor => anchor.value === formDataToSet.anchorId);
        console.log("Anchor lookup:", formDataToSet.anchorId, "Found:", !!foundAnchor);
      }
      if (formDataToSet.anchorRelationshipId) {
        const foundRelationship = anchorRelationships.find(rel => rel.value === formDataToSet.anchorRelationshipId);
        console.log("Anchor Relationship lookup:", formDataToSet.anchorRelationshipId, "Found:", !!foundRelationship);
      }
      
      // Only set form data if dropdown options are loaded
      if (customerCategories.length > 0 || isicSectors.length > 0 || branches.length > 0 || anchors.length > 0 || anchorRelationships.length > 0) {
        console.log("Setting form data with:", formDataToSet);
        
        // Verify that the IDs exist in the dropdown options before setting
        const categoryExists = !formDataToSet.customerCategoryId || customerCategories.find(cat => cat.value === formDataToSet.customerCategoryId);
        const sectorExists = !formDataToSet.isicSectorId || isicSectors.find(sector => sector.value === formDataToSet.isicSectorId);
        const branchExists = !formDataToSet.branchId || branches.find(branch => branch.value === formDataToSet.branchId);
        
        console.log("ID verification - Category exists:", categoryExists, "Sector exists:", sectorExists, "Branch exists:", branchExists);
        
        // Only set form data if all required options are available
        if (categoryExists && sectorExists && branchExists) {
          setFormData(formDataToSet);
          setIsFormInitialized(true);
          console.log("=== FORM DATA POPULATION COMPLETE ===");
        } else {
          console.log("Some required options not found, waiting for all options to load...");
          console.log("Missing options - Category:", !categoryExists, "Sector:", !sectorExists, "Branch:", !branchExists);
        }
      } else {
        console.log("Dropdown options not loaded yet, waiting...");
        console.log("Available options - Categories:", customerCategories.length, "Sectors:", isicSectors.length, "Branches:", branches.length, "Anchors:", anchors.length, "Relationships:", anchorRelationships.length);
      }
    };

    populateFormData();
  }, [item?.id, initialCustomerCategory, initialIsicSector, initialBranch, initialAnchorRelationship, customerCategories.length, isicSectors.length, branches.length, anchors.length, anchorRelationships.length]); // Include dropdown options in dependencies

  // Reset initialization flag when item or initial props change (new edit session)
  useEffect(() => {
    console.log("Resetting form initialization flag for new edit session");
    console.log("Item changed:", item);
    console.log("Initial props changed:", { initialCustomerCategory, initialIsicSector, initialBranch, initialAnchorRelationship });
    setIsFormInitialized(false);
    
    // If switching to create mode (no item), reset form data
    if (!item) {
      console.log("Switching to create mode, resetting form data");
      setFormData({
        anchorId: "",
        anchorRelationshipId: "",
        customerName: "",
        phoneNumber: "",
        customerCategoryId: "",
        isicSectorId: "",
        branchId: "",
        contactPersonName: "",
        contactPersonPhone: "",
        employerId: "",
        employerName: "",
      });
      setErrors({});
    }
  }, [item?.id, initialCustomerCategory, initialIsicSector, initialBranch, initialAnchorRelationship]);

  // Debug form data changes
  useEffect(() => {
    if (item) {
      console.log("=== FORM DATA DEBUG (EDIT MODE) ===");
      console.log("Current formData:", formData);
      console.log("Customer Category ID:", formData.customerCategoryId, "Type:", typeof formData.customerCategoryId, "Truthy:", !!formData.customerCategoryId);
      console.log("ISIC Sector ID:", formData.isicSectorId, "Type:", typeof formData.isicSectorId, "Truthy:", !!formData.isicSectorId);
      console.log("Branch ID:", formData.branchId, "Type:", typeof formData.branchId, "Truthy:", !!formData.branchId);
      console.log("Anchor ID:", formData.anchorId, "Type:", typeof formData.anchorId, "Truthy:", !!formData.anchorId);
      console.log("Anchor Relationship ID:", formData.anchorRelationshipId, "Type:", typeof formData.anchorRelationshipId, "Truthy:", !!formData.anchorRelationshipId);
      console.log("Is Form Initialized:", isFormInitialized);
      console.log("=========================");
    }
  }, [formData, item, isFormInitialized]);

  // Debug form data changes for new loan clients
  useEffect(() => {
    if (!item) {
      console.log("=== FORM DATA DEBUG (CREATE MODE) ===");
      console.log("Current formData:", formData);
      console.log("Customer Category ID:", formData.customerCategoryId, "Type:", typeof formData.customerCategoryId, "Truthy:", !!formData.customerCategoryId);
      console.log("ISIC Sector ID:", formData.isicSectorId, "Type:", typeof formData.isicSectorId, "Truthy:", !!formData.isicSectorId);
      console.log("Branch ID:", formData.branchId, "Type:", typeof formData.branchId, "Truthy:", !!formData.branchId);
      console.log("Anchor ID:", formData.anchorId, "Type:", typeof formData.anchorId, "Truthy:", !!formData.anchorId);
      console.log("Anchor Relationship ID:", formData.anchorRelationshipId, "Type:", typeof formData.anchorRelationshipId, "Truthy:", !!formData.anchorRelationshipId);
      console.log("Is Form Initialized:", isFormInitialized);
      console.log("Form submission readiness:", {
        hasCustomerCategory: !!formData.customerCategoryId,
        hasIsicSector: !!formData.isicSectorId,
        hasBranch: !!formData.branchId,
        hasCustomerName: !!formData.customerName,
        hasPhoneNumber: !!formData.phoneNumber
      });
      console.log("=========================");
    }
  }, [formData, item, isFormInitialized]);



  // Custom styles for React Select
  const selectStyles = {
    control: (provided, state) => ({
      ...provided,
      minHeight: "48px",
      borderColor: state.isFocused
        ? "#10b981"
        : errors[state.selectProps.name]
        ? "#ef4444"
        : "#d1d5db",
      boxShadow: "none",
      "&:hover": {
        borderColor: state.isFocused ? "#10b981" : "#9ca3af",
      },
    }),
    option: (provided, state) => ({
      ...provided,
      backgroundColor: state.isSelected
        ? "#10b981"
        : state.isFocused
        ? "#f3f4f6"
        : "white",
      color: state.isSelected ? "white" : "#374151",
    }),
    menu: (provided) => ({
      ...provided,
      zIndex: 9999,
    }),
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  // Handle React Select changes
  const handleSelectChange = (selectedOption, actionMeta) => {
    const { name } = actionMeta;
    const value = selectedOption ? selectedOption.value : "";

    console.log("=== SELECT CHANGE DEBUG ===");
    console.log("Field name:", name);
    console.log("Selected option:", selectedOption);
    console.log("Value to set:", value);
    console.log("Previous form data:", formData);

    // Map field names to their corresponding ID fields in form data
    const fieldMappings = {
      anchor: "anchorId",
      anchorRelationship: "anchorRelationshipId",
      isicSector: "isicSectorId",
      customerCategory: "customerCategoryId",
      employer: "employerId",
      branch: "branchId",
    };

    const fieldName = fieldMappings[name] || name;
    console.log("Mapped field name:", fieldName);

    // Special handling for anchor relationship changes
    if (name === "anchorRelationship" && selectedOption) {
      // If relationship is "Employee", auto-fill employer name with anchor name
      if (selectedOption.label === "Employer") {
        const selectedAnchor = anchors.find(
          (anchor) => anchor.value === formData.anchorId
        );
        if (selectedAnchor) {
          setFormData((prev) => ({
            ...prev,
            [fieldName]: value,
            employerName: selectedAnchor.name || selectedAnchor.label,
          }));
        } else {
          setFormData((prev) => ({
            ...prev,
            [fieldName]: value,
          }));
        }
      } else {
        // Clear employer name if relationship is not "Employee"
        setFormData((prev) => ({
          ...prev,
          [fieldName]: value,
          employerName: "",
        }));
      }
    } else if (name === "customerCategory") {
      // Special handling for customer category changes
      const selectedCategory = selectedOption;
      const requiresContactPerson =
        selectedCategory &&
        !["Employed", "Junior and youth", "Youth"].includes(
          selectedCategory.label
        );

      // Clear contact person fields if not required for this category
      if (!requiresContactPerson) {
        setFormData((prev) => ({
          ...prev,
          [fieldName]: value,
          contactPersonName: "",
          contactPersonPhone: "",
        }));
      } else {
        setFormData((prev) => ({
          ...prev,
          [fieldName]: value,
        }));
      }
    } else {
      // Normal field update
      setFormData((prev) => ({
        ...prev,
        [fieldName]: value,
      }));
    }

    // Clear error when user makes selection
    if (errors[fieldName]) {
      setErrors((prev) => ({
        ...prev,
        [fieldName]: "",
        // Also clear contact person errors when category changes
        ...(name === "customerCategory" && {
          contactPersonName: "",
          contactPersonPhone: "",
        }),
      }));
    }

    console.log("Form data updated. New state will be:", {
      ...formData,
      [fieldName]: value
    });
    console.log("=== SELECT CHANGE DEBUG END ===");
  };

  const validateForm = () => {
    const newErrors = {};

    console.log("=== FORM VALIDATION DEBUG ===");
    console.log("Form Data:", formData);
    console.log("Form Data Types:", {
      customerName: typeof formData.customerName,
      phoneNumber: typeof formData.phoneNumber,
      customerCategoryId: typeof formData.customerCategoryId,
      isicSectorId: typeof formData.isicSectorId,
      branchId: typeof formData.branchId,
      anchorId: typeof formData.anchorId,
      anchorRelationshipId: typeof formData.anchorRelationshipId
    });
    console.log("Customer Categories:", customerCategories);
    console.log("Contact Person Name:", `"${formData.contactPersonName}"`);
    console.log("Contact Person Phone:", `"${formData.contactPersonPhone}"`);
    console.log(
      "Contact Person Phone Length:",
      formData.contactPersonPhone.length
    );

    // Required fields
    if (!formData.customerName.trim()) {
      newErrors.customerName = "Customer name is required";
    }

    if (!formData.phoneNumber.trim()) {
      newErrors.phoneNumber = "Phone number is required";
    } else if (
      !/^[\+]?[0-9\s\-\(\)]{7,15}$/.test(
        formData.phoneNumber.replace(/\s/g, "")
      )
    ) {
      newErrors.phoneNumber = "Please enter a valid phone number";
    }

    console.log("Validation - branchId:", formData.branchId, "Type:", typeof formData.branchId);
    
    if (!formData.branchId || formData.branchId === "") {
      newErrors.branchId = "Branch is required";
    }

    console.log("Validation - customerCategoryId:", formData.customerCategoryId, "Type:", typeof formData.customerCategoryId);
    console.log("Validation - isicSectorId:", formData.isicSectorId, "Type:", typeof formData.isicSectorId);
    
    if (!formData.customerCategoryId || formData.customerCategoryId === "") {
      newErrors.customerCategoryId = "Customer category is required";
    }

    if (!formData.isicSectorId || formData.isicSectorId === "") {
      newErrors.isicSectorId = "ISIC sector is required";
    }

    // Get selected category for conditional validation
    const selectedCategory = customerCategories.find(
      (cat) => cat.value === formData.customerCategoryId
    );

    console.log("Selected Category:", selectedCategory);
    console.log("Should show employer name:", shouldShowEmployerName());
    console.log("Should show contact person:", shouldShowContactPerson());

    // Employer name required for employed customers
    if (
      selectedCategory?.label === "Employed" &&
      !formData.employerName.trim()
    ) {
      newErrors.employerName =
        "Employer name is required for employed customers";
    }

    // Contact person required for all categories except Employed, Junior and youth, and Youth
    const requiresContactPerson =
      selectedCategory &&
      !["Employed", "Junior and youth", "Youth"].includes(
        selectedCategory.label
      );

    console.log("Requires Contact Person:", requiresContactPerson);
    console.log(
      "Contact Person Phone Trim:",
      `"${formData.contactPersonPhone.trim()}"`
    );

    // Only validate contact person fields if they are required for this category
    if (requiresContactPerson) {
      if (!formData.contactPersonName.trim()) {
        newErrors.contactPersonName =
          "Contact person name is required for this category";
      }

      // Contact person phone validation - only validate if phone has content
      if (formData.contactPersonPhone.trim()) {
        const phoneValue = formData.contactPersonPhone.trim();
        // Use the same validation as main phone number
        if (!/^[\+]?[0-9\s\-\(\)]{7,15}$/.test(phoneValue.replace(/\s/g, ""))) {
          newErrors.contactPersonPhone = "Please enter a valid phone number";
        }
      }
    }

    // Explicitly log what we're doing with contact person validation
    console.log("Contact person validation applied:", requiresContactPerson);
    console.log("Contact person errors:", {
      name: newErrors.contactPersonName,
      phone: newErrors.contactPersonPhone,
    });

    console.log("Validation Errors:", newErrors);
    console.log("Validation Result:", Object.keys(newErrors).length === 0);
    console.log("==============================");

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Helper functions for conditional rendering
  const shouldShowEmployerName = () => {
    const selectedCategory = customerCategories.find(
      (cat) => cat.value === formData.customerCategoryId
    );
    return selectedCategory?.label === "Employed";
  };

  const shouldShowContactPerson = () => {
    const selectedCategory = customerCategories.find(
      (cat) => cat.value === formData.customerCategoryId
    );
    return (
      selectedCategory &&
      !["Employed", "Junior and youth", "Youth"].includes(
        selectedCategory.label
      )
    );
  };

  // Reset form to initial state
  const handleReset = () => {
    setFormData({
      anchorId: "",
      anchorRelationshipId: "",
      customerName: "",
      phoneNumber: "",
      customerCategoryId: "",
      isicSectorId: "",
      branchId: "",
      contactPersonName: "",
      contactPersonPhone: "",
      employerId: "",
      employerName: "",
    });
    setErrors({});
  };

  const handleSubmit = async (e) => {
    console.log("=== FORM SUBMIT TRIGGERED ===");
    console.log("Event:", e);
    console.log("Current form data before validation:", formData);
    console.log("Is form initialized:", isFormInitialized);
    console.log("Dropdown options status:");
    console.log("  - Customer Categories:", customerCategories.length);
    console.log("  - ISIC Sectors:", isicSectors.length);
    console.log("  - Branches:", branches.length);
    console.log("  - Anchors:", anchors.length);
    console.log("  - Anchor Relationships:", anchorRelationships.length);
    e.preventDefault();

    // Check if form is properly initialized
    if (!isFormInitialized) {
      console.log("Form not initialized yet, preventing submission");
      console.log("Form initialization status:", {
        isFormInitialized,
        customerCategoriesLoaded: customerCategories.length > 0,
        branchesLoaded: branches.length > 0,
        isicSectorsLoaded: isicSectors.length > 0
      });
      return;
    }

    // Check if required fields are populated
    if (!formData.customerCategoryId || !formData.isicSectorId || !formData.branchId) {
      console.log("Required fields not populated yet:");
      console.log("Customer Category ID:", formData.customerCategoryId, "Type:", typeof formData.customerCategoryId);
      console.log("ISIC Sector ID:", formData.isicSectorId, "Type:", typeof formData.isicSectorId);
      console.log("Branch ID:", formData.branchId, "Type:", typeof formData.branchId);
      console.log("Form data state:", formData);
      console.log("Waiting for form data to be properly populated...");
      return;
    }

    // Additional validation for create mode
    if (!item) {
      if (!formData.customerName || !formData.phoneNumber) {
        console.log("Create mode validation failed:");
        console.log("Customer Name:", formData.customerName, "Type:", typeof formData.customerName);
        console.log("Phone Number:", formData.phoneNumber, "Type:", typeof formData.phoneNumber);
        console.log("Form data state:", formData);
        return;
      }
    }

    console.log("Running form validation...");
    const isValid = validateForm();
    console.log("Form validation result:", isValid);

    if (!isValid) {
      console.log("Form validation failed, stopping submission");
      return;
    }

    console.log("Form is valid, proceeding with submission...");
    setIsSubmitting(true);

    try {
      // Prepare data for API
      const baseLoanClientData = {
        branchId: formData.branchId,
        contactPersonName: formData.contactPersonName,
        contactPersonPhone: formData.contactPersonPhone,
        customerCategoryId: formData.customerCategoryId,
        customerName: formData.customerName,
        employerId: formData.employerId,
        employerName: formData.employerName,
        isicSectorId: formData.isicSectorId,
        phoneNumber: formData.phoneNumber,
      };

      // Only include anchor fields if they have values
      if (formData.anchorId && formData.anchorId.trim()) {
        baseLoanClientData.anchorId = formData.anchorId;
      }

      if (
        formData.anchorRelationshipId &&
        formData.anchorRelationshipId.trim()
      ) {
        baseLoanClientData.anchorRelationshipId = formData.anchorRelationshipId;
      }

      // For create mode, parent component will handle adding createdDate
      const loanClientData = baseLoanClientData;

      console.log("=== FORM SUBMISSION DEBUG ===");
      console.log("Form Data State:", formData);
      console.log("Base Loan Client Data:", baseLoanClientData);
      console.log("Final Loan Client Data being submitted:", loanClientData);
      console.log("Is Edit Mode:", !!item);
      console.log("==============================");

      if (item) {
        // Edit mode - pass raw form data to parent component
        const updateData = {
          ...loanClientData,
          // id: item.id
        };

        console.log("Passing form data to parent component for update:", updateData);
        console.log("Form data structure check for edit:", {
          hasCustomerName: !!updateData.customerName,
          hasPhoneNumber: !!updateData.phoneNumber,
          hasCustomerCategoryId: !!updateData.customerCategoryId,
          hasIsicSectorId: !!updateData.isicSectorId,
          hasBranchId: !!updateData.branchId,
          hasAnchorId: !!updateData.anchorId,
          hasAnchorRelationshipId: !!updateData.anchorRelationshipId
        });
        const result = await onSubmit(updateData, item);
        console.log("Parent component onSubmit result for edit:", result);
      } else {
        // Create mode - pass raw form data to parent component
        console.log("Passing form data to parent component for creation:", loanClientData);
        console.log("Form data structure check:", {
          hasCustomerName: !!loanClientData.customerName,
          hasPhoneNumber: !!loanClientData.phoneNumber,
          hasCustomerCategoryId: !!loanClientData.customerCategoryId,
          hasIsicSectorId: !!loanClientData.isicSectorId,
          hasBranchId: !!loanClientData.branchId,
          hasAnchorId: !!loanClientData.anchorId,
          hasAnchorRelationshipId: !!loanClientData.anchorRelationshipId
        });
        const result = await onSubmit(loanClientData);
        console.log("Parent component onSubmit result:", result);
      }

      onClose();
    } catch (error) {
      console.error("Error submitting form:", error);
      // Handle error (show notification, etc.)
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-h-[80vh] overflow-y-auto max-w-4xl mx-auto">
      <form onSubmit={handleSubmit} className="space-y-8 p-8">
        {/* Section 1: Anchor Information */}
        <div className="space-y-6">


          {/* Anchor Name and Customer's Role with Anchor - Side by Side */}
          <div className="grid grid-cols-1 md:grid-cols-1 gap-6">
            {/* Anchor Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Anchor Name
              </label>
              <Select
                name="anchor"
                value={anchors.find(
                  (option) => option.value === formData.anchorId
                )}
                onChange={handleSelectChange}
                options={anchors}
                styles={selectStyles}
                placeholder={
                  anchorsLoading ? "Loading anchors..." : "Select anchor lead"
                }
                isSearchable
                isLoading={anchorsLoading}
                isDisabled={anchorsLoading}
                className="react-select-container"
                classNamePrefix="react-select"
              />
              {console.log("Anchor Select - formData.anchorId:", formData.anchorId)}
              {console.log("Anchor Select - found option:", anchors.find(
                (option) => option.value === formData.anchorId
              ))}
              {errors.anchorId && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.anchorId}
                </p>
              )}
            </div>

            {/* Customer's Role with Anchor */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                 Customer Relationship with Anchor
              </label>
              <Select
                name="anchorRelationship"
                value={anchorRelationships.find(
                  (option) => option.value === formData.anchorRelationshipId
                )}
                onChange={handleSelectChange}
                options={anchorRelationships}
                styles={selectStyles}
                placeholder={
                  anchorRelationshipsLoading
                    ? "Loading relationships..."
                    : "Select relationship"
                }
                isSearchable
                isLoading={anchorRelationshipsLoading}
                isDisabled={anchorRelationshipsLoading}
                className="react-select-container"
                classNamePrefix="react-select"
              />
              {console.log("Anchor Relationship Select - formData.anchorRelationshipId:", formData.anchorRelationshipId)}
              {console.log("Anchor Relationship Select - found option:", anchorRelationships.find(
                (option) => option.value === formData.anchorRelationshipId
              ))}
              {errors.anchorRelationshipId && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.anchorRelationshipId}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Section 2: Customer Information */}
        <div className="space-y-6">


          {/* Customer Name and Phone Number - Side by Side */}
          <div className="grid grid-cols-1 md:grid-cols-1 gap-6">
            {/* Customer Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Customer Name *
              </label>
              <input
                type="text"
                name="customerName"
                value={formData.customerName}
                onChange={handleChange}
                className={`w-full px-4 py-3 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 outline-none transition-colors duration-200 ${
                  errors.customerName
                    ? "border-red-500 dark:border-red-400"
                    : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
                }`}
                placeholder="Enter customer full name"
              />
              {errors.customerName && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.customerName}
                </p>
              )}
            </div>

            {/* Phone Number */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Phone Number *
              </label>
              <input
                type="tel"
                name="phoneNumber"
                value={formData.phoneNumber}
                onChange={handleChange}
                className={`w-full px-4 py-3 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 outline-none transition-colors duration-200 ${
                  errors.phoneNumber
                    ? "border-red-500 dark:border-red-400"
                    : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
                }`}
                placeholder="e.g., +254712345678 or 0712345678"
              />
              {errors.phoneNumber && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.phoneNumber}
                </p>
              )}
            </div>
          </div>

          {/* Customer Category */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Customer Category *
            </label>
            <Select
              name="customerCategory"
              value={customerCategories.find(
                (option) => option.value === formData.customerCategoryId
              )}
              onChange={handleSelectChange}
              options={customerCategories}
              styles={selectStyles}
              placeholder={
                customerCategoriesLoading
                  ? "Loading categories..."
                  : formData.customerCategoryId && !customerCategories.find(option => option.value === formData.customerCategoryId)
                  ? "Loading selected category..."
                  : "Select customer category"
              }
              isSearchable
              isLoading={customerCategoriesLoading}
              isDisabled={customerCategoriesLoading}
              className="react-select-container"
              classNamePrefix="react-select"
            />
            {console.log("Customer Category Select - formData.customerCategoryId:", formData.customerCategoryId, "Type:", typeof formData.customerCategoryId)}
            {console.log("Customer Category Select - found option:", customerCategories.find(
              (option) => option.value === formData.customerCategoryId
            ))}
            {console.log("Customer Category Select - all options:", customerCategories.slice(0, 3))}
            {errors.customerCategoryId && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {errors.customerCategoryId}
              </p>
            )}
          </div>

          {/* ISIC Sector */}
          <div>
            <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              ISIC Sector *
              <div className="group relative ml-2">
                <Info size={16} className="text-gray-400 cursor-help" />
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                  International Standard Industrial Classification
                </div>
              </div>
            </label>
            <Select
              name="isicSector"
              value={isicSectors.find(
                (option) => option.value === formData.isicSectorId
              )}
              onChange={handleSelectChange}
              options={isicSectors}
              styles={selectStyles}
              placeholder={
                isicSectorsLoading
                  ? "Loading sectors..."
                  : formData.isicSectorId && !isicSectors.find(option => option.value === formData.isicSectorId)
                  ? "Loading selected sector..."
                  : "Select industry sector"
              }
              isSearchable
              isLoading={isicSectorsLoading}
              isDisabled={isicSectorsLoading}
              className="react-select-container"
              classNamePrefix="react-select"
            />
            {console.log("ISIC Sector Select - formData.isicSectorId:", formData.isicSectorId, "Type:", typeof formData.isicSectorId)}
            {console.log("ISIC Sector Select - found option:", isicSectors.find(
              (option) => option.value === formData.isicSectorId
            ))}
            {console.log("ISIC Sector Select - all options:", isicSectors.slice(0, 3))}
            {errors.isicSectorId && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {errors.isicSectorId}
              </p>
            )}
          </div>
        </div>

        {/* Section 2: Branch & Relationship Assignment */}
        <div className="space-y-6">


          {/* Branch Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Branch Name *
            </label>
            <Select
              name="branch"
              value={branches.find(
                (option) => option.value === formData.branchId
              )}
              onChange={handleSelectChange}
              options={branches}
              styles={selectStyles}
              placeholder={
                branchesLoading ? "Loading branches..." : "Select a branch"
              }
              isSearchable
              isLoading={branchesLoading}
              isDisabled={branchesLoading}
              className="react-select-container"
              classNamePrefix="react-select"
            />
            {console.log("Branch Select - formData.branchId:", formData.branchId)}
            {console.log("Branch Select - found option:", branches.find(
              (option) => option.value === formData.branchId
            ))}
            {errors.branchId && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {errors.branchId}
              </p>
            )}
          </div>
        </div>

        {/* Section 3: Contact Person Details - Conditional */}
        {shouldShowContactPerson() && (
          <div className="space-y-6">


            {/* Contact Person Name and Phone - Side by Side */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Contact Person Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Contact Person Name *
                </label>
                <input
                  type="text"
                  name="contactPersonName"
                  value={formData.contactPersonName}
                  onChange={handleChange}
                  className={`w-full px-4 py-3 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 outline-none transition-colors duration-200 ${
                    errors.contactPersonName
                      ? "border-red-500 dark:border-red-400"
                      : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
                  }`}
                  placeholder="Enter contact person full name"
                />
                {errors.contactPersonName && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {errors.contactPersonName}
                  </p>
                )}
              </div>

              {/* Contact Person Phone */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Contact Person Phone No
                </label>
                <input
                  type="tel"
                  name="contactPersonPhone"
                  value={formData.contactPersonPhone}
                  onChange={handleChange}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 outline-none transition-colors duration-200 focus:border-green-500 hover:border-gray-400"
                  placeholder="e.g., +254712345678 or 0712345678"
                />
              </div>
            </div>
          </div>
        )}

        {/* Section 4: Employer Information - Conditional for Employed Category */}
        {shouldShowEmployerName() && (
          <div className="space-y-6">


            {/* Employer Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Employer Name *
              </label>
              <input
                type="text"
                name="employerName"
                value={formData.employerName}
                onChange={handleChange}
                className={`w-full px-4 py-3 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 outline-none transition-colors duration-200 ${
                  errors.employerName
                    ? "border-red-500 dark:border-red-400"
                    : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
                }`}
                placeholder="Enter employer name"
              />
              {errors.employerName && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.employerName}
                </p>
              )}
            </div>
          </div>
        )}

        {/* Form Actions */}
        <div className="flex justify-between items-center pt-8 border-t border-gray-200 dark:border-gray-600">
          <button
            type="button"
            onClick={handleReset}
            className="px-6 py-3 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-200"
          >
            Reset
          </button>

          <div className="flex space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-6 py-3 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-200"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-6 py-3 text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:bg-green-400 disabled:cursor-not-allowed rounded-lg transition-colors duration-200 flex items-center"
            >
              {isSubmitting && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              )}
              {isSubmitting
                ? "Processing..."
                : item
                ? "Update Customer"
                : "Create Customer"}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default LoanClientForm;
