import { useState, useEffect } from "react";
import Modal from "../common/Modal";
import Select from "react-select";
import { usersService } from "../../services/userService";

const ReassignModal = ({ isOpen, onClose, lead, onReassign, loading }) => {
  const [selectedUser, setSelectedUser] = useState(null);
  const [users, setUsers] = useState([]);
  const [error, setError] = useState("");
  const [loadingUsers, setLoadingUsers] = useState(false);

  useEffect(() => {
    if (isOpen) {
      fetchUsers();
    }
  }, [isOpen]);

  const fetchUsers = async () => {
    try {
      setLoadingUsers(true);
      const usersData = await usersService.getAll();
      setUsers(usersData);
    } catch (err) {
      console.error("Error fetching users:", err);
      setError("Failed to load users");
    } finally {
      setLoadingUsers(false);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!selectedUser) {
      setError("Please select a user");
      return;
    }
    
    onReassign(lead.id, selectedUser.id);
    setSelectedUser(null);
    setError("");
  };

  const handleClose = () => {
    setSelectedUser(null);
    setError("");
    onClose();
  };

  const getModalTitle = () => {
    if (lead && lead.name) {
      return `Reassign ${lead.name}`;
    }
    return "Reassign Lead";
  };

  // Format users for React Select options
  const userOptions = users.map(user => ({
    value: user.id,
    label: `${user.name} (${user.rm_code})`,
    ...user
  }));

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title={getModalTitle()} size="sm">
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="userSelect" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Select User
          </label>
          <Select
            id="userSelect"
            value={selectedUser}
            onChange={(selectedOption) => {
              setSelectedUser(selectedOption);
              if (error) setError("");
            }}
            options={userOptions}
            isLoading={loadingUsers}
            placeholder="Select a user..."
            isClearable
            className="react-select-container"
            classNamePrefix="react-select"
          />
          {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
        </div>
        
        <div className="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            onClick={handleClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 dark:bg-gray-600 dark:text-white dark:border-gray-500 dark:hover:bg-gray-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading || loadingUsers}
            className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
          >
            {loading ? "Reassigning..." : "Reassign"}
          </button>
        </div>
      </form>
    </Modal>
  );
};

export default ReassignModal;