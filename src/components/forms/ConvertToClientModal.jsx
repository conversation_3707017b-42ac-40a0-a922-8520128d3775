import { useState } from "react";
import { X, User, CreditCard } from "lucide-react";
import instance from "../../axios/instance";
import { toast } from "react-toastify";

const ConvertToClientModal = ({ isOpen, onClose, lead, onSuccess }) => {
  const [formData, setFormData] = useState({
    accountNumber: "",
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  if (!isOpen) return null;

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    if (!formData.accountNumber.trim()) {
      newErrors.accountNumber = "Account number is required";
    } else if (formData.accountNumber.length > 13) {
      newErrors.accountNumber = "Account number must not exceed 13 characters";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      console.log(
        `Converting lead ${lead.id} to client with account number: ${formData.accountNumber}`
      );

      // Call success callback immediately for optimistic update
      onSuccess?.(lead.id, formData.accountNumber, {});

      // Reset form and close modal immediately for better UX
      setFormData({ accountNumber: "" });
      onClose();

      // Make the API call in the background
      const response = await instance.post(`/leads/${lead.id}/convert`, {
        account_number: formData.accountNumber,
      });

      console.log("Convert to client response:", response.data);
      console.log("Lead successfully converted to client!");

      // Show success toast message
      toast.success(`Lead "${lead.name || lead.lead_name}" successfully converted to client with account number: ${formData.accountNumber}`);

    } catch (error) {
      console.error("Error converting lead to client:", error);

      // Since we already closed the modal, show error as toast
      let errorMessage = "Failed to convert lead to client. Please try again.";

      if (error.response) {
        const { status, data } = error.response;

        switch (status) {
          case 400:
            errorMessage = data?.message || "Invalid account number format";
            break;
          case 404:
            errorMessage = "Lead not found. Please refresh the page and try again.";
            break;
          case 409:
            errorMessage = "Account number already exists. Please use a different account number.";
            break;
          default:
            errorMessage = data?.message || errorMessage;
        }
      } else {
        errorMessage = "Network error. Please check your connection and try again.";
      }

      toast.error(errorMessage);

      // TODO: Implement rollback mechanism if needed
      // For now, user can refresh the page if there's an issue
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setFormData({ accountNumber: "" });
      setErrors({});
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 bg-[rgba(0,0,0,0.7)] flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <div>
              {/* <h2
                className="text-lg font-semibold"
                style={{ color: "#3d4465" }}
              >
                Convert to Client
              </h2> */}
              <h2 className="text-lg" style={{ color: "#7e7e7e" }}>
                Convert <span className="font-bold">{lead?.customer_name ||
                  lead?.lead_name ||
                  lead?.name ||
                  "Unknown Lead"}</span>  lead to a Client
              </h2>
            </div>
          </div>
          <button
            onClick={handleClose}
            disabled={isSubmitting}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50"
          >
            <X size={20} style={{ color: "#7e7e7e" }} />
          </button>
        </div>

        {/* Lead Information */}
        {/* <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <User size={16} className="text-blue-600" />
            </div>
            <div>
              <p className="font-medium" style={{ color: "#3d4465" }}>
                {lead?.customer_name ||
                  lead?.lead_name ||
                  lead?.name ||
                  "Unknown Lead"}
              </p>
              <p className="text-sm" style={{ color: "#7e7e7e" }}>
                {lead?.phone_number || lead?.phoneNumber || "No phone number"}
              </p>
            </div>
          </div>
        </div> */}

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6">
          <div className="space-y-4">
            {/* Account Number */}
            <div>
              <label
                htmlFor="accountNumber"
                className="block text-sm font-medium mb-2"
                style={{ color: "#7e7e7e" }}
              >
                Account Number *
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <CreditCard size={16} style={{ color: "#7e7e7e" }} />
                </div>
                <input
                  type="text"
                  id="accountNumber"
                  name="accountNumber"
                  value={formData.accountNumber}
                  onChange={handleChange}
                  placeholder="Enter account number"
                  maxLength={13}
                  disabled={isSubmitting}
                  className={`w-full pl-10 pr-4 py-3 border rounded-lg outline-0 disabled:opacity-50 disabled:cursor-not-allowed ${
                    errors.accountNumber ? "border-red-500" : "border-gray-300"
                  }`}
                />
              </div>
              {errors.accountNumber && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.accountNumber}
                </p>
              )}
              <p className="mt-1 text-xs text-gray-500">
                {formData.accountNumber.length}/13 characters
              </p>
            </div>
          </div>

          {/* Buttons */}
          <div className="flex gap-3 mt-6">
            <button
              type="button"
              onClick={handleClose}
              disabled={isSubmitting}
              className="flex-1 px-4 py-3 text-sm font-medium rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              style={{ color: "#7e7e7e" }}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="flex-1 px-4 py-3 text-sm font-medium text-white rounded-lg bg-green-600 hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
            >
              {isSubmitting ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  Converting...
                </>
              ) : (
                <>
                  <User size={16} />
                  Convert to Client
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ConvertToClientModal;
