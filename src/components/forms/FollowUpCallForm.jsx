import { useState, useEffect } from "react";
import {
  Phone,
  PhoneMissed,
  Mic,
  MicOff,
  Pause,
  Play,
  Upload,
  X,
} from "lucide-react";
import Select from "react-select";
import {
  purposesService,
  formatPurposesForTable,
} from "../../services/purposesService";
import { followUpsService } from "../../services/followUpsService";
import LeadStatusChanger from "../common/LeadStatusChanger";
import { toast } from "react-toastify";

const FollowUpCallForm = ({ item, onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    callStatus: "",
    purposeId: "",
    notes: "",
    attachments: [],
  });

  const [purposes, setPurposes] = useState([]);
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showLeadStatusChanger, setShowLeadStatusChanger] = useState(false);

  // Call state management
  const [isCallActive, setIsCallActive] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [callTimer, setCallTimer] = useState(0);
  const [isMuted, setIsMuted] = useState(false);
  const [isOnHold, setIsOnHold] = useState(false);

  // Fetch purposes for the dropdown
  const fetchPurposes = async () => {
    try {
      const response = await purposesService.getAll();
      const formattedPurposes = formatPurposesForTable(response);
      setPurposes(formattedPurposes);
    } catch (error) {
      console.error("Error fetching purposes:", error);
      toast.error("Failed to load purposes");
    }
  };

  useEffect(() => {
    fetchPurposes();
  }, []);

  // Timer effect for call duration
  useEffect(() => {
    let interval;
    if (isCallActive && !isOnHold) {
      interval = setInterval(() => {
        setCallTimer((prev) => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isCallActive, isOnHold]);

  // Call functionality handlers
  const handleStartCall = async () => {
    setIsConnecting(true);

    try {
      // Simulate API call with 2 second delay
      await new Promise((resolve) => setTimeout(resolve, 2000));

      setIsCallActive(true);
      setCallTimer(0);
      setIsMuted(false);
      setIsOnHold(false);
    } catch (error) {
      console.error("Failed to initiate call:", error);
    } finally {
      setIsConnecting(false);
    }
  };

  const handleEndCall = async () => {
    try {
      setIsCallActive(false);
      setIsConnecting(false);
      setCallTimer(0);
      setIsMuted(false);
      setIsOnHold(false);
    } catch (error) {
      console.error("Failed to end call:", error);
    }
  };

  const handleToggleMute = () => {
    setIsMuted(!isMuted);
  };

  const handleToggleHold = () => {
    setIsOnHold(!isOnHold);
  };

  // Format timer display
  const formatTimer = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  useEffect(() => {
    if (item) {
      setFormData({
        callStatus: item.callStatus || "",
        purposeId: item.purposeId || "",
        notes: item.notes || "",
        attachments: item.attachments || [],
      });
    }
  }, [item]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  const handlePurposeChange = (selectedOption) => {
    setFormData((prev) => ({
      ...prev,
      purposeId: selectedOption ? selectedOption.value : "",
    }));
    if (errors.purposeId) {
      setErrors((prev) => ({ ...prev, purposeId: "" }));
    }
  };

  const handleFileUpload = (e) => {
    const files = Array.from(e.target.files);
    const newAttachments = files.map((file) => ({
      id: Date.now() + Math.random(),
      file,
      name: file.name,
      size: file.size,
    }));

    setFormData((prev) => ({
      ...prev,
      attachments: [...prev.attachments, ...newAttachments],
    }));
  };

  const removeAttachment = (attachmentId) => {
    setFormData((prev) => ({
      ...prev,
      attachments: prev.attachments.filter((att) => att.id !== attachmentId),
    }));
  };

  const validateForm = () => {
    const newErrors = {};
    if (!formData.callStatus) newErrors.callStatus = "Call status is required";
    if (!formData.purposeId) newErrors.purposeId = "Purpose is required";
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Create FormData for file uploads
      const formDataToSend = new FormData();

      // Add form fields
      formDataToSend.append("call_status", formData.callStatus);
      formDataToSend.append("purpose_id", formData.purposeId);
      formDataToSend.append("notes", formData.notes);

      // Add actual files to FormData
      formData.attachments.forEach((attachment) => {
        formDataToSend.append(`attachments`, attachment.file);
      });

      console.log("=== SUBMITTING FOLLOW-UP CALL ===");
      console.log("Follow-up ID:", item?.id);
      console.log("FormData contents:", Object.fromEntries(formDataToSend));

      // Call the follow-up service
      const response = await followUpsService.makeVisitOrCall(
        item?.id,
        formDataToSend
      );

      console.log("Follow-up call response:", response);

      // Call the onSubmit callback with success
      onSubmit?.(response, item);
      onClose();
      toast.success("Call recorded successfully!");
    } catch (error) {
      console.error("Error submitting follow-up call:", error);
      toast.error(error.response?.data?.message || "Failed to record call");
      onSubmit?.(null, item, error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Format purposes for react-select
  const purposeOptions = purposes.map((purpose) => ({
    value: purpose.id,
    label: purpose.name,
  }));

  const selectedPurpose = purposeOptions.find(
    (option) => option.value === formData.purposeId
  );

  return (
    <div className="flex flex-col h-[80vh]">
      <form onSubmit={handleSubmit} className="flex flex-col h-full">
        {/* Scrollable Content */}
        <div className="flex-1 overflow-y-auto overflow-x-hidden scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 px-1">
          <div className="flex flex-col gap-6 pb-4">
            {/* Call Section - Always show at the top */}
            <div className="border-b border-gray-200 dark:border-gray-600 min-h-[100px] flex items-center">
              {!isCallActive ? (
                // Call Button - Aligned to left
                <div className="flex justify-start w-full">
                  <button
                    type="button"
                    onClick={handleStartCall}
                    disabled={isConnecting}
                    className="inline-flex items-center px-6 py-3 bg-green-500 hover:bg-green-600 disabled:bg-green-400 text-white font-medium rounded-lg transition-colors duration-200 shadow-sm disabled:cursor-not-allowed"
                  >
                    <Phone size={20} className="mr-2" />
                    {isConnecting
                      ? "Calling..."
                      : `Call ${item?.customer_name || "Customer"}`}
                  </button>
                </div>
              ) : (
                // Call Controls
                <div className="flex justify-between items-center w-full">
                  {/* Customer Info */}
                  <div className="flex flex-col">
                    <span className="text-sm text-gray-500">Calling</span>
                    <span className="font-medium text-gray-900">
                      {item?.customer_name || "Customer"}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    {/* Timer */}
                    <div
                      className="text-lg font-mono"
                      style={{ color: "#7e7e7e" }}
                    >
                      {formatTimer(callTimer)}
                    </div>

                    {/* Call Control Buttons */}
                    <div className="flex items-center space-x-3 ml-6">
                      {/* Mute Button */}
                      <button
                        type="button"
                        onClick={handleToggleMute}
                        className={`inline-flex items-center px-4 py-2 font-medium rounded-lg transition-colors duration-200 ${
                          isMuted
                            ? "bg-red-500 hover:bg-red-600 text-white"
                            : "bg-gray-200 hover:bg-gray-300 text-gray-700"
                        }`}
                      >
                        {isMuted ? (
                          <MicOff size={16} className="mr-1" />
                        ) : (
                          <Mic size={16} className="mr-1" />
                        )}
                        <span>{isMuted ? "Unmute" : "Mute"}</span>
                      </button>

                      {/* Hold Button */}
                      <button
                        type="button"
                        onClick={handleToggleHold}
                        className={`inline-flex items-center px-4 py-2 font-medium rounded-lg transition-colors duration-200 ${
                          isOnHold
                            ? "bg-yellow-500 hover:bg-yellow-600 text-white"
                            : "bg-gray-200 hover:bg-gray-300 text-gray-700"
                        }`}
                      >
                        {isOnHold ? (
                          <Play size={16} className="mr-1" />
                        ) : (
                          <Pause size={16} className="mr-1" />
                        )}
                        <span>{isOnHold ? "Resume" : "Hold"}</span>
                      </button>

                      {/* End Call Button */}
                      <button
                        type="button"
                        onClick={handleEndCall}
                        className="inline-flex items-center px-6 py-3 font-medium rounded-lg transition-colors duration-200"
                        style={{ backgroundColor: "#f46b68" }}
                        onMouseEnter={(e) =>
                          (e.target.style.backgroundColor = "#e55a57")
                        }
                        onMouseLeave={(e) =>
                          (e.target.style.backgroundColor = "#f46b68")
                        }
                      >
                        <PhoneMissed size={16} className="mr-1 text-white" />
                        <span className="text-white">End</span>
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Call Status */}
            <div className="flex items-center gap-4">
              <label
                className="text-sm font-medium w-32 flex-shrink-0"
                style={{ color: "#7e7e7e" }}
              >
                Call Status *
              </label>
              <div className="flex-1">
                <select
                  name="callStatus"
                  value={formData.callStatus}
                  onChange={handleChange}
                  className={`w-full px-4 py-3 border rounded-lg bg-white outline-none transition-colors duration-200 ${
                    errors.callStatus
                      ? "border-red-500"
                      : "border-gray-300 focus:border-green-500 hover:border-gray-400"
                  }`}
                  style={{ color: "#7e7e7e" }}
                >
                  <option value="">--select--</option>
                  <option value="Success">Success</option>
                  <option value="No answer">No answer</option>
                  <option value="Busy">Busy</option>
                  <option value="Wrong number">Wrong number</option>
                  <option value="Call back later">Call back later</option>
                </select>
                {errors.callStatus && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.callStatus}
                  </p>
                )}
              </div>
            </div>

            {/* Purpose */}
            <div className="flex items-center gap-4">
              <label
                className="text-sm font-medium w-32 flex-shrink-0"
                style={{ color: "#7e7e7e" }}
              >
                Purpose *
              </label>
              <div className="flex-1">
                <Select
                  value={selectedPurpose}
                  onChange={handlePurposeChange}
                  options={purposeOptions}
                  placeholder="--select--"
                  isClearable
                  className={`react-select-container ${
                    errors.purposeId ? "react-select-error" : ""
                  }`}
                  classNamePrefix="react-select"
                  styles={{
                    control: (provided, state) => ({
                      ...provided,
                      borderColor: errors.purposeId
                        ? "#ef4444"
                        : state.isFocused
                        ? "#10b981"
                        : "#d1d5db",
                      "&:hover": {
                        borderColor: errors.purposeId
                          ? "#ef4444"
                          : state.isFocused
                          ? "#10b981"
                          : "#9ca3af",
                      },
                      boxShadow: "none",
                      minHeight: "48px",
                    }),
                    placeholder: (provided) => ({
                      ...provided,
                      color: "#7e7e7e",
                    }),
                    singleValue: (provided) => ({
                      ...provided,
                      color: "#7e7e7e",
                    }),
                  }}
                />
                {errors.purposeId && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.purposeId}
                  </p>
                )}
              </div>
            </div>

            {/* Notes */}
            <div className="flex items-start gap-4">
              <label
                className="text-sm font-medium w-32 flex-shrink-0 pt-3"
                style={{ color: "#7e7e7e" }}
              >
                Notes
              </label>
              <div className="flex-1">
                <textarea
                  name="notes"
                  value={formData.notes}
                  onChange={handleChange}
                  rows={4}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-white outline-none transition-colors duration-200 focus:border-green-500 hover:border-gray-400 resize-none"
                  style={{ color: "#7e7e7e" }}
                  placeholder="Enter any additional notes..."
                />
              </div>
            </div>

            {/* Attachments */}
            <div className="flex items-start gap-4">
              <label
                className="text-sm font-medium w-32 flex-shrink-0 pt-3"
                style={{ color: "#7e7e7e" }}
              >
                Attachments
              </label>
              <div className="flex-1">
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 hover:border-gray-400 transition-colors duration-200">
                  <input
                    type="file"
                    multiple
                    onChange={handleFileUpload}
                    className="hidden"
                    id="file-upload"
                    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif"
                  />
                  <label
                    htmlFor="file-upload"
                    className="flex flex-col items-center cursor-pointer"
                  >
                    <Upload size={24} className="text-gray-400 mb-2" />
                    <span className="text-sm text-gray-600">
                      Click to upload files
                    </span>
                    <span className="text-xs text-gray-400 mt-1">
                      PDF, DOC, DOCX, JPG, PNG, GIF (Max 10MB each)
                    </span>
                  </label>
                </div>

                {/* Display uploaded files */}
                {formData.attachments.length > 0 && (
                  <div className="mt-3 space-y-2">
                    {formData.attachments.map((attachment) => (
                      <div
                        key={attachment.id}
                        className="flex items-center justify-between bg-gray-50 p-2 rounded border"
                      >
                        <span className="text-sm text-gray-700 truncate">
                          {attachment.name}
                        </span>
                        <button
                          type="button"
                          onClick={() => removeAttachment(attachment.id)}
                          className="text-red-500 hover:text-red-700 ml-2"
                        >
                          <X size={16} />
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Lead Status Update Section */}
            {item && item.lead_id && (
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-800">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <h3 className="text-sm font-medium text-blue-900 dark:text-blue-100">
                        Update Lead Status
                      </h3>
                    </div>
                  </div>
                  <div className="flex-shrink-0 ml-6">
                    <LeadStatusChanger
                      currentStatus={item.lead_status || "Pending"}
                      leadId={item.lead_id}
                      leadName={item.customer_name || "Lead"}
                      onStatusChange={(leadId, newStatus) => {
                        console.log(
                          `Lead ${leadId} status updated to ${newStatus}`
                        );
                        toast.success(`Lead status updated to ${newStatus}`);
                      }}
                      size="md"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Fixed Footer */}
        <div className="border-t bg-white p-4 flex justify-end gap-3">
          <button
            type="button"
            onClick={onClose}
            disabled={isSubmitting}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Recording...
              </>
            ) : (
              "Record Call"
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default FollowUpCallForm;
