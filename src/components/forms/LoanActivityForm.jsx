import { useState, useEffect } from "react";
import { X, Upload, Loader2, User, DollarSign, Calendar, FileText } from "lucide-react";
import Select from "react-select";
import { loanActivitiesService } from "../../services/loanActivitiesService";
import { purposesService } from "../../services/purposesService";
import { usersService } from "../../services/userService";
import { loanService } from "../../services/loanService";
import { validateLoanActivityData, validateFileUpload, sanitizeString } from "../../utils/validationUtils";

const LoanActivityForm = ({ item, onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    rmUserId: "",
    loanClientId: "",
    loanAccountNumber: "",
    purposeId: "",
    loanBalance: "",
    arrearsDays: "",
    comment: "",
    viaApi: false,
    apiCallReference: "",
    interactionType: "",
    callStatus: "",
    visitStatus: "",
    callDurationMinutes: "",
    nextFollowupDate: "",
    followupStatus: "",
    attachments: [],
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Dropdown options
  const [rmUsers, setRmUsers] = useState([]);
  const [loanClients, setLoanClients] = useState([]);
  const [purposes, setPurposes] = useState([]);

  // Predefined options
  const interactionTypeOptions = [
    { value: "call", label: "Call" },
    { value: "visit", label: "Visit" },
    { value: "email", label: "Email" },
    { value: "sms", label: "SMS" },
  ];

  const callStatusOptions = [
    { value: "answered", label: "Answered" },
    { value: "missed", label: "Missed" },
    { value: "busy", label: "Busy" },
    { value: "no_answer", label: "No Answer" },
    { value: "voicemail", label: "Voicemail" },
  ];

  const visitStatusOptions = [
    { value: "completed", label: "Completed" },
    { value: "scheduled", label: "Scheduled" },
    { value: "cancelled", label: "Cancelled" },
    { value: "rescheduled", label: "Rescheduled" },
    { value: "no_show", label: "No Show" },
  ];

  const followupStatusOptions = [
    { value: "pending", label: "Pending" },
    { value: "completed", label: "Completed" },
    { value: "cancelled", label: "Cancelled" },
    { value: "overdue", label: "Overdue" },
  ];

  // Load dropdown data on component mount
  useEffect(() => {
    const loadDropdownData = async () => {
      setIsLoading(true);
      try {
        const [usersData, clientsData, purposesData] = await Promise.all([
          usersService.getAll(),
          loanService.clients.getExistingLoanClients(),
          purposesService.getAll(),
        ]);

        // Format RM users for dropdown
        const formattedUsers = usersData.map(user => ({
          value: user.id,
          label: `${user.name} (${user.rm_code || 'No Code'})`,
          ...user
        }));
        setRmUsers(formattedUsers);

        // Loan clients are already formatted
        setLoanClients(clientsData);

        // Format purposes for dropdown
        const formattedPurposes = purposesData.data?.map(purpose => ({
          value: purpose.id,
          label: purpose.name,
          ...purpose
        })) || [];
        setPurposes(formattedPurposes);

      } catch (error) {
        console.error('Error loading dropdown data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadDropdownData();
  }, []);

  // Populate form data if editing
  useEffect(() => {
    if (item) {
      setFormData({
        rmUserId: item.rmUserId || "",
        loanClientId: item.loanClientId || "",
        loanAccountNumber: item.loanAccountNumber || "",
        purposeId: item.purposeId || "",
        loanBalance: item.loanBalance || "",
        arrearsDays: item.arrearsDays || "",
        comment: item.comment || "",
        viaApi: item.viaApi || false,
        apiCallReference: item.apiCallReference || "",
        interactionType: item.interactionType || "",
        callStatus: item.callStatus || "",
        visitStatus: item.visitStatus || "",
        callDurationMinutes: item.callDurationMinutes || "",
        nextFollowupDate: item.nextFollowupDate || "",
        followupStatus: item.followupStatus || "",
        attachments: [],
      });
    }
  }, [item]);

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    const newValue = type === 'checkbox' ? checked : value;

    setFormData(prev => ({
      ...prev,
      [name]: newValue
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ""
      }));
    }
  };

  // Handle React Select changes
  const handleSelectChange = (selectedOption, actionMeta) => {
    const { name } = actionMeta;
    const value = selectedOption ? selectedOption.value : "";

    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user makes selection
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ""
      }));
    }
  };

  // Handle file upload
  const handleFileUpload = (e) => {
    const files = Array.from(e.target.files);
    
    // Validate files
    const fileValidation = validateFileUpload(files, {
      maxFileSize: 10, // 10MB
      maxFiles: 5,
      allowedExtensions: ['.pdf', '.jpg', '.jpeg', '.png', '.doc', '.docx', '.xls', '.xlsx']
    });

    if (!fileValidation.isValid) {
      setErrors(prev => ({
        ...prev,
        attachments: fileValidation.errors.join(', ')
      }));
      return;
    }

    const newAttachments = files.map((file) => ({
      id: Date.now() + Math.random(),
      file: file,
      name: file.name,
      size: file.size,
      type: file.type,
    }));

    setFormData(prev => ({
      ...prev,
      attachments: [...prev.attachments, ...newAttachments]
    }));

    // Clear the input
    e.target.value = "";
    
    // Clear attachment errors
    if (errors.attachments) {
      setErrors(prev => ({
        ...prev,
        attachments: ""
      }));
    }
  };

  // Remove attachment
  const removeAttachment = (attachmentId) => {
    setFormData(prev => ({
      ...prev,
      attachments: prev.attachments.filter(att => att.id !== attachmentId)
    }));
  };

  // Validate form
  const validateForm = () => {
    // Sanitize string inputs
    const sanitizedData = {
      ...formData,
      loanAccountNumber: sanitizeString(formData.loanAccountNumber, 50),
      comment: sanitizeString(formData.comment, 1000),
      apiCallReference: sanitizeString(formData.apiCallReference, 255),
      interactionType: sanitizeString(formData.interactionType, 50),
      callStatus: sanitizeString(formData.callStatus, 50),
      visitStatus: sanitizeString(formData.visitStatus, 50),
      followupStatus: sanitizeString(formData.followupStatus, 50),
    };

    const validation = validateLoanActivityData(sanitizedData);
    
    if (!validation.isValid) {
      const newErrors = {};
      validation.errors.forEach(error => {
        // Map error messages to form fields
        if (error.includes('RM User ID')) newErrors.rmUserId = error;
        else if (error.includes('Loan Client ID')) newErrors.loanClientId = error;
        else if (error.includes('Purpose ID')) newErrors.purposeId = error;
        else if (error.includes('account number')) newErrors.loanAccountNumber = error;
        else if (error.includes('Comment')) newErrors.comment = error;
        else if (error.includes('API call reference')) newErrors.apiCallReference = error;
        else if (error.includes('Interaction type')) newErrors.interactionType = error;
        else if (error.includes('Call status')) newErrors.callStatus = error;
        else if (error.includes('Visit status')) newErrors.visitStatus = error;
        else if (error.includes('Followup status')) newErrors.followupStatus = error;
        else if (error.includes('Arrears days')) newErrors.arrearsDays = error;
        else if (error.includes('Call duration')) newErrors.callDurationMinutes = error;
        else if (error.includes('Loan balance')) newErrors.loanBalance = error;
        else if (error.includes('followup date')) newErrors.nextFollowupDate = error;
        else newErrors.general = error;
      });
      
      setErrors(newErrors);
      return false;
    }

    setErrors({});
    return true;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare data for submission
      const submitData = {
        rmUserId: formData.rmUserId,
        loanClientId: formData.loanClientId || null,
        loanAccountNumber: sanitizeString(formData.loanAccountNumber, 50) || null,
        purposeId: formData.purposeId || null,
        loanBalance: formData.loanBalance || null,
        arrearsDays: formData.arrearsDays ? parseInt(formData.arrearsDays) : null,
        comment: sanitizeString(formData.comment, 1000) || null,
        viaApi: formData.viaApi,
        apiCallReference: sanitizeString(formData.apiCallReference, 255) || null,
        interactionType: sanitizeString(formData.interactionType, 50) || null,
        callStatus: sanitizeString(formData.callStatus, 50) || null,
        visitStatus: sanitizeString(formData.visitStatus, 50) || null,
        callDurationMinutes: formData.callDurationMinutes ? parseInt(formData.callDurationMinutes) : null,
        nextFollowupDate: formData.nextFollowupDate || null,
        followupStatus: sanitizeString(formData.followupStatus, 50) || null,
      };

      // Extract files for upload
      const files = formData.attachments.map(att => att.file);

      // Call the service
      const result = await loanActivitiesService.create(submitData, files);
      
      // Call parent onSubmit callback
      if (onSubmit) {
        await onSubmit(result);
      }

      onClose();
    } catch (error) {
      console.error("Error submitting loan activity form:", error);
      setErrors({ general: error.message || "Failed to create loan activity" });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle reset
  const handleReset = () => {
    setFormData({
      rmUserId: "",
      loanClientId: "",
      loanAccountNumber: "",
      purposeId: "",
      loanBalance: "",
      arrearsDays: "",
      comment: "",
      viaApi: false,
      apiCallReference: "",
      interactionType: "",
      callStatus: "",
      visitStatus: "",
      callDurationMinutes: "",
      nextFollowupDate: "",
      followupStatus: "",
      attachments: [],
    });
    setErrors({});
  };

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-4xl mx-auto">
        <div className="flex items-center justify-center p-8">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600 dark:text-gray-400">Loading form data...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-4xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          {item ? "Edit Loan Activity" : "Create New Loan Activity"}
        </h2>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        >
          <X size={24} />
        </button>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="p-6">
        {/* General Error */}
        {errors.general && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {errors.general}
          </div>
        )}

        {/* Basic Information Section */}
        <div className="mb-8">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
            <User className="w-5 h-5 mr-2" />
            Basic Information
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* RM User (Required) */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                RM User <span className="text-red-500">*</span>
              </label>
              <Select
                name="rmUserId"
                value={rmUsers.find(user => user.value === formData.rmUserId) || null}
                onChange={handleSelectChange}
                options={rmUsers}
                placeholder="Select RM User"
                className="react-select-container"
                classNamePrefix="react-select"
                isSearchable
              />
              {errors.rmUserId && (
                <p className="mt-1 text-sm text-red-600">{errors.rmUserId}</p>
              )}
            </div>

            {/* Loan Client */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Loan Client
              </label>
              <Select
                name="loanClientId"
                value={loanClients.find(client => client.value === formData.loanClientId) || null}
                onChange={handleSelectChange}
                options={loanClients}
                placeholder="Select Loan Client"
                className="react-select-container"
                classNamePrefix="react-select"
                isSearchable
                isClearable
              />
              {errors.loanClientId && (
                <p className="mt-1 text-sm text-red-600">{errors.loanClientId}</p>
              )}
            </div>

            {/* Loan Account Number */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Loan Account Number
              </label>
              <input
                type="text"
                name="loanAccountNumber"
                value={formData.loanAccountNumber}
                onChange={handleInputChange}
                maxLength={50}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                placeholder="Enter loan account number"
              />
              {errors.loanAccountNumber && (
                <p className="mt-1 text-sm text-red-600">{errors.loanAccountNumber}</p>
              )}
            </div>

            {/* Purpose */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Purpose
              </label>
              <Select
                name="purposeId"
                value={purposes.find(purpose => purpose.value === formData.purposeId) || null}
                onChange={handleSelectChange}
                options={purposes}
                placeholder="Select Purpose"
                className="react-select-container"
                classNamePrefix="react-select"
                isSearchable
                isClearable
              />
              {errors.purposeId && (
                <p className="mt-1 text-sm text-red-600">{errors.purposeId}</p>
              )}
            </div>
          </div>
        </div>

        {/* Continue with more sections... */}
      </form>
    </div>
  );
};

export default LoanActivityForm;
