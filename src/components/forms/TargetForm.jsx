import { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import { X } from "lucide-react";
import Select from "react-select";
import instance from "../../axios/instance";
import SuccessModal from "../modals/SuccessModal";

const TargetForm = ({
  onClose,
  onSubmit,
  item = null,
  setShowSuccessModal,
}) => {
  const location = useLocation();

  // Determine default scope based on route
  const getDefaultScope = () => {
    if (location.pathname.includes("/role-targets")) {
      return "Role";
    } else if (location.pathname.includes("/individual-targets")) {
      return "Individual";
    }
    return "";
  };

  const [formData, setFormData] = useState({
    metricType: "Call", // Default to Call
    targetValue: "",
    activity: "", // New activity field
    frequency: "daily", // Default to daily
    frequencyOption: "every-day",
    customStartDate: "",
    customEndDate: "",
    selectedWeeks: [],
    selectedMonths: [],
    startDate: "",
    endDate: "",
    scope: getDefaultScope(),
    assignTo: [],
  });
  const [originalData, setOriginalData] = useState(null);
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [roles, setRoles] = useState([]);
  const [users, setUsers] = useState([]);
  const [loadingOptions, setLoadingOptions] = useState(false);

  // Users will be fetched from API

  // Metric type options
  const metricOptions = [
    { value: "Call", label: "Call" },
    { value: "Visit", label: "Visit" },
  ];

  // Activity options
  const activityOptions = [
    { value: "LEADS_HITLIST", label: "Leads Hitlist" },
    { value: "CUSTOMER_RELATIONSHIP", label: "Customer relationship" },
    { value: "TWO_BY_TWO_BY_TWO_HITLIST", label: "2by2by2 hitlist" },
    { value: "DORMANCY_HITLIST", label: "Dormancy hitlist" },
    { value: "LOAN_ACTIVITIES", label: "Loan activities" },
  ];

  // Frequency options
  const frequencyOptions = [
    { value: "daily", label: "Daily" },
    { value: "weekly", label: "Weekly" },
    { value: "monthly", label: "Monthly" },
    { value: "custom", label: "Custom" },
  ];

  // Scope options
  const scopeOptions = [
    { value: "Role", label: "Role" },
    { value: "Individual", label: "Individual" },
  ];

  // Initialize form data when editing
  useEffect(() => {
    if (item) {
      // Extract assignTo IDs based on scope
      let assignToIds = [];
      if (item.scope === "role") {
        // For role targets, assigned_to is a string (role name)
        // We'll need to find the role ID from the roles list
        assignToIds = []; // Will be populated after roles are fetched
      } else if (item.scope === "individual") {
        // For individual targets, assigned_to is an object or array of objects
        if (Array.isArray(item.assigned_to)) {
          assignToIds = item.assigned_to.map((user) => user.id);
        } else if (
          typeof item.assigned_to === "object" &&
          item.assigned_to.id
        ) {
          assignToIds = [item.assigned_to.id];
        }
      }

      const editFormData = {
        metricType: item.metric || "",
        targetValue: item.value?.toString() || "",
        activity: item.activity || "",
        frequency: item.frequency || "",
        startDate: item.start_date || "",
        endDate: item.end_date || "",
        scope: item.scope === "role" ? "Role" : "Individual",
        assignTo: assignToIds,
      };

      setFormData(editFormData);
      setOriginalData(editFormData);
    }
  }, [item]);

  // Fetch roles or users based on scope
  useEffect(() => {
    if (formData.scope === "Role") {
      fetchRoles();
    } else if (formData.scope === "Individual") {
      fetchUsers();
    }
  }, [formData.scope]);

  const fetchRoles = async () => {
    try {
      setLoadingOptions(true);
      const response = await instance.get("/roles");
      const roleOptions = response.data.data.map((role) => ({
        value: role.id,
        label: role.name,
      }));
      setRoles(roleOptions);
    } catch (error) {
      console.error("Error fetching roles:", error);
      setRoles([]);
    } finally {
      setLoadingOptions(false);
    }
  };

  const fetchUsers = async () => {
    try {
      setLoadingOptions(true);
      const response = await instance.get("/users?useBranch=true");
      const userOptions = response.data.data.map((user) => ({
        value: user.id,
        label: user.name,
      }));
      setUsers(userOptions);
    } catch (error) {
      console.error("Error fetching users:", error);
      setUsers([]);
    } finally {
      setLoadingOptions(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  const handleSelectChange = (name, selectedOptions) => {
    if (name === "assignTo") {
      const values = selectedOptions
        ? selectedOptions.map((option) => option.value)
        : [];
      setFormData((prev) => ({
        ...prev,
        [name]: values,
      }));
    } else if (name === "frequency") {
      // Clear frequency-related fields when frequency changes
      setFormData((prev) => ({
        ...prev,
        [name]: selectedOptions ? selectedOptions.value : "",
        frequencyOption: "",
        customStartDate: "",
        customEndDate: "",
        selectedWeeks: [],
        selectedMonths: [],
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: selectedOptions ? selectedOptions.value : "",
      }));
    }
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.metricType) {
      newErrors.metricType = "Metric type is required";
    }
    if (!formData.targetValue || formData.targetValue <= 0) {
      newErrors.targetValue = "Target value must be greater than 0";
    }
    if (!formData.activity) {
      newErrors.activity = "Activity is required";
    }
    if (!formData.frequency) {
      newErrors.frequency = "Frequency is required";
    }

    // Validate frequency-specific requirements
    if (formData.frequency && formData.frequency !== "custom") {
      if (!formData.frequencyOption) {
        newErrors.frequencyOption = "Please select a frequency option";
      } else if (formData.frequencyOption === "custom-daily") {
        if (!formData.customStartDate) {
          newErrors.customStartDate = "Start date is required";
        }
        if (!formData.customEndDate) {
          newErrors.customEndDate = "End date is required";
        }
        if (
          formData.customStartDate &&
          formData.customEndDate &&
          new Date(formData.customStartDate) >= new Date(formData.customEndDate)
        ) {
          newErrors.customEndDate = "End date must be after start date";
        }
      } else if (formData.frequencyOption === "custom-weekly") {
        if (formData.selectedWeeks.length === 0) {
          newErrors.selectedWeeks = "Please select at least one week";
        }
      } else if (formData.frequencyOption === "custom-monthly") {
        if (formData.selectedMonths.length === 0) {
          newErrors.selectedMonths = "Please select at least one month";
        }
      }
    } else if (formData.frequency === "custom") {
      if (!formData.customStartDate) {
        newErrors.customStartDate = "Start date is required";
      }
      if (!formData.customEndDate) {
        newErrors.customEndDate = "End date is required";
      }
      if (
        formData.customStartDate &&
        formData.customEndDate &&
        new Date(formData.customStartDate) >= new Date(formData.customEndDate)
      ) {
        newErrors.customEndDate = "End date must be after start date";
      }
    }

    // Only validate scope and assignTo when creating
    if (!item) {
      if (!formData.scope) {
        newErrors.scope = "Scope is required";
      }
      if (formData.assignTo.length === 0) {
        newErrors.assignTo = "At least one assignment is required";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      let targetData = {
        metricType: formData.metricType,
        targetValue: parseInt(formData.targetValue),
        activity: formData.activity,
        frequency: formData.frequency,
        startDate: null,
        endDate: null,
        ranges: null,
      };

      // Calculate dates based on frequency option
      if (formData.frequencyOption) {
        const today = getTodayDateTime();

        if (formData.frequencyOption.startsWith("every-")) {
          // Every day, Every week, Every month
          targetData.startDate = today;
          targetData.endDate = null;
        } else if (formData.frequencyOption === "today") {
          // Today
          targetData.startDate = today;
          targetData.endDate = today;
        } else if (formData.frequencyOption.startsWith("this-")) {
          // This week, This month
          if (formData.frequencyOption === "this-week") {
            const weekDates = getWeekStartEnd();
            targetData.startDate = weekDates.startDate;
            targetData.endDate = weekDates.endDate;
          } else if (formData.frequencyOption === "this-month") {
            const monthDates = getMonthStartEnd();
            targetData.startDate = monthDates.startDate;
            targetData.endDate = monthDates.endDate;
          }
        } else if (formData.frequencyOption.startsWith("custom-")) {
          // Custom selections
          if (formData.frequencyOption === "custom-daily") {
            // Custom date range
            if (formData.customStartDate && formData.customEndDate) {
              const startDate = new Date(formData.customStartDate);
              startDate.setHours(0, 0, 0, 0);
              const endDate = new Date(formData.customEndDate);
              endDate.setHours(23, 59, 59, 999);

              targetData.startDate = startDate.toISOString();
              targetData.endDate = endDate.toISOString();
            }
          } else {
            // Custom weeks or months
            const overallRange = getCustomRangeOverall();
            if (overallRange) {
              targetData.startDate = overallRange.startDate;
              targetData.endDate = overallRange.endDate;
            }
            targetData.ranges = getCustomRanges();
          }
        }
      } else if (formData.frequency === "custom") {
        // Direct custom frequency
        if (formData.customStartDate && formData.customEndDate) {
          const startDate = new Date(formData.customStartDate);
          startDate.setHours(0, 0, 0, 0);
          const endDate = new Date(formData.customEndDate);
          endDate.setHours(23, 59, 59, 999);

          targetData.startDate = startDate.toISOString();
          targetData.endDate = endDate.toISOString();
        }
      }

      // For creating, include scope and assignTo
      if (!item) {
        targetData.scope = formData.scope.toLowerCase();
        targetData.assignTo = formData.assignTo;
      }
      // For editing, don't include scope and assignTo

      console.log("Target data to be submitted:", targetData);

      // Call the onSubmit callback with the data and item ID for editing
      await onSubmit(targetData, item?.id);

      // Reset form and close modal
      setFormData({
        metricType: "",
        targetValue: "",
        activity: "",
        frequency: "",
        frequencyOption: "",
        customStartDate: "",
        customEndDate: "",
        selectedWeeks: [],
        selectedMonths: [],
        startDate: "",
        endDate: "",
        scope: "",
        assignTo: [],
      });
      onClose();
    } catch (error) {
      console.error("Error submitting target:", error);
      setErrors({
        submit: item
          ? "Failed to update target. Please try again."
          : "Failed to create target. Please try again.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  // Get assign to options based on scope
  const getAssignToOptions = () => {
    if (formData.scope === "Role") {
      return roles;
    } else if (formData.scope === "Individual") {
      return users;
    }
    return [];
  };

  const getSelectedAssignTo = () => {
    const options = getAssignToOptions();
    return options.filter((option) => formData.assignTo.includes(option.value));
  };

  // Helper functions for frequency options
  const generateWeekOptions = () => {
    const weeks = [];
    const today = new Date();
    const currentWeekStart = new Date(today);
    currentWeekStart.setDate(today.getDate() - today.getDay()); // Start of current week (Sunday)

    for (let i = 0; i < 8; i++) {
      // Next 8 weeks (2 months)
      const weekStart = new Date(currentWeekStart);
      weekStart.setDate(currentWeekStart.getDate() + i * 7);
      const weekEnd = new Date(weekStart);
      weekEnd.setDate(weekStart.getDate() + 6);

      const startMonth = weekStart.toLocaleDateString("en-US", {
        month: "short",
      });
      const endMonth = weekEnd.toLocaleDateString("en-US", { month: "short" });
      const startDay = weekStart.getDate();
      const endDay = weekEnd.getDate();

      weeks.push({
        value: `${weekStart.toISOString().split("T")[0]}_${
          weekEnd.toISOString().split("T")[0]
        }`,
        label: `${startMonth} ${startDay} – ${endMonth} ${endDay}`,
        startDate: weekStart.toISOString().split("T")[0],
        endDate: weekEnd.toISOString().split("T")[0],
      });
    }
    return weeks;
  };

  const generateMonthOptions = () => {
    const months = [];
    const today = new Date();

    for (let i = 0; i < 6; i++) {
      // Next 6 months
      const month = new Date(today.getFullYear(), today.getMonth() + i, 1);
      months.push({
        value: month.toISOString().split("T")[0].substring(0, 7), // YYYY-MM format
        label: month.toLocaleDateString("en-US", {
          month: "long",
          year: "numeric",
        }),
      });
    }
    return months;
  };

  const handleFrequencyOptionChange = (option) => {
    setFormData((prev) => ({
      ...prev,
      frequencyOption: option,
      selectedWeeks: [],
      selectedMonths: [],
      customStartDate: "",
      customEndDate: "",
    }));
    // Clear frequency option error when user selects an option
    if (errors.frequencyOption) {
      setErrors((prev) => ({ ...prev, frequencyOption: "" }));
    }
  };

  const handleWeekSelection = (weekValue) => {
    setFormData((prev) => ({
      ...prev,
      selectedWeeks: prev.selectedWeeks.includes(weekValue)
        ? prev.selectedWeeks.filter((w) => w !== weekValue)
        : [...prev.selectedWeeks, weekValue],
    }));
  };

  const handleMonthSelection = (monthValue) => {
    setFormData((prev) => ({
      ...prev,
      selectedMonths: prev.selectedMonths.includes(monthValue)
        ? prev.selectedMonths.filter((m) => m !== monthValue)
        : [...prev.selectedMonths, monthValue],
    }));
  };

  // Helper function to get singular frequency text
  const getFrequencySingular = (frequency) => {
    switch (frequency) {
      case "daily":
        return "day";
      case "weekly":
        return "week";
      case "monthly":
        return "month";
      default:
        return frequency;
    }
  };

  // Helper functions for date calculations
  const getTodayDateTime = () => {
    return new Date().toISOString();
  };

  const getWeekStartEnd = () => {
    const today = new Date();
    const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() - today.getDay()); // Sunday
    startOfWeek.setHours(5, 0, 0, 0); // 5 AM

    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6); // Saturday
    endOfWeek.setHours(23, 59, 59, 999);

    return {
      startDate: startOfWeek.toISOString(),
      endDate: endOfWeek.toISOString(),
    };
  };

  const getMonthStartEnd = () => {
    const today = new Date();
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    startOfMonth.setHours(5, 0, 0, 0); // 5 AM on the 1st day

    const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
    endOfMonth.setHours(23, 59, 59, 999);

    return {
      startDate: startOfMonth.toISOString(),
      endDate: endOfMonth.toISOString(),
    };
  };

  const getCustomRanges = () => {
    if (formData.frequency === "weekly" && formData.selectedWeeks.length > 0) {
      return formData.selectedWeeks.map((weekValue) => {
        const [startDateStr, endDateStr] = weekValue.split("_");
        const startDate = new Date(startDateStr);
        startDate.setHours(5, 0, 0, 0); // 5 AM on start date
        const endDate = new Date(endDateStr);
        endDate.setHours(23, 59, 59, 999);

        return {
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
        };
      });
    } else if (
      formData.frequency === "monthly" &&
      formData.selectedMonths.length > 0
    ) {
      return formData.selectedMonths.map((monthValue) => {
        const [year, month] = monthValue.split("-");
        const startDate = new Date(parseInt(year), parseInt(month) - 1, 1);
        startDate.setHours(5, 0, 0, 0); // 5 AM on 1st day
        const endDate = new Date(parseInt(year), parseInt(month), 0);
        endDate.setHours(23, 59, 59, 999);

        return {
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
        };
      });
    }
    return null;
  };

  // Helper function to get overall start and end dates for custom ranges
  const getCustomRangeOverall = () => {
    if (formData.frequency === "weekly" && formData.selectedWeeks.length > 0) {
      const sortedWeeks = [...formData.selectedWeeks].sort();
      const firstWeek = sortedWeeks[0];
      const lastWeek = sortedWeeks[sortedWeeks.length - 1];

      const [firstStartStr] = firstWeek.split("_");
      const [, lastEndStr] = lastWeek.split("_");

      const startDate = new Date(firstStartStr);
      startDate.setHours(5, 0, 0, 0);
      const endDate = new Date(lastEndStr);
      endDate.setHours(23, 59, 59, 999);

      return {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      };
    } else if (
      formData.frequency === "monthly" &&
      formData.selectedMonths.length > 0
    ) {
      const sortedMonths = [...formData.selectedMonths].sort();
      const firstMonth = sortedMonths[0];
      const lastMonth = sortedMonths[sortedMonths.length - 1];

      const [firstYear, firstMonthNum] = firstMonth.split("-");
      const [lastYear, lastMonthNum] = lastMonth.split("-");

      const startDate = new Date(
        parseInt(firstYear),
        parseInt(firstMonthNum) - 1,
        1
      );
      startDate.setHours(5, 0, 0, 0);
      const endDate = new Date(parseInt(lastYear), parseInt(lastMonthNum), 0);
      endDate.setHours(23, 59, 59, 999);

      return {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      };
    }
    return null;
  };

  return (
    <div className="flex flex-col h-full">
      <form onSubmit={handleSubmit} className="flex flex-col h-full">
        <div className="flex-1 overflow-y-auto p-6 space-y-6">
          {/* Target Type and Target Value - Side by Side */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Metric Type */}
            <div>
              <label
                className="block font-medium mb-2"
                style={{ color: "#7e7e7e", fontSize: "15px" }}
              >
                Target Type
              </label>
              <Select
                options={metricOptions}
                value={metricOptions.find(
                  (option) => option.value === formData.metricType
                )}
                onChange={(selectedOption) =>
                  handleSelectChange("metricType", selectedOption)
                }
                placeholder="Select target type"
                className="react-select-container"
                classNamePrefix="react-select"
                styles={{
                  control: (base, state) => ({
                    ...base,
                    minHeight: "50px",
                    height: "50px",
                    borderColor: errors.metricType
                      ? "#ef4444"
                      : state.isFocused
                      ? "#10b981"
                      : "#d1d5db",
                    borderWidth: "1px",
                    borderRadius: "8px",
                    boxShadow: "none",
                    outline: "none",
                    "&:hover": {
                      borderColor: errors.metricType ? "#ef4444" : "#9ca3af",
                    },
                    backgroundColor: "white",
                    fontSize: "14px",
                    transition: "border-color 0.2s ease-in-out",
                  }),
                  placeholder: (base) => ({
                    ...base,
                    color: "#9ca3af",
                    fontSize: "14px",
                  }),
                  singleValue: (base) => ({
                    ...base,
                    color: "#111827",
                    fontSize: "14px",
                  }),
                  input: (base) => ({
                    ...base,
                    color: "#111827",
                    fontSize: "14px",
                  }),
                  valueContainer: (base) => ({
                    ...base,
                    padding: "0 16px",
                    height: "46px",
                    display: "flex",
                    alignItems: "center",
                  }),
                  indicatorsContainer: (base) => ({
                    ...base,
                    padding: "0 8px",
                    height: "46px",
                  }),
                  menu: (base) => ({
                    ...base,
                    borderRadius: "8px",
                    border: "1px solid #d1d5db",
                    boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
                  }),
                  option: (base, state) => ({
                    ...base,
                    backgroundColor: state.isSelected
                      ? "#10b981"
                      : state.isFocused
                      ? "#f3f4f6"
                      : "white",
                    color: state.isSelected ? "white" : "#111827",
                    fontSize: "14px",
                    "&:hover": {
                      backgroundColor: state.isSelected ? "#10b981" : "#f3f4f6",
                    },
                  }),
                }}
              />
              {errors.metricType && (
                <p className="text-red-500 text-sm mt-1">{errors.metricType}</p>
              )}
            </div>

            {/* Target Value */}
            <div>
              <label
                className="block font-medium mb-2"
                style={{ color: "#7e7e7e", fontSize: "15px" }}
              >
                Target Value
              </label>
              <input
                type="number"
                name="targetValue"
                value={formData.targetValue}
                onChange={handleChange}
                placeholder="Enter target value"
                min="1"
                className={`w-full px-4 py-3 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none transition-colors duration-200 ${
                  errors.targetValue
                    ? "border-red-500 dark:border-red-400 focus:border-red-500"
                    : "border-gray-300 dark:border-gray-600 focus:border-green-500"
                }`}
              />
              {errors.targetValue && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.targetValue}
                </p>
              )}
            </div>
          </div>

          {/* Scope and Assign To - Side by Side - Only show when creating */}
          {!item && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Scope */}
              <div>
                <label
                  className="block font-medium mb-2"
                  style={{ color: "#7e7e7e", fontSize: "15px" }}
                >
                  Scope
                </label>
                <Select
                  options={scopeOptions}
                  value={scopeOptions.find(
                    (option) => option.value === formData.scope
                  )}
                  onChange={(selectedOption) =>
                    handleSelectChange("scope", selectedOption)
                  }
                  placeholder="Select scope"
                  className="react-select-container"
                  classNamePrefix="react-select"
                  styles={{
                    control: (base, state) => ({
                      ...base,
                      minHeight: "48px",
                      height: "48px",
                      borderColor: errors.scope
                        ? "#ef4444"
                        : state.isFocused
                        ? "#10b981"
                        : "#d1d5db",
                      borderWidth: "1px",
                      borderRadius: "8px",
                      boxShadow: "none",
                      outline: "none",
                      "&:hover": {
                        borderColor: errors.scope ? "#ef4444" : "#9ca3af",
                      },
                      backgroundColor: "white",
                      fontSize: "14px",
                      transition: "border-color 0.2s ease-in-out",
                    }),
                    placeholder: (base) => ({
                      ...base,
                      color: "#9ca3af",
                      fontSize: "14px",
                    }),
                    singleValue: (base) => ({
                      ...base,
                      color: "#111827",
                      fontSize: "14px",
                    }),
                    input: (base) => ({
                      ...base,
                      color: "#111827",
                      fontSize: "14px",
                    }),
                    valueContainer: (base) => ({
                      ...base,
                      padding: "0 16px",
                      height: "46px",
                      display: "flex",
                      alignItems: "center",
                    }),
                    indicatorsContainer: (base) => ({
                      ...base,
                      padding: "0 8px",
                      height: "46px",
                    }),
                    menu: (base) => ({
                      ...base,
                      borderRadius: "8px",
                      border: "1px solid #d1d5db",
                      boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
                    }),
                    option: (base, state) => ({
                      ...base,
                      backgroundColor: state.isSelected
                        ? "#10b981"
                        : state.isFocused
                        ? "#f3f4f6"
                        : "white",
                      color: state.isSelected ? "white" : "#111827",
                      fontSize: "14px",
                      "&:hover": {
                        backgroundColor: state.isSelected
                          ? "#10b981"
                          : "#f3f4f6",
                      },
                    }),
                  }}
                />
                {errors.scope && (
                  <p className="text-red-500 text-sm mt-1">{errors.scope}</p>
                )}
              </div>

              {/* Assign To */}
              <div>
                <label
                  className="block font-medium mb-2"
                  style={{ color: "#7e7e7e", fontSize: "15px" }}
                >
                  Assign To
                </label>
                <Select
                  isMulti
                  options={getAssignToOptions()}
                  value={getSelectedAssignTo()}
                  onChange={(selectedOptions) =>
                    handleSelectChange("assignTo", selectedOptions)
                  }
                  placeholder={
                    formData.scope === "Role"
                      ? "Select roles"
                      : formData.scope === "Individual"
                      ? "Select users"
                      : "Select scope first"
                  }
                  isDisabled={!formData.scope}
                  isLoading={loadingOptions}
                  className="react-select-container"
                  classNamePrefix="react-select"
                  styles={{
                    control: (base, state) => ({
                      ...base,
                      minHeight: "48px",
                      height: "48px",
                      borderColor: errors.assignTo
                        ? "#ef4444"
                        : state.isFocused
                        ? "#10b981"
                        : "#d1d5db",
                      borderWidth: "1px",
                      borderRadius: "8px",
                      boxShadow: "none",
                      outline: "none",
                      "&:hover": {
                        borderColor: errors.assignTo ? "#ef4444" : "#9ca3af",
                      },
                      backgroundColor: "white",
                      fontSize: "14px",
                      transition: "border-color 0.2s ease-in-out",
                    }),
                    placeholder: (base) => ({
                      ...base,
                      color: "#9ca3af",
                      fontSize: "14px",
                    }),
                    input: (base) => ({
                      ...base,
                      color: "#111827",
                      fontSize: "14px",
                    }),
                    valueContainer: (base) => ({
                      ...base,
                      padding: "0 16px",
                      height: "46px",
                      display: "flex",
                      alignItems: "center",
                    }),
                    indicatorsContainer: (base) => ({
                      ...base,
                      padding: "0 8px",
                      height: "46px",
                    }),
                    menu: (base) => ({
                      ...base,
                      borderRadius: "8px",
                      border: "1px solid #d1d5db",
                      boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
                    }),
                    option: (base, state) => ({
                      ...base,
                      backgroundColor: state.isSelected
                        ? "#10b981"
                        : state.isFocused
                        ? "#f3f4f6"
                        : "white",
                      color: state.isSelected ? "white" : "#111827",
                      fontSize: "14px",
                      "&:hover": {
                        backgroundColor: state.isSelected
                          ? "#10b981"
                          : "#f3f4f6",
                      },
                    }),
                    multiValue: (base) => ({
                      ...base,
                      backgroundColor: "#e5e7eb",
                      borderRadius: "4px",
                    }),
                    multiValueLabel: (base) => ({
                      ...base,
                      color: "#374151",
                      fontSize: "14px",
                    }),
                    multiValueRemove: (base) => ({
                      ...base,
                      color: "#6b7280",
                      "&:hover": {
                        backgroundColor: "#d1d5db",
                        color: "#374151",
                      },
                    }),
                  }}
                />
                {errors.assignTo && (
                  <p className="text-red-500 text-sm mt-1">{errors.assignTo}</p>
                )}
              </div>
            </div>
          )}

          {/* Activity */}
          <div>
            <label
              className="block font-medium mb-2"
              style={{ color: "#7e7e7e", fontSize: "15px" }}
            >
              Activity
            </label>
            <Select
              options={activityOptions}
              value={activityOptions.find(
                (option) => option.value === formData.activity
              )}
              onChange={(selectedOption) =>
                handleSelectChange("activity", selectedOption)
              }
              placeholder="Select activity"
              className="react-select-container"
              classNamePrefix="react-select"
              styles={{
                control: (base, state) => ({
                  ...base,
                  minHeight: "48px",
                  height: "48px",
                  borderColor: errors.activity
                    ? "#ef4444"
                    : state.isFocused
                    ? "#10b981"
                    : "#d1d5db",
                  borderWidth: "1px",
                  borderRadius: "8px",
                  boxShadow: "none",
                  outline: "none",
                  "&:hover": {
                    borderColor: errors.activity ? "#ef4444" : "#9ca3af",
                  },
                  backgroundColor: "white",
                  fontSize: "14px",
                  transition: "border-color 0.2s ease-in-out",
                }),
                placeholder: (base) => ({
                  ...base,
                  color: "#9ca3af",
                  fontSize: "14px",
                }),
                singleValue: (base) => ({
                  ...base,
                  color: "#111827",
                  fontSize: "14px",
                }),
                input: (base) => ({
                  ...base,
                  color: "#111827",
                  fontSize: "14px",
                }),
                valueContainer: (base) => ({
                  ...base,
                  padding: "0 16px",
                  height: "46px",
                  display: "flex",
                  alignItems: "center",
                }),
                indicatorsContainer: (base) => ({
                  ...base,
                  padding: "0 8px",
                  height: "46px",
                }),
                menu: (base) => ({
                  ...base,
                  borderRadius: "8px",
                  border: "1px solid #d1d5db",
                  boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
                }),
                option: (base, state) => ({
                  ...base,
                  backgroundColor: state.isSelected
                    ? "#10b981"
                    : state.isFocused
                    ? "#f3f4f6"
                    : "white",
                  color: state.isSelected ? "white" : "#111827",
                  fontSize: "14px",
                  "&:hover": {
                    backgroundColor: state.isSelected ? "#10b981" : "#f3f4f6",
                  },
                }),
              }}
            />
            {errors.activity && (
              <p className="text-red-500 text-sm mt-1">{errors.activity}</p>
            )}
          </div>

          {/* Frequency */}
          <div style={{ display: "none" }}>
            <label
              className="block font-medium mb-2"
              style={{ color: "#7e7e7e", fontSize: "15px" }}
            >
              Frequency
            </label>
            <Select
              options={frequencyOptions}
              value={frequencyOptions.find(
                (option) => option.value === formData.frequency
              )}
              onChange={(selectedOption) =>
                handleSelectChange("frequency", selectedOption)
              }
              placeholder="Select frequency"
              className="react-select-container"
              classNamePrefix="react-select"
              styles={{
                control: (base, state) => ({
                  ...base,
                  minHeight: "48px",
                  height: "48px",
                  borderColor: errors.frequency
                    ? "#ef4444"
                    : state.isFocused
                    ? "#10b981"
                    : "#d1d5db",
                  borderWidth: "1px",
                  borderRadius: "8px",
                  boxShadow: "none",
                  outline: "none",
                  "&:hover": {
                    borderColor: errors.frequency ? "#ef4444" : "#9ca3af",
                  },
                  backgroundColor: "white",
                  fontSize: "14px",
                  transition: "border-color 0.2s ease-in-out",
                }),
                placeholder: (base) => ({
                  ...base,
                  color: "#9ca3af",
                  fontSize: "14px",
                }),
                singleValue: (base) => ({
                  ...base,
                  color: "#111827",
                  fontSize: "14px",
                }),
                input: (base) => ({
                  ...base,
                  color: "#111827",
                  fontSize: "14px",
                }),
                valueContainer: (base) => ({
                  ...base,
                  padding: "0 16px",
                  height: "46px",
                  display: "flex",
                  alignItems: "center",
                }),
                indicatorsContainer: (base) => ({
                  ...base,
                  padding: "0 8px",
                  height: "46px",
                }),
                menu: (base) => ({
                  ...base,
                  borderRadius: "8px",
                  border: "1px solid #d1d5db",
                  boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
                }),
                option: (base, state) => ({
                  ...base,
                  backgroundColor: state.isSelected
                    ? "#10b981"
                    : state.isFocused
                    ? "#f3f4f6"
                    : "white",
                  color: state.isSelected ? "white" : "#111827",
                  fontSize: "14px",
                  "&:hover": {
                    backgroundColor: state.isSelected ? "#10b981" : "#f3f4f6",
                  },
                }),
              }}
            />
            {errors.frequency && (
              <p className="text-red-500 text-md mt-1">{errors.frequency}</p>
            )}
          </div>

          {/* Frequency Options */}
          {formData.frequency && formData.frequency !== "custom" && (
            <div className="space-y-6 bg-gray-50 p-6 rounded-lg hidden">
              {errors.frequencyOption && (
                <span className="inline-flex items-center px-2.5 py-3.5 rounded-lg text-md font-medium bg-red-100 text-red-800 w-full">
                  {errors.frequencyOption}
                </span>
              )}
              <h4
                className="font-semibold text-gray-900"
                style={{ fontSize: "16px" }}
              >
                {formData.frequency.charAt(0).toUpperCase() +
                  formData.frequency.slice(1)}{" "}
                options
              </h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Left Column */}
                <div className="space-y-4">
                  <label className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-white transition-colors cursor-pointer">
                    <input
                      type="radio"
                      name="frequencyOption"
                      value={`every-${getFrequencySingular(
                        formData.frequency
                      )}`}
                      checked={
                        formData.frequencyOption ===
                        `every-${getFrequencySingular(formData.frequency)}`
                      }
                      onChange={(e) =>
                        handleFrequencyOptionChange(e.target.value)
                      }
                      className="w-4 h-4 text-gray-600 focus:ring-gray-500 focus:ring-2"
                    />
                    <span
                      className="text-gray-900 font-medium"
                      style={{ fontSize: "15px" }}
                    >
                      Every {getFrequencySingular(formData.frequency)}
                    </span>
                  </label>

                  <label className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-white transition-colors cursor-pointer">
                    <input
                      type="radio"
                      name="frequencyOption"
                      value={
                        formData.frequency === "daily"
                          ? "today"
                          : `this-${getFrequencySingular(formData.frequency)}`
                      }
                      checked={
                        formData.frequencyOption ===
                        (formData.frequency === "daily"
                          ? "today"
                          : `this-${getFrequencySingular(formData.frequency)}`)
                      }
                      onChange={(e) =>
                        handleFrequencyOptionChange(e.target.value)
                      }
                      className="w-4 h-4 text-gray-600 focus:ring-gray-500 focus:ring-2"
                    />
                    <span
                      className="text-gray-900 font-medium"
                      style={{ fontSize: "15px" }}
                    >
                      {formData.frequency === "daily"
                        ? "Today"
                        : `This ${getFrequencySingular(formData.frequency)}`}
                    </span>
                  </label>
                  <label className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-white transition-colors cursor-pointer">
                    <input
                      type="radio"
                      name="frequencyOption"
                      value="this-week"
                      checked={formData.frequencyOption === "this-week"}
                      onChange={(e) =>
                        handleFrequencyOptionChange(e.target.value)
                      }
                      className="w-4 h-4 text-gray-600 focus:ring-gray-500 focus:ring-2"
                    />
                    <span
                      className="text-gray-900 font-medium"
                      style={{ fontSize: "15px" }}
                    >
                      This week
                    </span>
                  </label>
                </div>

                {/* Right Column */}
                <div className="space-y-4">
                  <label className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-white transition-colors cursor-pointer">
                    <input
                      type="radio"
                      name="frequencyOption"
                      value="this-month"
                      checked={formData.frequencyOption === "this-month"}
                      onChange={(e) =>
                        handleFrequencyOptionChange(e.target.value)
                      }
                      className="w-4 h-4 text-gray-600 focus:ring-gray-500 focus:ring-2"
                    />
                    <span
                      className="text-gray-900 font-medium"
                      style={{ fontSize: "15px" }}
                    >
                      This month
                    </span>
                  </label>

                  {formData.frequency !== "monthly" && (
                    <label className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-white transition-colors cursor-pointer">
                      <input
                        type="radio"
                        name="frequencyOption"
                        value={`custom-${formData.frequency}`}
                        checked={
                          formData.frequencyOption ===
                          `custom-${formData.frequency}`
                        }
                        onChange={(e) =>
                          handleFrequencyOptionChange(e.target.value)
                        }
                        className="w-4 h-4 text-gray-600 focus:ring-gray-500 focus:ring-2"
                      />
                      <span
                        className="text-gray-900 font-medium"
                        style={{ fontSize: "15px" }}
                      >
                        Custom (
                        {formData.frequency === "daily"
                          ? "date range"
                          : formData.frequency === "weekly"
                          ? "select weeks"
                          : "select months"}
                        )
                      </span>
                    </label>
                  )}

                  {/* Monthly specific options */}
                  {formData.frequency === "monthly" && (
                    <label className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-white transition-colors cursor-pointer">
                      <input
                        type="radio"
                        name="frequencyOption"
                        value={`custom-${formData.frequency}`}
                        checked={
                          formData.frequencyOption ===
                          `custom-${formData.frequency}`
                        }
                        onChange={(e) =>
                          handleFrequencyOptionChange(e.target.value)
                        }
                        className="w-4 h-4 text-gray-600 focus:ring-gray-500 focus:ring-2"
                      />
                      <span
                        className="text-gray-900 font-medium"
                        style={{ fontSize: "15px" }}
                      >
                        Custom (select months)
                      </span>
                    </label>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Custom Daily Options - Date Range */}
          {formData.frequencyOption === "custom-daily" && (
            <div className="space-y-4 bg-white p-6 rounded-lg border border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-lg font-semibold text-gray-900 mb-3">
                    Start date
                  </label>
                  <div className="relative">
                    <input
                      type="date"
                      name="customStartDate"
                      value={formData.customStartDate}
                      onChange={handleChange}
                      className={`w-full px-4 py-3 border rounded-lg bg-white text-gray-900 focus:outline-none transition-colors duration-200 ${
                        errors.customStartDate
                          ? "border-red-500"
                          : "border-gray-300 focus:border-green-500"
                      }`}
                    />
                  </div>
                  {errors.customStartDate && (
                    <p className="text-red-500 text-sm mt-1">
                      {errors.customStartDate}
                    </p>
                  )}
                </div>
                <div>
                  <label className="block text-lg font-semibold text-gray-900 mb-3">
                    End date
                  </label>
                  <div className="relative">
                    <input
                      type="date"
                      name="customEndDate"
                      value={formData.customEndDate}
                      onChange={handleChange}
                      className={`w-full px-4 py-3 border rounded-lg bg-white text-gray-900 focus:outline-none transition-colors duration-200 ${
                        errors.customEndDate
                          ? "border-red-500"
                          : "border-gray-300 focus:border-green-500"
                      }`}
                    />
                  </div>
                  {errors.customEndDate && (
                    <p className="text-red-500 text-sm mt-1">
                      {errors.customEndDate}
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Custom Weekly Options - Week Selection */}
          {formData.frequencyOption === "custom-weekly" && (
            <div className="space-y-6 bg-white p-6 rounded-lg border border-gray-200">
              <h4
                className="font-semibold text-gray-900"
                style={{ fontSize: "16px" }}
              >
                Select weeks (next 2 months)
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {generateWeekOptions().map((week) => (
                  <label
                    key={week.value}
                    className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer"
                  >
                    <input
                      type="checkbox"
                      checked={formData.selectedWeeks.includes(week.value)}
                      onChange={() => handleWeekSelection(week.value)}
                      className="w-4 h-4 text-gray-600 focus:ring-gray-500 focus:ring-2 rounded"
                    />
                    <span
                      className="text-gray-900 font-medium"
                      style={{ fontSize: "15px" }}
                    >
                      {week.label}
                    </span>
                  </label>
                ))}
              </div>
              {errors.selectedWeeks && (
                <p className="text-red-500 text-sm mt-2">
                  {errors.selectedWeeks}
                </p>
              )}
            </div>
          )}

          {/* Custom Monthly Options - Month Selection */}
          {formData.frequencyOption === "custom-monthly" && (
            <div className="space-y-6 bg-white p-6 rounded-lg border border-gray-200">
              <h4
                className="font-semibold text-gray-900"
                style={{ fontSize: "16px" }}
              >
                Select months (next 6 months)
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {generateMonthOptions().map((month) => (
                  <label
                    key={month.value}
                    className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer"
                  >
                    <input
                      type="checkbox"
                      checked={formData.selectedMonths.includes(month.value)}
                      onChange={() => handleMonthSelection(month.value)}
                      className="w-4 h-4 text-gray-600 focus:ring-gray-500 focus:ring-2 rounded"
                    />
                    <span
                      className="text-gray-900 font-medium"
                      style={{ fontSize: "15px" }}
                    >
                      {month.label}
                    </span>
                  </label>
                ))}
              </div>
              {errors.selectedMonths && (
                <p className="text-red-500 text-sm mt-2">
                  {errors.selectedMonths}
                </p>
              )}
            </div>
          )}

          {/* Custom Frequency Options */}
          {formData.frequency === "custom" && (
            <div className="space-y-4">
              <h4 className="text-sm font-medium" style={{ color: "#7e7e7e" }}>
                Custom date range
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label
                    className="block text-sm font-medium mb-2"
                    style={{ color: "#7e7e7e" }}
                  >
                    Start date
                  </label>
                  <input
                    type="date"
                    name="customStartDate"
                    value={formData.customStartDate}
                    onChange={handleChange}
                    className={`w-full px-4 py-3 border rounded-lg bg-white text-gray-900 focus:outline-none transition-colors duration-200 ${
                      errors.customStartDate
                        ? "border-red-500"
                        : "border-gray-300 focus:border-green-500"
                    }`}
                  />
                  {errors.customStartDate && (
                    <p className="text-red-500 text-sm mt-1">
                      {errors.customStartDate}
                    </p>
                  )}
                </div>
                <div>
                  <label
                    className="block text-sm font-medium mb-2"
                    style={{ color: "#7e7e7e" }}
                  >
                    End date
                  </label>
                  <input
                    type="date"
                    name="customEndDate"
                    value={formData.customEndDate}
                    onChange={handleChange}
                    className={`w-full px-4 py-3 border rounded-lg bg-white text-gray-900 focus:outline-none transition-colors duration-200 ${
                      errors.customEndDate
                        ? "border-red-500"
                        : "border-gray-300 focus:border-green-500"
                    }`}
                  />
                  {errors.customEndDate && (
                    <p className="text-red-500 text-sm mt-1">
                      {errors.customEndDate}
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Submit Error */}
          {errors.submit && (
            <span className="inline-flex items-center px-2.5 py-3.5 rounded-lg text-md font-medium bg-red-100 text-red-800 w-full">
              {errors.submit}
            </span>
          )}
        </div>

        {/* Buttons - Fixed at bottom */}
        <div className="flex-shrink-0 p-6 pt-4 border-t border-gray-200 bg-white">
          <div className="flex gap-3">
            <button
              type="button"
              onClick={handleClose}
              disabled={isSubmitting}
              className="flex-1 px-4 py-3 text-sm font-medium rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              style={{ color: "#7e7e7e" }}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="flex-1 px-4 py-3 text-sm font-medium text-white rounded-lg bg-green-600 hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
            >
              {isSubmitting ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  Creating...
                </>
              ) : item ? (
                "Update Target"
              ) : (
                "Create Target"
              )}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default TargetForm;
