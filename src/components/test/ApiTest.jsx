import { useState } from "react";
i
const ApiTest = () => {
  const { regionApi } = useApi();
  const [testResults, setTestResults] = useState([]);
  const [loading, setLoading] = useState(false);

  const addTestResult = (test, result, error = null) => {
    setTestResults(prev => [...prev, {
      test,
      result,
      error,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  const testGetAllRegions = async () => {
    setLoading(true);
    try {
      const data = await regionApi.getAll();
      addTestResult("GET /regions", "Success", null);
      console.log("Regions data:", data);
    } catch (error) {
      addTestResult("GET /regions", "Failed", error.message);
    } finally {
      setLoading(false);
    }
  };

  const testCreateRegion = async () => {
    setLoading(true);
    try {
      const testData = { name: `Test Region ${Date.now()}` };
      const result = await regionApi.create(testData);
      addTestResult("POST /regions", "Success", null);
      console.log("Created region:", result);
    } catch (error) {
      addTestResult("POST /regions", "Failed", error.message);
    } finally {
      setLoading(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow">
      <h2 className="text-xl font-bold mb-4 text-gray-900 dark:text-white">
        API Integration Test
      </h2>
      
      <div className="space-y-4">
        <div className="flex space-x-2">
          <button
            onClick={testGetAllRegions}
            disabled={loading}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
          >
            Test GET Regions
          </button>
          
          <button
            onClick={testCreateRegion}
            disabled={loading}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
          >
            Test CREATE Region
          </button>
          
          <button
            onClick={clearResults}
            className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            Clear Results
          </button>
        </div>

        {loading && (
          <div className="text-blue-600 dark:text-blue-400">
            Testing API...
          </div>
        )}

        <div className="mt-6">
          <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white">
            Test Results:
          </h3>
          
          {testResults.length === 0 ? (
            <p className="text-gray-500 dark:text-gray-400">
              No tests run yet. Click a button above to test the API.
            </p>
          ) : (
            <div className="space-y-2">
              {testResults.map((result, index) => (
                <div
                  key={index}
                  className={`p-3 rounded border-l-4 ${
                    result.result === "Success"
                      ? "bg-green-50 border-green-400 dark:bg-green-900/20"
                      : "bg-red-50 border-red-400 dark:bg-red-900/20"
                  }`}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <span className="font-medium text-gray-900 dark:text-white">
                        {result.test}
                      </span>
                      <span
                        className={`ml-2 px-2 py-1 text-xs rounded ${
                          result.result === "Success"
                            ? "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100"
                            : "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100"
                        }`}
                      >
                        {result.result}
                      </span>
                    </div>
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {result.timestamp}
                    </span>
                  </div>
                  {result.error && (
                    <div className="mt-1 text-sm text-red-600 dark:text-red-400">
                      Error: {result.error}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ApiTest;
