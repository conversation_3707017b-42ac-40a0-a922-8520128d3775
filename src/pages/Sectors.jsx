import { useState, useEffect } from "react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import DeleteConfirmation from "../components/forms/DeleteConfirmation";
import SectorForm from "../components/forms/SectorForm";
import SuccessModal from "../components/modals/SuccessModal";
import {
  isicSectorsService,
  formatSectorsResponse,
  formatDateTime,
} from "../services/isicSectorsService";
import { downloadSectorsTemplate } from "../utils/excelUtils";
import { hasPermission } from "../utils/permissionUtils";

const Sectors = () => {
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [sectors, setSectors] = useState([]);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [error, setError] = useState(null);

  // Define columns of the table here and what to render
  const columns = [
    {
      key: "code",
      title: "CODE",
      render: (value) => (
        <span className="text-lg text-gray-600 dark:text-gray-400 font-mono">
          {value }
        </span>
      ),
    },
    {
      key: "name",
      title: "SECTOR",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "addedBy",
      title: "ADDED BY",
      render: (value) => (
        <span className="inline-flex px-2 py-1 font-semibold rounded-full  ">
          {value }
      
        </span>
      ),
    },
    {
      key: "formattedDate",
      title: "ADDED ON",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
  ];

  // Fetch sectors data on component mount
  useEffect(() => {
    fetchSectors();
  }, []);

  const fetchSectors = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await isicSectorsService.getAll();
      const formattedSectors = formatSectorsResponse(response);
      setSectors(formattedSectors);
    } catch (err) {
      console.error("Error fetching sectors:", err);
      setError("Failed to fetch sectors. Please try again.");
      setSectors([]); // Set empty array on error
    } finally {
      setLoading(false);
    }
  };

  // Form submission handlers
  const handleCreateSubmit = async (formData) => {
    try {
      console.log("Creating SECTOR:", formData);
      const newSector = await isicSectorsService.create(formData);

      // Add the new sector to the state
      const formattedSector = {
        id: newSector.id,
        code: newSector.code || "N/A",
        name: newSector.name,
        addedOnDate: newSector.addedOnDate,
        addedBy: newSector.addedBy || "N/A",
        formattedDate: formatDateTime(newSector.addedOnDate),
      };

      setSectors((prevSectors) => [formattedSector, ...prevSectors]);
      console.log("Sector created successfully:", newSector);
    } catch (error) {
      console.error("Error creating sector:", error);
      setError("Failed to create sector. Please try again.");
    }
  };

  const handleEditSubmit = async (formData, sectorId) => {
    try {
      console.log("Updating sector:", formData, "ID:", sectorId);
      const updatedSector = await isicSectorsService.update(sectorId, formData);

      // Update the sector in state
      const formattedSector = {
        id: updatedSector.id,
        code: updatedSector.code || "N/A",
        name: updatedSector.name,
        addedOnDate: updatedSector.addedOnDate,
        addedBy: updatedSector.addedBy || "N/A",
        formattedDate: formatDateTime(updatedSector.addedOnDate),
      };

      setSectors((prevSectors) =>
        prevSectors.map((sector) =>
          sector.id === sectorId ? formattedSector : sector
        )
      );
      console.log("Sector updated successfully:", updatedSector);
    } catch (error) {
      console.error("Error updating sector:", error);
      setError("Failed to update sector. Please try again.");
    }
  };

  const handleDeleteConfirm = async (sector) => {
    try {
      console.log("Deleting sector:", sector);
      const success = await isicSectorsService.delete(sector.id);

      if (success) {
        // Remove the sector from state
        setSectors((prevSectors) =>
          prevSectors.filter((s) => s.id !== sector.id)
        );
        console.log("Sector deleted successfully");
      }
    } catch (error) {
      console.error("Error deleting sector:", error);
      setError("Failed to delete sector. Please try again.");
    }
  };

  const handleView = (sector) => {
    console.log("View sector:", sector);
    // Here you would typically navigate to view page or show view modal
  };

  const handleLoadMore = () => {
    setLoadingMore(true);
    // Simulate API call
    setTimeout(() => {
      setLoadingMore(false);
      console.log("Load more sectors");
    }, 2000);
  };

  // Import/Export handlers
  const handleImport = async (file) => {
    try {
      console.log("Importing sectors from file:", file.name);

      const result = await isicSectorsService.importFromFile(file);

      console.log("Import result:", result);

      if (result.success) {
        setSuccessMessage(
          `Successfully imported ${result.imported || 0} sectors!`
        );
        setShowSuccessModal(true);

        // Refresh the sectors data
        await fetchSectors();
      } else {
        console.error(result.message || "Failed to import sectors");
      }
    } catch (error) {
      console.error("Error importing sectors:", error);
    }
  };

  const handleDownloadTemplate = () => {
    try {
      downloadSectorsTemplate();
      setSuccessMessage("Sectors template downloaded successfully!");
      setShowSuccessModal(true);
    } catch (error) {
      console.error("Error downloading sectors template:", error);
    }
  };

  const handleExport = () => {
    console.log("Exporting sectors data...");
    // Here you would typically generate and download the export file
    // For now, we'll just log the action
  };

  return (
    <PrivateLayout perm_required={["isic.sector.view"]}>
      {/* Error Display */}
      {error && (
        <div className="mb-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <p className="text-red-600 dark:text-red-400">{error}</p>
          <button
            onClick={() => {
              setError(null);
              fetchSectors();
            }}
            className="mt-2 text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 underline"
          >
            Try again
          </button>
        </div>
      )}

      {/* Data Table */}
      <div className="">
        <DataTable
          columns={columns}
          data={sectors}
          searchPlaceholder="Search ..."
          addButtonText="New Sector"
          onView={handleView}
          actions={
            [
              { name: "view", is_visible: () => hasPermission("isic.sector.view") },
              { name: "edit", is_visible: () => hasPermission("isic.sector.edit") },
              { name: "delete", is_visible: () => hasPermission("isic.sector.delete") },
            ]
          }
          loading={loading}
          loadingMore={loadingMore}
          loadMoreText="Load More Sectors"
          onLoadMore={handleLoadMore}
          showLoadMore={true}
          showCreateButton = {hasPermission("isic.sector.create") && true}
          allowMultiSelect={hasPermission("isic.sector.delete") && true}
          // Modal forms
          createForm={({ onClose }) => (
            <SectorForm onClose={onClose} onSubmit={handleCreateSubmit} />
          )}
          editForm={({ item, onClose }) => (
            <SectorForm
              item={item}
              onClose={onClose}
              onSubmit={(formData, sectorId) =>
                handleEditSubmit(formData, sectorId)
              }
            />
          )}
          deleteForm={({ item, onClose }) => (
            <DeleteConfirmation
              item={item}
              onClose={onClose}
              onConfirm={handleDeleteConfirm}
              itemName="Sector"
            />
          )}
          createModalTitle="Create New Sector"
          editModalTitle="Sector"
          deleteModalTitle=""
          modalSize="lg"
          deleteModalSize="sm"
          // Import/Export functionality
          showImportExport={hasPermission("isic.sector.create") && true}
          onExport={handleExport}
          onImport={handleImport}
          importModalTitle="Import Sectors"
          importTemplateFileName="Sectors-Template.xlsx"
          importAcceptedFileTypes=".xlsx,.xls,.csv"
          onDownloadTemplate={handleDownloadTemplate}
        />
      </div>

      {/* Success Modal */}
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        title="Success"
        message={successMessage}
      />
    </PrivateLayout>
  );
};

export default Sectors;
