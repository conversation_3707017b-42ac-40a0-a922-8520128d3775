import { useState, useEffect, useMemo } from "react";
import { useNavigate, useLocation, useSearchParams } from "react-router-dom";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import FollowUpCallForm from "../components/forms/FollowUpCallForm";
import FollowUpVisitForm from "../components/forms/FollowUpVisitForm";
import FollowUpLeadProfile from "../components/forms/FollowUpLeadProfile";
import RescheduleModal from "../components/modals/RescheduleModal";
import SuccessModal from "../components/modals/SuccessModal";
import ErrorModal from "../components/modals/ErrorModal";
import {
  followUpsService,
  formatFollowUpsForTable,
} from "../services/followUpsService";
import { toast } from "react-toastify";
import { Phone, MapPin, Calendar, User, X } from "lucide-react";

const FollowUps = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams, setSearchParams] = useSearchParams();

  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [followUps, setFollowUps] = useState([]);
  const [error, setError] = useState(null);

  // Statistics state
  const [statisticsCounts, setStatisticsCounts] = useState({
    today: 0,
    upcoming: 0,
    completed: 0,
    overdue: 0,
    cancelled: 0,
  });
  const [activeStatistic, setActiveStatistic] = useState("today");

  // Modal states
  const [showRescheduleModal, setShowRescheduleModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [errorMessage, setErrorMessage] = useState("");

  // Initialize activeStatistic from URL parameter
  useEffect(() => {
    const typeParam = searchParams.get("type");
    if (
      typeParam &&
      ["today", "upcoming", "completed", "overdue", "cancelled"].includes(
        typeParam
      )
    ) {
      setActiveStatistic(typeParam);
    } else {
      // Default to today if no valid type parameter
      setActiveStatistic("today");
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.set("type", "today");
      setSearchParams(newSearchParams);
    }
  }, [searchParams, setSearchParams]);

  // Fetch statistics counts for all types
  const fetchStatisticsCounts = async () => {
    try {
      const data = await followUpsService.getCounts();
      setStatisticsCounts({
        today: data.today_count || 0,
        upcoming: data.upcoming_count || 0,
        completed: data.completed_count || 0,
        overdue: data.overdue_count || 0,
        cancelled: data.cancelled_count || 0,
      });
    } catch (error) {
      console.error("Error fetching statistics counts:", error);
      setStatisticsCounts({
        today: 0,
        upcoming: 0,
        completed: 0,
        overdue: 0,
        cancelled: 0,
      });
    }
  };

  // Statistics data
  const getStatistics = () => {
    return [
      {
        key: "today",
        label: "Today",
        value: statisticsCounts.today || 0,
      },
      {
        key: "upcoming",
        label: "Upcoming",
        value: statisticsCounts.upcoming || 0,
      },
      {
        key: "completed",
        label: "Completed",
        value: statisticsCounts.completed || 0,
      },
      {
        key: "overdue",
        label: "Overdue",
        value: statisticsCounts.overdue || 0,
      },
      {
        key: "cancelled",
        label: "Cancelled",
        value: statisticsCounts.cancelled || 0,
      },
    ];
  };

  // Filter states
  const [filters, setFilters] = useState({
    status: "",
  });

  // Date range filter states
  const [fromDate, setFromDate] = useState("");
  const [toDate, setToDate] = useState("");

  // Filter configuration for the DataTable
  const filterConfig = [
    {
      field: "status",
      label: "Status",
      type: "select",
      options: [
        { value: "", label: "All Statuses" },
        { value: "Pending", label: "Pending" },
        { value: "Completed", label: "Completed" },
        { value: "Canceled", label: "Cancelled" }, // Note: data uses "Canceled" but display "Cancelled"
      ],
    },
  ];

  // Filter change handlers
  const handleFilterChange = (field, value) => {
    setFilters((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleClearFilters = () => {
    setFilters({
      status: "",
    });
  };

  // Date change handlers for DataTable
  const handleFromDateChange = (date) => {
    setFromDate(date);
  };

  const handleToDateChange = (date) => {
    setToDate(date);
  };

  // Check if agent_name is available in any follow-up to show/hide Agent Name column
  const showAgentNameColumn = followUps.some((followUp) => followUp.agent_name);

  // Define columns of the table here and what to render
  const columns = [
    {
      key: "customer_name",
      title: "CUSTOMER NAME",
      render: (value, item) => (
        <button
          onClick={() => handleCustomerNameClick(item)}
          className="font-medium text-gray-900 dark:text-white hover:text-gray-700 dark:hover:text-gray-300 text-left"
        >
          {value}
        </button>
      ),
    },
    ...(showAgentNameColumn
      ? [
          {
            key: "agent_name",
            title: "OFFICE NAME",
            render: (value) => (
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {value || "N/A"}
              </span>
            ),
          },
        ]
      : []),
    {
      key: "followup_date",
      title: "SCHEDULED DATE",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value ? new Date(value).toLocaleDateString("en-GB") : "-"}
        </span>
      ),
    },
    {
      key: "date_completed",
      title: "COMPLETED ON",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value ? new Date(value).toLocaleDateString("en-GB") : "-"}
        </span>
      ),
    },
    ...(activeStatistic === "cancelled"
      ? [
          {
            key: "cancelled_at",
            title: "CANCELLED ON",
            render: (value) => (
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {value ? new Date(value).toLocaleDateString("en-GB") : "-"}
              </span>
            ),
          },
        ]
      : []),
    {
      key: "status",
      title: "STATUS",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
    // {
    //   key: "anchorName",
    //   title: "ANCHOR NAME",
    //   render: (value) => (
    //     <span className="text-sm text-gray-600 dark:text-gray-400">
    //       {value || "N/A"}
    //     </span>
    //   ),
    // },
    {
      key: "assignedOfficer",
      title: "ASSIGNED OFFICER",
      render: (value, item) => (
        <div className="text-sm text-gray-600 dark:text-gray-400">
          <div className="font-medium">{value}</div>
          {item.assignedOfficerCode && item.assignedOfficerCode !== "N/A" && (
            <div className="text-xs text-gray-500 dark:text-gray-500">
              {item.assignedOfficerCode}
            </div>
          )}
        </div>
      ),
    },
    {
      key: "followUpType",
      title: "FOLLOW-UP TYPE",
      render: (value) => {
        const getTypeColor = (type) => {
          switch (type) {
            case "Phone Call":
              return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
            case "Site Visit":
              return "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400";
            default:
              return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
          }
        };

        const getTypeIcon = (type) => {
          switch (type) {
            case "Phone Call":
              return <Phone size={12} className="mr-1" />;
            case "Site Visit":
              return <MapPin size={12} className="mr-1" />;
            default:
              return null;
          }
        };

        return (
          <span
            className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${getTypeColor(
              value
            )}`}
          >
            {getTypeIcon(value)}
            {value}
          </span>
        );
      },
    },

    {
      key: "date",
      title: "SCHEDULED DATE",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
    {
      key: "status",
      title: "STATUS",
      render: (value) => {
        const getStatusColor = (status) => {
          switch (status) {
            case "Pending":
              return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
            case "Completed":
              return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
            case "Canceled":
              return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
            default:
              return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
          }
        };

        return (
          <span
            className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(
              value
            )}`}
          >
            {value}
          </span>
        );
      },
    },
    {
      key: "createdDate",
      title: "CREATED DATE",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
  ];

  // Fetch data for a specific tab type
  const fetchTabData = async (statisticKey) => {
    setLoading(true);

    try {
      // Fetch data from API with type parameter
      console.log(`Making API call with type parameter: ${statisticKey}`);
      const data = await followUpsService.getFilteredFollowUps(statisticKey);
      console.log(
        `Received ${data.length} items for type: ${statisticKey}`,
        data
      );

      // Set the data directly (assuming it's already formatted)
      setFollowUps(data);
    } catch (error) {
      console.error("Error fetching filtered follow-ups:", error);
      setError(error.message);
      toast.error(error.message || "Failed to load follow-ups data");
    } finally {
      setLoading(false);
    }
  };

  // Handle statistic tab change
  const handleStatisticChange = async (statisticKey) => {
    console.log(
      `handleStatisticChange called with: ${statisticKey}, current activeStatistic: ${activeStatistic}`
    );

    // Prevent unnecessary updates if already on the same tab
    if (statisticKey === activeStatistic) {
      console.log(`Already on ${statisticKey} tab, skipping update`);
      return;
    }

    setActiveStatistic(statisticKey);

    // Update URL parameter
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("type", statisticKey);
    setSearchParams(newSearchParams);
    console.log(`URL updated to: /leads/follow-ups?type=${statisticKey}`);

    // Fetch data for this tab
    await fetchTabData(statisticKey);
  };

  // Load data on component mount and when activeStatistic changes
  useEffect(() => {
    fetchStatisticsCounts();
    fetchTabData(activeStatistic);
  }, [activeStatistic]);

  // Customer name click handler
  const handleCustomerNameClick = (item) => {
    setSelectedItem(item);
    // This will be handled by the DataTable's profileForm prop
  };

  // Reschedule handler
  const handleReschedule = (item) => {
    setSelectedItem(item);
    setShowRescheduleModal(true);
  };

  // Reschedule success handler
  const handleRescheduleSuccess = (followUpId, newDate) => {
    // Update the follow-up in the state
    setFollowUps((prevFollowUps) =>
      prevFollowUps.map((followUp) =>
        followUp.id === followUpId
          ? { ...followUp, followup_date: newDate }
          : followUp
      )
    );

    setSuccessMessage("Follow-up rescheduled successfully!");
    setShowSuccessModal(true);
    setShowRescheduleModal(false);
  };

  // Cancel handler
  const handleCancel = async (item) => {
    const confirmed = window.confirm(
      `Are you sure you want to cancel the follow-up for ${item.customer_name}?`
    );

    if (!confirmed) return;

    try {
      await followUpsService.cancel(item.id);

      // Remove from current state
      setFollowUps((prevFollowUps) =>
        prevFollowUps.filter((followUp) => followUp.id !== item.id)
      );

      // Update statistics counts
      setStatisticsCounts((prevCounts) => ({
        ...prevCounts,
        [activeStatistic]: Math.max(0, prevCounts[activeStatistic] - 1),
        cancelled: prevCounts.cancelled + 1,
      }));

      setSuccessMessage("Follow-up cancelled successfully!");
      setShowSuccessModal(true);
    } catch (error) {
      console.error("Error cancelling follow-up:", error);
      setErrorMessage(
        error.response?.data?.message || "Failed to cancel follow-up"
      );
      setShowErrorModal(true);
    }
  };

  // Handle status update
  const handleStatusUpdate = async (followUpId, newStatus) => {
    try {
      console.log(`Updating follow-up ${followUpId} status to ${newStatus}`);

      const response = await followUpsService.updateStatus(
        followUpId,
        newStatus
      );

      // Update the follow-up in state
      setFollowUps((prevFollowUps) =>
        prevFollowUps.map((followUp) =>
          followUp.id === followUpId
            ? {
                ...followUp,
                status:
                  newStatus.charAt(0).toUpperCase() +
                  newStatus.slice(1).toLowerCase(),
              }
            : followUp
        )
      );

      // Refresh data to get updated information
      await fetchFollowUps();

      toast.success(`Follow-up status updated to ${newStatus}`);
      return true;
    } catch (error) {
      console.error("Error updating follow-up status:", error);
      toast.error(error.message || "Failed to update follow-up status");
      return false;
    }
  };

  // Handle call activity creation from follow-up
  const handleCallSubmit = async (callData, leadItem, error) => {
    console.log("Call submitted from follow-up:", {
      callData,
      leadItem,
      error,
    });

    // If call was created successfully (no error), handle follow-up completion
    if (!error && callData && leadItem) {
      console.log("Call created successfully from follow-up");

      // Ask user if they want to mark follow-up as completed
      const shouldComplete = window.confirm(
        "Call activity created successfully! Would you like to mark this follow-up as completed?"
      );

      if (shouldComplete) {
        try {
          await handleStatusUpdate(selectedItem.id, "completed");
          toast.success(
            "Call activity created and follow-up marked as completed!"
          );
        } catch (statusError) {
          console.error("Error updating follow-up status:", statusError);
          toast.success("Call activity created successfully!");
          toast.error("Failed to update follow-up status");
        }
      } else {
        toast.success("Call activity created successfully!");
      }

      // Refresh follow-ups data to get updated information
      await fetchFollowUps();
    } else if (error) {
      console.error("Error creating call activity:", error);
      toast.error("Failed to create call activity");
    }
  };

  // Handle visit activity creation from follow-up
  const handleVisitSubmit = async (visitData, leadItem, error) => {
    console.log("Visit submitted from follow-up:", {
      visitData,
      leadItem,
      error,
    });

    // If visit was created successfully (no error), handle follow-up completion
    if (!error && visitData && leadItem) {
      console.log("Visit created successfully from follow-up");

      // Ask user if they want to mark follow-up as completed
      const shouldComplete = window.confirm(
        "Visit activity created successfully! Would you like to mark this follow-up as completed?"
      );

      if (shouldComplete) {
        try {
          await handleStatusUpdate(selectedItem.id, "completed");
          toast.success(
            "Visit activity created and follow-up marked as completed!"
          );
        } catch (statusError) {
          console.error("Error updating follow-up status:", statusError);
          toast.success("Visit activity created successfully!");
          toast.error("Failed to update follow-up status");
        }
      } else {
        toast.success("Visit activity created successfully!");
      }

      // Refresh follow-ups data to get updated information
      await fetchFollowUps();
    } else if (error) {
      console.error("Error creating visit activity:", error);
      toast.error("Failed to create visit activity");
    }
  };

  // Handle custom actions (call, visit, reschedule, cancel)
  const handleCustomAction = async (action, followUp) => {
    console.log(`=== CUSTOM ACTION: ${action} ===`);
    console.log("Follow-up data:", followUp);
    console.log("================================");

    setSelectedItem(followUp);

    switch (action) {
      case "call":
      case "visit":
        // These will be handled by the modal forms
        break;
      case "reschedule":
        handleReschedule(followUp);
        break;
      case "cancel":
        handleCancel(followUp);
        break;
      default:
        console.log(`Unhandled custom action: ${action}`);
    }
  };

  const handleEditSubmit = (formData) => {
    console.log("Updating follow-up:", formData);
    // API Integration: PUT /api/follow-ups/{id}
    // const response = await fetch(`/api/follow-ups/${formData.id}`, {
    //   method: 'PUT',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(formData)
    // });
  };

  const handleView = async (followUp) => {
    setSelectedItem(followUp); // Set selected item for view
    console.log("View follow-up:", followUp);

    try {
      // API Integration: GET /api/follow-ups/{id}
      // Uncomment when API is ready
      // const response = await fetch(`/api/follow-ups/${followUp.id}`);

      // if (!response.ok) {
      //   throw new Error('Failed to fetch follow-up details');
      // }

      // const detailedFollowUp = await response.json();
      // console.log("Detailed follow-up data:", detailedFollowUp);

      // For now, simulate API call
      console.log("Simulated API call for viewing follow-up:", followUp.id);

      // Here you would typically navigate to view page or show view modal
      // For example: navigate(`/follow-ups/${followUp.id}`);
    } catch (error) {
      console.error("Error fetching follow-up details:", error);
      // Here you would typically show an error notification
    }
  };

  // Multiselect delete handler
  const handleMultiDelete = (selectedIds) => {
    console.log("Multi-delete called with IDs:", selectedIds);
    // TODO: Implement actual API call for bulk delete
    console.log(`Would delete ${selectedIds.length} selected follow-ups`);
  };

  const handleLoadMore = () => {
    setLoadingMore(true);
    // Simulate API call
    setTimeout(() => {
      setLoadingMore(false);
      console.log("Load more follow-ups");
    }, 2000);
  };

  return (
    <PrivateLayout pageTitle="Follow-ups">
      <div className="">
        {/* Data Table */}
        <DataTable
          columns={columns}
          data={followUps}
          searchPlaceholder="Search follow-ups..."
          onView={handleView}
          actions={[
            { name: "call", icon: Phone, is_visible: () => true },
            { name: "visit", icon: MapPin, is_visible: () => true },
            { name: "reschedule", icon: Calendar, is_visible: () => true },
            { name: "cancel", icon: X, is_visible: () => true },
          ]}
          // Disable multiselect
          allowMultiSelect={false}
          loading={loading}
          loadingMore={loadingMore}
          loadMoreText="Load More Follow-ups"
          onLoadMore={handleLoadMore}
          showLoadMore={true}
          highlightField="status"
          onStatusUpdate={handleStatusUpdate}
          onCustomAction={handleCustomAction}
          // Statistics functionality
          showStatistics={true}
          statistics={getStatistics()}
          activeStatistic={activeStatistic}
          onStatisticChange={handleStatisticChange}
          // Hide create button, show export and print
          showCreate={false}
          showImportExport={true}
          showImport={false}
          highlightColors={{
            Pending:
              "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",
            Completed:
              "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
            Canceled:
              "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400",
          }}
          // Multiselect functionality
          allowMultiSelect={true}
          onMultiDelete={handleMultiDelete}
          // Data count
          showDataCount={true}
          dataCountLabel="follow-ups"
          // Filters
          showFilters={true}
          filterConfig={filterConfig}
          onFilterChange={handleFilterChange}
          onClearFilters={handleClearFilters}
          // Date range filter
          showDateRangeFilter={true}
          dateRangeField="date"
          fromDate={fromDate}
          toDate={toDate}
          onFromDateChange={handleFromDateChange}
          onToDateChange={handleToDateChange}
          // Modal forms
          callForm={({ item, onClose }) => (
            <FollowUpCallForm
              item={item}
              onClose={onClose}
              onSubmit={handleCallSubmit}
            />
          )}
          visitForm={({ item, onClose }) => (
            <FollowUpVisitForm
              item={item}
              onClose={onClose}
              onSubmit={handleVisitSubmit}
            />
          )}
          profileForm={({ item, onClose }) => (
            <FollowUpLeadProfile item={item} onClose={onClose} />
          )}
          callModalTitle={
            selectedItem ? `Call ${selectedItem.customer_name}` : "Make Call"
          }
          visitModalTitle={
            selectedItem
              ? `Visit ${selectedItem.customer_name}`
              : "Schedule Visit"
          }
          modalSize="lg"
          callModalSize="lg"
          visitModalSize="lg"
        />

        {/* Reschedule Modal */}
        <RescheduleModal
          isOpen={showRescheduleModal}
          onClose={() => setShowRescheduleModal(false)}
          followUp={selectedItem}
          onRescheduleSuccess={handleRescheduleSuccess}
        />

        {/* Success Modal */}
        <SuccessModal
          isOpen={showSuccessModal}
          onClose={() => setShowSuccessModal(false)}
          title="Success"
          message={successMessage}
        />

        {/* Error Modal */}
        <ErrorModal
          isOpen={showErrorModal}
          onClose={() => setShowErrorModal(false)}
          title="Error"
          message={errorMessage}
        />
      </div>
    </PrivateLayout>
  );
};

export default FollowUps;
