import { useState, useEffect } from "react";
import { Minus } from "lucide-react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import CustomerForm from "../components/forms/CustomerForm";
import CallForm from "../components/forms/CallForm";
import LeadProfile from "../components/forms/LeadProfile";
import DeleteConfirmation from "../components/forms/DeleteConfirmation";
import {
  customersService,
  formatCustomersResponse,
  formatCustomersForTable,
} from "../services/customersService";
import { downloadCustomersTemplate } from "../utils/excelUtils";
import { leadsService } from "../services/leadsService";
import { toast } from 'react-toastify';
import { hasPermission } from "../utils/permissionUtils";

const Customers = () => {
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [customers, setCustomers] = useState([]);
  const [error, setError] = useState(null);

  // For anchor selection in import
  const [anchors, setAnchors] = useState([]);

  // Define columns of the table here and what to render
  const columns = [
    {
      key: "accountNumber",
      title: "ACCOUNT NUMBER",
      render: (value) => (
        <span className="font-bold" style={{ color: "#7e7e7e" }}>
          {value && value !== "N/A" ? (
            value
          ) : (
            <Minus size={16} className="text-gray-400" />
          )}
        </span>
      ),
    },
    {
      key: "name",
      title: "NAME",
      render: (value) => (
        <span className="font-medium" style={{ color: "#7e7e7e" }}>
          {value || "Unknown Customer"}
        </span>
      ),
    },
    {
      key: "phoneNumber",
      title: "PHONE NUMBER",
      render: (value) => (
        <span className="text-sm" style={{ color: "#7e7e7e" }}>
          {value && value !== "N/A" ? (
            value
          ) : (
            <Minus size={16} className="text-gray-400" />
          )}
        </span>
      ),
    },
    {
      key: "branch",
      title: "BRANCH",
      render: (value) => (
        <span className="text-sm" style={{ color: "#7e7e7e" }}>
          {value && value !== "N/A" ? (
            value
          ) : (
            <Minus size={16} className="text-gray-400" />
          )}
        </span>
      ),
    },
    {
      key: "createdAt",
      title: "CONVERTED AT",
      render: (value) => (
        <span className="text-sm" style={{ color: "#7e7e7e" }}>
          {value && value !== "N/A" ? (
            value
          ) : (
            <Minus size={16} className="text-gray-400" />
          )}
        </span>
      ),
    },
  ];

  // Fetch customers from API
  const fetchCustomers = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log("Fetching customers from /customers endpoint...");

      const response = await customersService.getAll();
      console.log("Customers API response:", response);

      const formattedCustomers = formatCustomersResponse(response);
      const tableReadyCustomers = formatCustomersForTable(formattedCustomers);

      console.log("Formatted customers for table:", tableReadyCustomers);
      setCustomers(tableReadyCustomers);
    } catch (error) {
      console.error("Error fetching customers:", error);
      setError(error.response?.data?.message || "Failed to load customers");
      setCustomers([]);
    } finally {
      setLoading(false);
    }
  };

  // Fetch anchors for import functionality
  const fetchAnchors = async () => {
    try {
      // Use leads service to get anchors since customers import uses same endpoint
      const response = await leadsService.getAnchors();
      console.log("Anchors fetched for customers import:", response);
      setAnchors(response || []);
    } catch (error) {
      console.error("Error fetching anchors for customers import:", error);
      setAnchors([]);
    }
  };

  // Fetch customers and anchors on component mount
  useEffect(() => {
    fetchCustomers();
    fetchAnchors();
  }, []);

  // Form submission handlers (non-functional for now)
  const handleCreateSubmit = (formData) => {
    console.log("Creating customer:", formData);
    console.log("Create functionality not implemented yet");
    // TODO: Implement customer creation API call
  };

  const handleEditSubmit = (formData) => {
    console.log("Updating customer:", formData);
    console.log("Edit functionality not implemented yet");
    // TODO: Implement customer update API call
  };

  const handleDeleteConfirm = (customer) => {
    console.log("Deleting customer:", customer);
    console.log("Delete functionality not implemented yet");
    // TODO: Implement customer deletion API call
  };

  // Import/Export handlers
  const handleExport = () => {
    console.log("Exporting customers data...");
    // Here you would typically generate and download the export file
    // For now, we'll just log the action
  };

  // Handle call action (same as leads)
  const handleCallSubmit = async (callData, customer, error) => {
    console.log("Call submitted:", { callData, customer, error });

    // If call was created successfully (no error), update the state
    if (!error && callData && customer) {
      console.log("Call created successfully, updating customer state...");

      // Update the customers state to reflect new call activity
      setCustomers(prevCustomers =>
        prevCustomers.map(customerItem => {
          if (customerItem.id === customer.id) {
            return {
              ...customerItem,
              // Add any call-related fields if needed
              lastInteraction: {
                interaction_type: "call",
                date: new Date().toISOString(),
              },
            };
          }
          return customerItem;
        })
      );

      console.log("Customer state updated after call creation");
    }
  };

  const handleImport = async (file, selectedAnchor = null) => {
    try {
      console.log("Importing customers from file:", file.name);
      console.log("Selected anchor:", selectedAnchor);

      // Use the leads service import endpoint for customers
      // The backend should handle customers import through the same endpoint
      const result = await leadsService.importFromFile(file, selectedAnchor);

      console.log("Import result:", result);

      if (result.success) {
        toast.success(`Successfully imported ${result.imported || 0} customers!`);

        // Refresh the customers data
        await fetchCustomers();
      } else {
        toast.error(result.message || "Failed to import customers");
      }
    } catch (error) {
      console.error("Error importing customers:", error);
      toast.error(error.message || "Failed to import customers");
    }
  };

  const handleDownloadTemplate = () => {
    try {
      downloadCustomersTemplate()
      toast.success("Customers template downloaded successfully!");
    } catch (error) {
      console.error("Error downloading customers template:", error);
      toast.error("Failed to download customers template");
    }
  };

  const handleView = (customer) => {
    console.log("View customer:", customer);
    // The profile modal will be opened automatically by DataTable's onView handler
    // when user clicks on a customer row
  };

  const handleLoadMore = () => {
    setLoadingMore(true);
    // Simulate API call
    setTimeout(() => {
      setLoadingMore(false);
      console.log("Load more customers");
    }, 2000);
  };

  return (
    <PrivateLayout perm_required={["view.all.customers","view.my.customers","OR"]}>
      <div className="">
        {/* Data Table */}
        <DataTable
          columns={columns}
          data={customers}
          searchPlaceholder="Search customers..."
          // addButtonText="New Customer"  // Commented out - button not visible
          onView={handleView}
          actions={[]}
          loading={loading}
          loadingMore={loadingMore}
          loadMoreText="Load More Customers"
          onLoadMore={handleLoadMore}
          showLoadMore={false}
          highlightField="status"
          highlightColors={{
            Active:
              "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
            Inactive:
              "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400",
            Pending:
              "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",
            Suspended:
              "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400",
          }}
          // Modal forms
          // createForm={({ onClose }) => (
          //   <CustomerForm onClose={onClose} onSubmit={handleCreateSubmit} />
          // )}  // Commented out - no create button
          editForm={({ item, onClose }) => (
            <CustomerForm
              item={item}
              onClose={onClose}
              onSubmit={handleEditSubmit}
            />
          )}
          deleteForm={({ item, onClose }) => (
            <DeleteConfirmation
              item={item}
              onClose={onClose}
              onConfirm={handleDeleteConfirm}
              itemName="Customer"
            />
          )}
          callForm={({ item, onClose }) => (
            <CallForm
              item={item}
              onClose={onClose}
              onSubmit={handleCallSubmit}
            />
          )}
          profileForm={({ item, onClose }) => (
            <LeadProfile
              item={item}
              onClose={onClose}
            />
          )}
          // createModalTitle="Create New Customer"  // Commented out - no create button
          editModalTitle="Edit Customer"
          deleteModalTitle=""
          callModalTitle="Make Call"
          profileModalTitle="Customer Profile"
          modalSize="lg"
          deleteModalSize="sm"
          callModalSize="lg"
          profileModalSize="xl"
          // Import/Export functionality
          showExportPrint={hasPermission("view.all.customers","view.my.customers","OR") && true}
          allowMultiSelect={hasPermission("customers.delete") && true}
          showImport={false}
          showCreateButton = {false}
          onExport={handleExport}
          onImport={handleImport}
          importModalTitle="Import Customers"
          importTemplateFileName="Customers-Template.xlsx"
          importAcceptedFileTypes=".xlsx,.xls,.csv"
          onDownloadTemplate={handleDownloadTemplate}
          // Anchor selection for import
          showAnchorSelection={true}
          anchors={anchors}
          isLeadsImport={true}
        />
      </div>
    </PrivateLayout>
  );
};

export default Customers;
