import { useState, useEffect, useCallback } from "react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import DeleteConfirmation from "../components/forms/DeleteConfirmation";
import CustomerFeedbackCategoryForm from "../components/forms/CustomerFeedbackCategoryForm";
import SuccessModal from "../components/modals/SuccessModal";
import { customerFeedbackCategoriesService } from "../services/customerFeedbackCategoriesService";
import { formatDateTimeDisplay } from "../utils/dateUtils";
import { hasPermission } from "../utils/permissionUtils";

const CustomerFeedbackCategories = () => {
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [categories, setCategories] = useState([]);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");

  // Define columns of the table here and what to render
  const columns = [
    {
      key: "name",
      title: "NAME",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "added_on",
      title: "ADDED ON",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {formatDateTimeDisplay(value)}
        </span>
      ),
    },
    {
      key: "added_by",
      title: "ADDED BY",
      render: (value) => (
        <span className="inline-flex px-2 py-1 font-semibold rounded-full dark:bg-green-900/20">
          {value || "N/A"}
        </span>
      ),
    },
  ];

  // Fetch categories data
  const fetchCategories = useCallback(async () => {
    setLoading(true);
    try {
      const data = await customerFeedbackCategoriesService.getAll();
      // Ensure data is always an array
      setCategories(Array.isArray(data.data) ? data.data : []);
    } catch (error) {
      console.error("Error fetching customer feedback categories:", error);
      // Set empty array on error to prevent filter issues
      setCategories([]);
      // Error is handled by the API context interceptor
    } finally {
      setLoading(false);
    }
  }, []);

  // Load categories on component mount
  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  // Form submission handlers
  const handleCreateSubmit = (result) => {
    console.log("Created customer feedback category:", result);
    // Add to state instead of refreshing - access result.data
    setCategories((prev) => [...prev, result.data]);
    setSuccessMessage("Customer feedback category created successfully!");
    setShowSuccessModal(true);
  };

  const handleEditSubmit = (result) => {
    console.log("Updated customer feedback category:", result);
    // Update in state instead of refreshing - access result.data
    setCategories((prev) =>
      prev.map((category) =>
        category.id === result.data.id ? result.data : category
      )
    );
    setSuccessMessage("Customer feedback category updated successfully!");
    setShowSuccessModal(true);
  };

  const handleDeleteConfirm = async (category) => {
    try {
      await customerFeedbackCategoriesService.delete(category.id);
      console.log("Deleted customer feedback category:", category);
      // Remove from state instead of refreshing
      setCategories((prev) => prev.filter((cat) => cat.id !== category.id));
      setSuccessMessage("Customer feedback category deleted successfully!");
      setShowSuccessModal(true);
    } catch (error) {
      console.error("Error deleting customer feedback category:", error);
      // Error is handled by the API context interceptor
    }
  };

  const handleView = (category) => {
    console.log("View customer feedback category:", category);
    // Here you would typically navigate to view page or show view modal
  };

  const handleLoadMore = () => {
    setLoadingMore(true);
    // Simulate API call
    setTimeout(() => {
      setLoadingMore(false);
      console.log("Load more customer feedback categories");
    }, 2000);
  };

  // Export/Print handlers
  const handleExport = () => {
    console.log("Exporting customer feedback categories data");
    // API integration logic here
  };

  const handlePrint = () => {
    console.log("Printing customer feedback categories data");
    // Print functionality here
  };

  return (
    <PrivateLayout perm_required={["customer.feedback.category.view"]}>
      {/* Data Table */}
      <div className="">
        <DataTable
          columns={columns}
          data={categories}
          searchPlaceholder="Search customer feedback categories..."
          addButtonText="New Feedback Category"
          onView={handleView}
          actions={[ 
                      
                      {name:"edit",is_visible:()=> hasPermission("customer.feedback.category.edit")}, 
                      {name:"delete",is_visible:()=> hasPermission("customer.feedback.category.delete")}, 
                    ]}
          loading={loading}
          loadingMore={loadingMore}
          loadMoreText="Load More Categories"
          onLoadMore={handleLoadMore}
          showLoadMore={true}
          // Export functionality - only export and print
          showImportExport={hasPermission("customer.feedback.category.create") && true}
          showCreateButton = {hasPermission("customer.feedback.category.create") && true}
          allowMultiSelect={hasPermission("customer.feedback.category.delete") && true}
          showImport={false}
          onExport={handleExport}
          onPrint={handlePrint}
          // Modal forms
          createForm={({ onClose }) => (
            <CustomerFeedbackCategoryForm
              onClose={onClose}
              onSubmit={handleCreateSubmit}
            />
          )}
          editForm={({ item, onClose }) => (
            <CustomerFeedbackCategoryForm
              item={item}
              onClose={onClose}
              onSubmit={handleEditSubmit}
            />
          )}
          deleteForm={({ item, onClose }) => (
            <DeleteConfirmation
              item={item}
              onClose={onClose}
              onConfirm={handleDeleteConfirm}
              itemName="Customer Feedback Category"
            />
          )}
          createModalTitle="Create New Customer Feedback Category"
          editModalTitle="Edit Customer Feedback Category"
          deleteModalTitle=""
          modalSize="lg"
          deleteModalSize="sm"
        />
      </div>

      {/* Success Modal */}
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        title="Success"
        message={successMessage}
      />
    </PrivateLayout>
  );
};

export default CustomerFeedbackCategories;
