import { useState, useEffect } from "react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import LoanClientForm from "../components/forms/LoanClientForm";
import LoanCallForm from "../components/forms/LoanCallForm";
import DeleteConfirmation from "../components/forms/DeleteConfirmation";
import LoanClientProfile from "../components/forms/LoanClientProfile";
import { loanService, formatLoansForTable } from "../services/loanService";
import { downloadLoanCustomersTemplate } from "../utils/excelUtils";
import { toast } from "react-toastify";
import { Loader2 } from "lucide-react";

const LoanHitlist = () => {
  // Loading states
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);

  // Data state
  const [loans, setLoans] = useState([]);

  // Modal states
  const [selectedItem, setSelectedItem] = useState(null);

  // Filter states
  const [fromDate, setFromDate] = useState("");
  const [toDate, setToDate] = useState("");



  // Fetch loans from API
  const fetchLoans = async () => {
    try {
      setLoading(true);
      console.log("Fetching loans from /loans endpoint...");
      const response = await loanService.getAll();
      console.log("Raw API response:", response);

      const formattedLoans = formatLoansForTable(response);
      console.log("Formatted loans for table:", formattedLoans);

      setLoans(formattedLoans);
    } catch (error) {
      console.error("Error fetching loans:", error);
      setLoans([]); // Set empty array on error
      toast.error("Failed to load loans data");
    } finally {
      setLoading(false);
    }
  };

  // Fetch loans on component mount
  useEffect(() => {
    fetchLoans();
  }, []);

  // Handle load more (for pagination)
  const handleLoadMore = () => {
    setLoadingMore(true);
    // Simulate loading more data
    setTimeout(() => {
      setLoadingMore(false);
    }, 1000);
  };

  // Handle create loan client
  const handleCreateSubmit = async (formData) => {
    try {
      console.log("Creating new loan client:", formData);
      
      // Clean up the form data for API creation
      // Remove fields that the API doesn't expect
      const { 
        employerName, 
        ...cleanFormData 
      } = formData;
      
      // Only include employerId if it's a valid UUID
      if (cleanFormData.employerId && cleanFormData.employerId.trim()) {
        // Validate UUID format
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        if (!uuidRegex.test(cleanFormData.employerId)) {
          cleanFormData.employerId = ""; // Remove invalid employerId
        }
      } else {
        delete cleanFormData.employerId; // Remove empty employerId
      }
      
      // Add creation date for new loan clients
      const loanClientData = {
        ...cleanFormData,
        createdDate: new Date().toISOString()
      };
      
      console.log("Cleaned form data for API creation:", loanClientData);
      
      const newLoanClient = await loanService.create(loanClientData);

      // Refresh the loans data to get the latest list
      await fetchLoans();

      toast.success("Loan customer created successfully!");
      return true;
    } catch (error) {
      console.error("Error creating loan client:", error);
      const errorMessage = error.response?.data?.message || error.message || "Failed to create loan customer";
      toast.error(errorMessage);
      return false;
    }
  };

  // Handle edit loan client
  const handleEditSubmit = async (formData, item) => {
    try {
      console.log("Updating loan client:", formData);
      console.log("Item being updated:", item);
      
      // Clean up the form data for API update
      // Remove fields that the API doesn't expect or that should not be sent
      const { 
        id, 
        employerName, 
        ...cleanFormData 
      } = formData;
      
      // Only include employerId if it's a valid UUID
      if (cleanFormData.employerId && cleanFormData.employerId.trim()) {
        // Validate UUID format
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        if (!uuidRegex.test(cleanFormData.employerId)) {
          cleanFormData.employerId = ""; // Remove invalid employerId
        }
      } else {
        delete cleanFormData.employerId; // Remove empty employerId
      }
      
      console.log("Cleaned form data for API update:", cleanFormData);
      
      const updatedLoanClient = await loanService.update(item.id, cleanFormData);

      // Refresh the loans data to get the latest list
      await fetchLoans();

      toast.success("Loan customer updated successfully!");
      return true;
    } catch (error) {
      console.error("Error updating loan client:", error);
      const errorMessage = error.response?.data?.message || error.message || "Failed to update loan customer";
      toast.error(errorMessage);
      return false;
    }
  };

  // Handle delete loan client
  const handleDeleteConfirm = async (loanClient) => {
    try {
      console.log("Deleting loan client:", loanClient);
      const success = await loanService.delete(loanClient.id);

      if (success) {
        // Remove the loan client from state
        setLoans(prevLoans => prevLoans.filter(l => l.id !== loanClient.id));
        toast.success(`Loan client "${loanClient.name}" deleted successfully!`);
      } else {
        throw new Error("Delete operation did not return success status");
      }
    } catch (error) {
      console.error("Error deleting loan client:", error);
      toast.error(error.message || "Failed to delete loan client");
    }
  };

  // Handle call action
  const handleCallSubmit = async (callData, selectedItem, files = []) => {
    try {
      console.log("Creating call for loan:", callData);
      console.log("Selected item:", selectedItem);
      console.log("Files:", files);

      // Create the call using the loan service
      const newCall = await loanService.calls.create(callData, files);

      // Update the loan activities count and last interaction for this loan client
      setLoans(prevLoans =>
        prevLoans.map(loan =>
          loan.id === selectedItem.id
            ? {
                ...loan,
                calls: (loan.calls || 0) + 1,
                lastInteraction: {
                  activity_type: "call",
                  date_time: new Date().toISOString()
                },
                lastInteractionType: "call"
              }
            : loan
        )
      );

      toast.success("Call logged successfully!");
      return true;
    } catch (error) {
      console.error("Error creating call:", error);
      toast.error(error.message || "Failed to log call");
      return false;
    }
  };

  // Handle view loan
  const handleView = (loan) => {
    console.log("View loan:", loan);
    setSelectedItem(loan);
  };



  // Handle filter changes
  const handleFilterChange = (filterKey, value) => {
    console.log(`Filter changed: ${filterKey} = ${value}`);
    // Implement filter logic here
  };

  const handleClearFilters = () => {
    console.log("Clearing all filters");
    // Implement clear filters logic here
  };

  const handleFromDateChange = (date) => {
    setFromDate(date);
  };

  const handleToDateChange = (date) => {
    setToDate(date);
  };

  // Handle import functionality with enhanced response handling
  const handleImport = async (file, selectedAnchor = null) => {
    try {
      console.log("=== LOAN HITLIST IMPORT HANDLER (NEW FORMAT) ===");
      console.log("Raw file object:", file);
      console.log("Selected anchor:", selectedAnchor);
      console.log("File properties:");
      console.log("  - name:", file.name);
      console.log("  - size:", file.size);
      console.log("  - type:", file.type);
      console.log("  - lastModified:", file.lastModified);
      console.log("===============================================");

      // Use the enhanced import service with anchor support
      const result = await loanService.importFromFile(file, selectedAnchor);
      console.log("Import result:", result);

      // Refresh the loan clients list
      await fetchLoans();

      // Show detailed success message
      const successCount = result.imported || result.successfulCreations || 0;
      const failedCount = result.failed || result.failedCreations || 0;

      let message = `Successfully imported ${successCount} loan customers`;
      if (failedCount > 0) {
        message += ` (${failedCount} failed)`;
      }

      toast.success(message);

      // Show additional details if available
      if (result.details?.anchorCreationResults?.length > 0) {
        const anchorResults = result.details.anchorCreationResults;
        const createdAnchors = anchorResults.filter(r => r.created).length;
        if (createdAnchors > 0) {
          toast.info(`Created ${createdAnchors} new anchors during import`);
        }
      }
    } catch (error) {
      console.error("Error importing loan clients:", error);

      // Enhanced error handling for new format
      let errorMessage = "Failed to import loan customers";

      if (error.message) {
        errorMessage = error.message;
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      toast.error(errorMessage);

      // Show additional error details if available
      if (error.details?.errors?.length > 0) {
        const errorCount = error.details.errors.length;
        toast.error(`${errorCount} validation errors occurred during import`);
      }
    }
  };

  // Handle download template (loan customers template)
  const handleDownloadTemplate = () => {
    try {
      downloadLoanCustomersTemplate()
      toast.success("Loan customers template downloaded successfully!");
    } catch (error) {
      console.error("Error downloading loan customers template:", error);
      toast.error("Failed to download loan customers template");
    }
  };

  // Define table columns (similar to leads structure)
  const columns = [
    {
      key: "anchor",
      title: "ANCHOR",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
    {
      key: "name",
      title: "NAME",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "phoneNumber",
      title: "PHONE NUMBER",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
    {
      key: "calls",
      title: "CALLS",
      render: (value) => (
        <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
          {value}
        </span>
      ),
    },
    {
      key: "lastInteraction",
      title: "LAST INTERACTION",
      render: (value, row) => {
        // Handle null, undefined, or "Never" cases
        if (
          !value ||
          value === "Never" ||
          (typeof value === "object" && !value.date_time && !value.date)
        ) {
          return (
            <span className="text-sm" style={{ color: "#7e7e7e" }}>
              Never
            </span>
          );
        }

        const formatInteractionDate = (dateString) => {
          const date = new Date(dateString);
          const time = date.toLocaleTimeString("en-US", {
            hour: "numeric",
            minute: "2-digit",
            hour12: true,
          });
          const dateFormatted = date.toLocaleDateString("en-US", {
            day: "numeric",
            month: "short",
            year: "numeric",
          });
          return `${time} ${dateFormatted}`;
        };

        // Handle new backend format: { activity_type: "call", date_time: "2025-07-30T10:08:00.763Z" }
        let interactionType = "call"; // default
        let dateToFormat = value;

        if (typeof value === "object" && value.activity_type && value.date_time) {
          // New backend format
          interactionType = value.activity_type;
          dateToFormat = value.date_time;
        } else if (typeof value === "object" && value.interaction_type && value.date) {
          // Legacy format support
          interactionType = value.interaction_type;
          dateToFormat = value.date;
        } else if (typeof value === "string") {
          // Fallback for old string format, try to get type from row
          interactionType = row.lastInteractionType || "call";
          dateToFormat = value;
        }

        return (
          <div className="flex flex-col">
            <span
              className="inline-flex px-2 py-1 text-xs font-semibold rounded mb-1 w-fit"
              style={{
                backgroundColor:
                  interactionType === "visit" ? "#fff6e0" : "#e0f2ff",
                color: interactionType === "visit" ? "#ffb800" : "#369dc9",
              }}
            >
              {interactionType}
            </span>
            <span className="text-sm" style={{ color: "#7e7e7e" }}>
              {formatInteractionDate(dateToFormat)}
            </span>
          </div>
        );
      },
    },
    {
      key: "officer",
      title: "OFFICER",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
  ];

  // Filter configuration (similar to leads)
  const filterConfig = [
    {
      key: "type",
      label: "Type",
      type: "select",
      options: [
        { value: "", label: "All Types" },
        { value: "new", label: "New" },
        { value: "existing", label: "Existing" },
      ],
    },
  ];

  return (
    <PrivateLayout pageTitle="Loan - Hit List">
      <div className="">
        <DataTable
          columns={columns}
          data={loans}
          searchPlaceholder="Search loan customers..."
          addButtonText="New Hit"
          onView={handleView}
          actions={[
            "call",
            "edit",
            "profile",
            "delete",
          ]}
          loading={loading}
          loadingMore={loadingMore}
          loadMoreText="Load More Loan Customers"
          onLoadMore={handleLoadMore}
          showLoadMore={true}
          highlightField="type"
          highlightColors={{
            New: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",
            Existing: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
          }}
          // Data count
          showDataCount={true}
          dataCountLabel="loan customers"
          // Filters
          showFilters={true}
          filterConfig={filterConfig}
          onFilterChange={handleFilterChange}
          onClearFilters={handleClearFilters}
          // Date range filter
          showDateRangeFilter={true}
          dateRangeField="created_at"
          fromDate={fromDate}
          toDate={toDate}
          onFromDateChange={handleFromDateChange}
          onToDateChange={handleToDateChange}
          // Modal forms
          createForm={({ onClose }) => (
            <LoanClientForm onClose={onClose} onSubmit={handleCreateSubmit} />
          )}
          editForm={({ item, onClose }) => (
            <LoanClientForm
              item={item}
              onClose={onClose}
              onSubmit={handleEditSubmit}
              initialAnchorRelationship={
                item?.anchor_relationship_id && item?.anchor_relationship_name
                  ? {
                      id: item.anchor_relationship_id,
                      name: item.anchor_relationship_name,
                    }
                  : null
              }
              initialCustomerCategory={
                item?.customer_category
                  ? {
                      id: item.customer_category.id,
                      name: item.customer_category.name,
                    }
                  : null
              }
              initialIsicSector={
                item?.isic_sector
                  ? {
                      id: item.isic_sector.id,
                      name: item.isic_sector.name,
                    }
                  : null
              }
              initialBranch={
                item?.branch
                  ? {
                      id: item.branch.id,
                      name: item.branch.name,
                    }
                  : null
              }
            />
          )}
          deleteForm={({ item, onClose }) => (
            <DeleteConfirmation
              item={item}
              onClose={onClose}
              onConfirm={handleDeleteConfirm}
              itemName="Loan Customer"
            />
          )}
          callForm={({ item, onClose }) => (
            <LoanCallForm
              item={item}
              onClose={onClose}
              onSubmit={handleCallSubmit}
            />
          )}
          profileForm={({ item, onClose }) => (
            <LoanClientProfile
              item={item}
              onClose={onClose}
            />
          )}
          createModalTitle="Create New Loan Customer"
          editModalTitle="Edit Loan Customer"
          deleteModalTitle=""
          callModalTitle="Make Call"
          profileModalTitle="Loan Client Profile"
          modalSize="xl"
          deleteModalSize="sm"
          callModalSize="lg"
          profileModalSize="xl"
          // Import/Export functionality
          showImportExport={true}
          onImport={handleImport}
          importModalTitle="Import Loan Customers"
          importTemplateFileName="Loan-Customers-Template.xlsx"
          importAcceptedFileTypes=".xlsx,.xls,.csv"
          onDownloadTemplate={handleDownloadTemplate}
        />
      </div>
    </PrivateLayout>
  );
};

export default LoanHitlist;
