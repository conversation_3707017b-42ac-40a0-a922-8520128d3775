import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { InputOtp } from "primereact/inputotp";
import { Button } from "primereact/button";
import { Shield, Mail, Phone } from "lucide-react";
import PublicLayout from "../components/layouts/PublicLayout";
import { authService } from "../services/authService";
import {
  transferTempTokensToActual,
  getMFAMethods,
  getUserData,
} from "../utils/cookieUtils";
import logoSmall from "../assets/images/logo.png";
import logoText from "../assets/images/logo-text.png";

const MFAOTPVerification = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [token, setToken] = useState();
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [apiError, setApiError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [method, setMethod] = useState(null);
  const [user, setUser] = useState(null);
  const [timeRemaining, setTimeRemaining] = useState(0);
  const [isExpired, setIsExpired] = useState(false);

  useEffect(() => {
    // Get method and user from navigation state or cookies
    if (location.state?.method && location.state?.user) {
      // Use data from navigation state (fresh from method selection)
      setMethod(location.state.method);
      setUser(location.state.user);

      // Set timer for 5 minutes
      const expirationTime = new Date().getTime() + 5 * 60 * 1000;
      localStorage.setItem("mfaOtpExpirationTime", expirationTime.toString());
    } else {
      // Try to get data from cookies and reconstruct method from stored MFA methods
      const storedMfaMethods = getMFAMethods();
      const storedUser = getUserData();

      if (storedMfaMethods && storedUser) {
        // If we have stored data but no specific method, redirect to method selection
        // This handles the case where user refreshed the OTP page
        navigate("/mfa/select-method");
      } else {
        // If no data available, redirect back to login
        navigate("/login");
      }
    }
  }, [location.state, navigate]);

  // Timer functionality
  useEffect(() => {
    const updateTimer = () => {
      const expirationTime = localStorage.getItem("mfaOtpExpirationTime");
      if (expirationTime) {
        const now = new Date().getTime();
        const timeLeft = parseInt(expirationTime) - now;

        if (timeLeft > 0) {
          setTimeRemaining(timeLeft);
          setIsExpired(false);
        } else {
          setTimeRemaining(0);
          setIsExpired(true);
        }
      }
    };

    // Update timer immediately
    updateTimer();

    // Update timer every second
    const interval = setInterval(updateTimer, 1000);

    return () => clearInterval(interval);
  }, []);

  // Format time remaining
  const formatTime = (milliseconds) => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  };

  // Custom OTP input template (borrowed from reset-verification)
  const customInput = ({ events, props }) => {
    const { invalid, unstyled, ...domProps } = props;

    const handleInputChange = (e) => {
      const value = e.target.value.toUpperCase();
      e.target.value = value;
      if (events.onInput) {
        events.onInput(e);
      }
    };

    return (
      <>
        <input
          {...events}
          {...domProps}
          type="text"
          className="custom-otp-input"
          onInput={handleInputChange}
          style={{ textTransform: "uppercase" }}
        />
        {props.id === 2 && (
          <div className="px-3">
            <i className="pi pi-minus" />
          </div>
        )}
      </>
    );
  };

  const handleSubmit = async () => {
    setApiError("");
    setSuccessMessage("");

    if (!token || token.length !== 6) {
      setApiError("Please enter the complete 6-digit code");
      return;
    }

    setIsLoading(true);

    try {
      // Verify the MFA code
      await authService.verifyMFACode(method.id, token);

      // Transfer temporary tokens to actual tokens
      const transferred = transferTempTokensToActual();

      if (transferred) {
        // Clear the timer since verification was successful
        localStorage.removeItem("mfaOtpExpirationTime");

        // Store user data
        localStorage.setItem("logged_in_user", JSON.stringify(user));

        // Navigate to dashboard
        navigate("/dashboard");
      } else {
        setApiError("Authentication failed. Please try logging in again.");
      }
    } catch (err) {
      let errorMessage = "Invalid verification code. Please try again.";

      if (err.response) {
        const status = err.response.status;
        if (status === 400) {
          errorMessage = "Invalid or expired verification code.";
        } else if (status === 500) {
          errorMessage = "Server error. Please try again later.";
        } else {
          errorMessage =
            err.response.data?.message ||
            "Invalid verification code. Please try again.";
        }
      } else if (err.request) {
        errorMessage =
          "Network error. Please check your connection and try again.";
      } else {
        errorMessage =
          err.message || "Invalid verification code. Please try again.";
      }

      setApiError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendCode = async () => {
    setApiError("");
    setSuccessMessage("");
    setIsResending(true);

    try {
      // Resend MFA code
      await authService.sendMFACode(method.id);

      // Clear the OTP input
      setToken("");

      // Show success message
      setSuccessMessage("Verification code sent successfully!");
      setTimeout(() => {
        setSuccessMessage("");
      }, 3000);

      // Reset timer for new OTP (5 minutes from now)
      const expirationTime = new Date().getTime() + 5 * 60 * 1000;
      localStorage.setItem("mfaOtpExpirationTime", expirationTime.toString());
    } catch (err) {
      setApiError("Failed to resend code. Please try again.");
    } finally {
      setIsResending(false);
    }
  };

  const getMethodIcon = () => {
    switch (method?.method) {
      case "EMAIL":
        return <Mail className="h-8 w-8 text-blue-600 dark:text-blue-400" />;
      case "SMS":
        return <Phone className="h-8 w-8 text-blue-600 dark:text-blue-400" />;
      default:
        return <Shield className="h-8 w-8 text-blue-600 dark:text-blue-400" />;
    }
  };

  const getMethodDescription = () => {
    switch (method?.method) {
      case "EMAIL":
        return `We've sent a verification code to ${method.contact}`;
      case "SMS":
        return `We've sent a verification code to ${method.contact}`;
      default:
        return "We've sent a verification code to your device";
    }
  };

  if (!method || !user) {
    return null;
  }

  return (
    <PublicLayout>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <div className="bg-white dark:bg-gray-800 shadow-lg rounded-lg border-0">
            {/* Logo Section */}
            <div className="flex items-center justify-center h-24 px-4">
              <div className="flex items-center gap-4">
                <span className="h-11 flex-shrink-0">
                  <img
                    className="logo-abbr h-full w-auto"
                    src={logoSmall}
                    alt=""
                  />
                </span>
                <span className="h-10 transition-opacity duration-300">
                  <img
                    className="brand-title h-full w-auto"
                    src={logoText}
                    alt=""
                  />
                </span>
              </div>
            </div>

            {/* Header */}
            <div className="p-6 pb-4">
              <div className="text-center mb-6">
                <div className="mx-auto w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mb-4">
                  {getMethodIcon()}
                </div>
                <h2 className="text-xl font-semibold text-center text-gray-900 dark:text-white mb-2">
                  Enter Verification Code
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-400 text-center">
                  {getMethodDescription()}
                </p>
              </div>
            </div>

            {/* OTP Form */}
            <div className="px-6 pb-6">
              {/* Error Message */}
              {apiError && (
                <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-lg text-sm mb-4">
                  {apiError}
                </div>
              )}

              {/* Success Message */}
              {successMessage && (
                <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-600 dark:text-green-400 px-4 py-3 rounded-lg text-sm mb-4">
                  {successMessage}
                </div>
              )}

              <div className="flex flex-col items-center">
                <InputOtp
                  value={token}
                  onChange={(e) =>
                    setToken(e.value ? e.value.toUpperCase() : "")
                  }
                  length={6}
                  inputTemplate={customInput}
                  style={{ gap: 0 }}
                  key="otp-input"
                  className="mb-6 flex flex-col w-full"
                />

                <div className="flex justify-between mt-5 w-full">
                  <div className="flex items-center">
                    {/* Timer Display */}
                    {timeRemaining > 0 && (
                      <p className="text-[15px]">
                        Resend code in:{" "}
                        <span className="font-medium">
                          {formatTime(timeRemaining)}
                        </span>
                      </p>
                    )}
                    {timeRemaining <= 0 && (
                      <Button
                        label={isResending ? "Sending..." : "Resend Code"}
                        link
                        className="p-0 text-green-600 font-semibold hover:text-green-700 dark:text-green-400 dark:hover:text-green-300"
                        onClick={handleResendCode}
                        disabled={isResending}
                      />
                    )}
                  </div>
                  <Button
                    label={isLoading ? "Verifying..." : "Submit Code"}
                    className="bg-[#1c5b41] hover:bg-green-700 disabled:opacity-[0.7] text-white border-green-600 hover:border-green-700 py-3 px-4 rounded-[0.5rem]"
                    onClick={handleSubmit}
                    disabled={isLoading || !token || token.length !== 6}
                  />
                </div>

                <div className="text-center mt-4">
                  <button
                    type="button"
                    onClick={() => {
                      // Get all stored MFA methods to pass back to selection page
                      const storedMfaMethods = getMFAMethods();
                      navigate("/mfa/select-method", {
                        state: {
                          mfaMethods: storedMfaMethods || [method],
                          user,
                        },
                      });
                    }}
                    className="text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 font-medium transition-colors duration-200 text-sm mr-4"
                  >
                    Choose Different Method
                  </button>
                  <button
                    type="button"
                    onClick={() => navigate("/login")}
                    className="text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 font-medium transition-colors duration-200 text-sm"
                  >
                    Back to Login
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PublicLayout>
  );
};

export default MFAOTPVerification;
