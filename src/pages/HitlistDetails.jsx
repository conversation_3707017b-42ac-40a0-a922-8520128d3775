import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  ArrowLeft,
  Phone,
  History,
  Edit,
  Trash2,
  RotateCcw,
  Loader2,
} from "lucide-react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import Modal from "../components/common/Modal";
import { customerServiceService } from "../services/customerServiceService";
import { usersService } from "../services/userService";
import Select from "react-select";
import CallToDoForm from "../components/forms/CallToDoForm";
import HistoryTab from "../components/forms/profile/HistoryTab";
import CustomCallHistoryModal from "../components/forms/profile/CustomCallHistoryModal";
import { formatTime } from "../utils/dateUtils";

const HitlistDetails = () => {
  const { hitlistCode } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isReassignModalOpen, setIsReassignModalOpen] = useState(false);
  const [users, setUsers] = useState([]);
  const [usersLoading, setUsersLoading] = useState(false);
  const [isEditLoading, setIsEditLoading] = useState(false);
  const [isDeleteLoading, setIsDeleteLoading] = useState(false);
  const [isReassignLoading, setIsReassignLoading] = useState(false);
  const [reassignForm, setReassignForm] = useState({
    assignedUser: null,
    first2: null,
    second2: null,
    third2: null,
  });

  const [hitlistDetails, setHitlistDetails] = useState({
    code: hitlistCode,
    uploadDate: "",
    uploadedBy: "",
    type: "",
    numberOfRecords: 0,
    records: [],
  });

  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editForm, setEditForm] = useState({
    customerName: "",
    phoneNumber: "",
    accountNumber: "",
  });

  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);

  // Fetch hitlist details
  useEffect(() => {
    const fetchHitlistDetails = async () => {
      try {
        setLoading(true);
        const data = await customerServiceService.getHitlistDetails(
          hitlistCode
        );
        setHitlistDetails({
          code: data.hitlistCode,
          uploadDate: data.uploadDate,
          uploadedBy: data.uploadedBy,
          type: data.type,
          numberOfRecords: data.numberOfRecords,
          records: data.records,
        });
      } catch (error) {
        console.error("Error fetching hitlist details:", error);
        // TODO: Show error message to user
      } finally {
        setLoading(false);
      }
    };

    if (hitlistCode) {
      fetchHitlistDetails();
    }
  }, [hitlistCode]);

  // Format relative time
  const formatRelativeTime = (dateString) => {
    if (!dateString) return "-";
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = date - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) return formatDateTime(dateString);
    if (diffDays === 0) return "Today";
    if (diffDays === 1) return "Tomorrow";
    return `in ${diffDays}d (${formatDate(dateString)})`;
  };

  // Transform records for the table
  const transformRecordsForTable = (records) => {
    if (hitlistDetails.type === "Dormancy") {
      return records.map((record) => ({
        id: record.id,
        customer_name: record.customerName,
        account_number: record.accountNumber,
        phone_number: record.phoneNumber,
        assigned_agent: record.assignedAgentName,
        rm_code: record.assignedAgentRMCode,
        call_status: record.callStatus,
        call_date: record.callDate,
      }));
    }

    // 2by2by2 type
    return records.map((record) => ({
      id: record.id,
      customer_name: record.customerName,
      account_number: record.accountNumber,
      phone_number: record.phoneNumber,
      assigned_agents:
        record.assignedAgentNames?.map((agent) => ({
          name: agent.name,
          rmCode: agent.rmCode,
          phases:
            agent === record.assignedAgentNames[0]
              ? ["1st Phase", "3rd Phase"]
              : ["2nd Phase"],
        })) || [],
      first_2_status: {
        is_completed: record.first2?.is_completed,
        status: record.first2?.is_completed ? "Completed" : "Not Started",
        execution_date: record.first2?.execution_date,
        expected_date: record.first2?.expected_completion_date,
        assigned_to: record.first2?.assigned_to_name,
      },
      second_2_status: {
        is_completed: record.second2?.is_completed,
        status: record.second2?.is_completed ? "Completed" : "Not Started",
        execution_date: record.second2?.execution_date,
        expected_date: record.second2?.expected_completion_date,
        assigned_to: record.second2?.assigned_to_name,
      },
      third_2_status: {
        is_completed: record.third2?.is_completed,
        status: record.third2?.is_completed ? "Completed" : "Not Started",
        execution_date: record.third2?.execution_date,
        expected_date: record.third2?.expected_completion_date,
        assigned_to: record.third2?.assigned_to_name,
      },
    }));
  };

  const customers = transformRecordsForTable(hitlistDetails.records);

  // Format date function
  const formatDate = (dateString) => {
    if (!dateString) return "-";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const formatDateTime = (dateString) => {
    if (!dateString) return "-";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Status badge component
  const StatusBadge = ({ status }) => {
    const getStatusColor = (status) => {
      switch (status?.toLowerCase()) {
        case "completed":
          return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
        case "pending":
          return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
        case "not started":
          return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
        default:
          return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
      }
    };

    return (
      <span
        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(
          status
        )}`}
      >
        {status || "-"}
      </span>
    );
  };

  // Define columns based on hitlist type
  const getColumns = () => {
    if (hitlistDetails.type === "Dormancy") {
      return [
        {
          key: "customer_name",
          title: "CUSTOMER",
          render: (value) => (
            <span className="font-medium text-gray-900 dark:text-white">
              {value}
            </span>
          ),
        },
        {
          key: "account_number",
          title: "ACCOUNT NUMBER",
          render: (value) => (
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {value || "-"}
            </span>
          ),
        },
        {
          key: "phone_number",
          title: "PHONE NUMBER",
          render: (value) => (
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {value || "-"}
            </span>
          ),
        },
        {
          key: "assigned_agent",
          title: "ASSIGNED AGENT",
          render: (value) => (
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {value || "-"}
            </span>
          ),
        },
        {
          key: "rm_code",
          title: "AGENT RM CODE",
          render: (value) => (
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {value || "-"}
            </span>
          ),
        },
        {
          key: "call_status",
          title: "CALL STATUS",
          render: (value) => <StatusBadge status={value} />,
        },
        {
          key: "call_date",
          title: "CALL DATE",
          render: (value) => (
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {formatDate(value)}
              <br></br>
              {formatTime(value)}
            </span>
          ),
        },
      ];
    } else {
      // 2by2by2 columns
      return [
        {
          key: "customer_name",
          title: "CUSTOMER",
          render: (value) => (
            <span className="font-medium text-gray-900 dark:text-white">
              {value}
            </span>
          ),
        },
        {
          key: "account_number",
          title: "ACCOUNT NUMBER",
          render: (value) => (
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {value || "-"}
            </span>
          ),
        },
        {
          key: "assigned_agents",
          title: "ASSIGNED AGENTS",
          render: (agents) => (
            <div className="space-y-2">
              {agents.map((agent, index) => (
                <div key={index} className="text-sm">
                  <div className="font-medium text-gray-900 dark:text-white">
                    {agent.name}
                    {/* ({agent.rmCode}) */}
                  </div>
                  <div className="text-xs text-gray-500">
                    {agent.phases.join(", ")}
                  </div>
                </div>
              ))}
            </div>
          ),
        },
        {
          key: "first_2_status",
          title: "1ST 2 STATUS",
          render: (value) => (
            <div className="space-y-1">
              <StatusBadge status={value.status} />
              <div className="text-xs text-gray-500">
                {value.is_completed
                  ? `Done on ${formatDateTime(value.execution_date)}`
                  : value.expected_date
                  ? `Starting ${formatRelativeTime(value.expected_date)}`
                  : "-"}
              </div>
            </div>
          ),
        },
        {
          key: "second_2_status",
          title: "2ND 2 STATUS",
          render: (value) => (
            <div className="space-y-1">
              <StatusBadge status={value.status} />
              <div className="text-xs text-gray-500">
                {value.is_completed
                  ? `Done on ${formatDateTime(value.execution_date)}`
                  : value.expected_date
                  ? `Starting ${formatRelativeTime(value.expected_date)}`
                  : "-"}
              </div>
            </div>
          ),
        },
        {
          key: "third_2_status",
          title: "3RD 2 STATUS",
          render: (value) => (
            <div className="space-y-1">
              <StatusBadge status={value.status} />
              <div className="text-xs text-gray-500">
                {value.is_completed
                  ? `Done on ${formatDateTime(value.execution_date)}`
                  : value.expected_date
                  ? `Starting ${formatRelativeTime(value.expected_date)}`
                  : "-"}
              </div>
            </div>
          ),
        },
      ];
    }
  };

  // Event handlers
  const handleView = (item) => {
    setSelectedItem(item);
    console.log("View item:", item);
  };

  const handleLoadMore = () => {
    setLoadingMore(true);
    setTimeout(() => {
      setLoadingMore(false);
      console.log("Load more customers");
    }, 2000);
  };

  const handleCustomAction = (action, item) => {
    setSelectedItem(item);
    switch (action) {
      case "reassign":
        setIsReassignModalOpen(true);
        break;
      case "call-history":
        setIsHistoryModalOpen(true);
        break;
      default:
        console.log("Unhandled custom action:", action);
    }
  };

  const handleEdit = (item) => {
    setSelectedItem(item);
    setEditForm({
      customerName: item.customer_name || "",
      phoneNumber: item.phone_number || "",
      accountNumber: item.account_number || "",
    });
    setIsEditModalOpen(true);
  };

  const handleEditChange = (e) => {
    const { name, value } = e.target;
    setEditForm((prev) => ({ ...prev, [name]: value }));
  };

  const handleEditSubmit = async (e) => {
    e.preventDefault();
    if (!selectedItem) return;
    setIsEditLoading(true);
    try {
      const updated = await customerServiceService.updateHitlistRecord(
        selectedItem.id,
        {
          customerName: editForm.customerName,
          phoneNumber: editForm.phoneNumber,
          accountNumber: editForm.accountNumber,
        }
      );
      // Update the record in state
      setHitlistDetails((prev) => ({
        ...prev,
        records: prev.records.map((rec) =>
          rec.id === selectedItem.id ? { ...rec, ...updated } : rec
        ),
      }));
      setIsEditModalOpen(false);
      setSelectedItem(null);
    } catch (error) {
      alert("Failed to update record. Please try again.");
    } finally {
      setIsEditLoading(false);
    }
  };

  const handleEditCancel = () => {
    setIsEditModalOpen(false);
    setSelectedItem(null);
  };

  const handleDelete = (item) => {
    setSelectedItem(item);
    setIsDeleteModalOpen(true);
  };
  const handleDeleteConfirm = async () => {
    if (!selectedItem) return;
    setIsDeleteLoading(true);
    try {
      const success = await customerServiceService.deleteHitlistRecord(
        selectedItem.id
      );
      if (success) {
        setHitlistDetails((prev) => ({
          ...prev,
          records: prev.records.filter((rec) => rec.id !== selectedItem.id),
        }));
        setIsDeleteModalOpen(false);
        setSelectedItem(null);
      } else {
        alert("Failed to delete record. Please try again.");
      }
    } catch (error) {
      alert("Failed to delete record. Please try again.");
    } finally {
      setIsDeleteLoading(false);
    }
  };
  const handleDeleteCancel = () => {
    setIsDeleteModalOpen(false);
    setSelectedItem(null);
  };

  const handleBackToHitlists = () => {
    navigate("/customer-service/hitlist");
  };

  // Export/Print handlers
  const handleExport = () => {
    console.log("Exporting hitlist details");
    // API integration logic here
  };

  const handlePrint = () => {
    console.log("Printing hitlist details");
    // Print functionality here
  };

  // Calculate progress percentage based on hitlist type
  const getProgressPercentage = () => {
    if (!hitlistDetails.numberOfRecords) return 0;

    if (hitlistDetails.type === "Dormancy") {
      const completed = customers.filter(
        (c) => c.call_status === "Completed"
      ).length;
      return (completed / hitlistDetails.numberOfRecords) * 100;
    } else {
      const completed = customers.filter(
        (c) => c.first_2_status?.status === "Completed"
      ).length;
      return (completed / hitlistDetails.numberOfRecords) * 100;
    }
  };

  // Get completed count based on hitlist type
  const getCompletedCount = () => {
    if (hitlistDetails.type === "Dormancy") {
      return customers.filter((c) => c.call_status === "Completed").length;
    } else {
      return customers.filter((c) => c.first_2_status?.status === "Completed")
        .length;
    }
  };

  useEffect(() => {
    if (isReassignModalOpen) {
      setUsersLoading(true);
      usersService
        .getAll()
        .then((data) => {
          setUsers(
            data.map((user) => ({
              value: user.id,
              label: `${user.name} (${user.rm_code || user.rmCode || "No RM"})`,
              user,
            }))
          );
        })
        .finally(() => setUsersLoading(false));
    }
  }, [isReassignModalOpen]);

  useEffect(() => {
    if (isReassignModalOpen && selectedItem) {
      // Auto-select current user(s) if present
      if (hitlistDetails.type === "Dormancy") {
        const match = users.find(
          (u) =>
            u.label.includes(selectedItem.assigned_agent) ||
            u.value === selectedItem.assigned_agent
        );
        setReassignForm((prev) => ({ ...prev, assignedUser: match || null }));
      } else {
        console.log("This is the item:", selectedItem);
        // 2by2by2: try to match assigned_to for each phase
        setReassignForm({
          first2:
            users.find(
              (u) => u.label.includes(selectedItem.assigned_agents[0]?.name)
              // || u.value === selectedItem.first_2_status?.assigned_to
            ) || null,
          second2:
            users.find((u) =>
              u.label.includes(selectedItem.assigned_agents[1]?.name)
            ) || null,
          third2:
            users.find((u) =>
              u.label.includes(selectedItem.assigned_agents[0]?.name)
            ) || null,
          assignedUser: null,
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [users, isReassignModalOpen, selectedItem]);

  const handleReassignChange = (field, value) => {
    setReassignForm((prev) => ({ ...prev, [field]: value }));
  };

  const handleReassignSubmit = async (e) => {
    e.preventDefault();
    if (!selectedItem) return;
    let payload = {};
    if (hitlistDetails.type === "Dormancy") {
      if (!reassignForm.assignedUser) {
        alert("Please select a user to reassign.");
        return;
      }
      payload = { newUser: reassignForm.assignedUser.value };
    } else {
      payload = {
        ...(reassignForm.first2
          ? { newFirst2: reassignForm.first2.value }
          : {}),
        ...(reassignForm.second2
          ? { newSecond2: reassignForm.second2.value }
          : {}),
        ...(reassignForm.third2
          ? { newThird2: reassignForm.third2.value }
          : {}),
      };
      if (!payload.newFirst2 && !payload.newSecond2 && !payload.newThird2) {
        alert("Please select at least one user to reassign.");
        return;
      }
    }
    setIsReassignLoading(true);
    try {
      const updated = await customerServiceService.reassignHitlistRecord(
        selectedItem.id,
        payload
      );
      setHitlistDetails((prev) => ({
        ...prev,
        records: prev.records.map((rec) =>
          rec.id === selectedItem.id ? { ...rec, ...updated } : rec
        ),
      }));
      setIsReassignModalOpen(false);
      setSelectedItem(null);
    } catch (error) {
      alert("Failed to reassign record. Please try again.");
    } finally {
      setIsReassignLoading(false);
    }
  };
  const handleReassignCancel = () => {
    setIsReassignModalOpen(false);
    setSelectedItem(null);
  };

  return (
    <PrivateLayout pageTitle={`Hitlist Details - ${hitlistDetails.code}`}>
      <div className="space-y-6">
        {/* Header Section */}
        <div className="bg-white rounded-lg shadow px-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBackToHitlists}
                className="inline-flex items-center px-4 py-2 bg-[#1c5b41] text-white rounded-lg transition-colors duration-200"
              >
                <ArrowLeft size={16} className="mr-2" />
                Back to Hitlists
              </button>
              {/* <h1 className="text-2xl font-semibold text-gray-900">
                {hitlistDetails.code}
              </h1> */}
            </div>
            <div>
              <p className="text-sm text-gray-500">Upload Date & Time</p>
              <p className="text-lg font-medium text-gray-900">
                {formatDateTime(hitlistDetails.uploadDate)}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Uploaded By</p>
              <p className="text-lg font-medium text-gray-900">
                {hitlistDetails.uploadedBy}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Type</p>
              <p className="text-lg font-medium text-gray-900">
                {hitlistDetails.type}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Total Customers</p>
              <p className="text-lg font-medium text-gray-900">
                {hitlistDetails.numberOfRecords}
              </p>
            </div>
          </div>

          {/* Call Progress */}
          <div className="mt-6">
            <p className="text-sm text-gray-500 mb-2">Call Progress</p>
            <div className="flex items-center space-x-4">
              <div className="flex-1 bg-gray-200 rounded-full h-3 dark:bg-gray-700">
                <div
                  className="bg-green-600 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${getProgressPercentage()}%` }}
                ></div>
              </div>
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {getCompletedCount()}/{hitlistDetails.numberOfRecords} calls
                made ({Math.round(getProgressPercentage())}%)
              </span>
            </div>
          </div>
        </div>

        {/* Data Table */}
        <DataTable
          columns={getColumns()}
          data={customers}
          searchPlaceholder="Search customers..."
          onView={handleView}
          actions={["edit", "delete", "reassign", "call-history"]}
          loading={loading}
          loadingMore={loadingMore}
          loadMoreText="Load More Customers"
          onLoadMore={handleLoadMore}
          showLoadMore={true}
          dataCountLabel="customers"
          showDataCount={true}
          onCustomAction={handleCustomAction}
          customActionLabels={{
            reassign: "Reassign",
            "call-history": "See Call History",
          }}
          // Export functionality - only export and print
          showImportExport={true}
          showImport={false}
          onExport={handleExport}
          onPrint={handlePrint}
          onEdit={handleEdit}
          onDelete={handleDelete}
        />
      </div>
      {/* Delete Modal */}
      <Modal
        isOpen={isDeleteModalOpen}
        onClose={handleDeleteCancel}
        title="Delete Customer"
        size="sm"
      >
        <div className="space-y-4">
          <p className="text-gray-700 dark:text-gray-200">
            Are you sure you want to delete this customer?
          </p>
          <div className="flex justify-end space-x-3">
            <button
              onClick={handleDeleteCancel}
              className="px-4 py-2 rounded-lg border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleDeleteConfirm}
              className="px-4 py-2 rounded-lg bg-red-600 text-white hover:bg-red-700 transition-colors"
            >
              Delete
            </button>
          </div>
        </div>
      </Modal>
      {/* Reassign Modal */}
      <Modal
        isOpen={isReassignModalOpen}
        onClose={handleReassignCancel}
        title="Reassign User(s)"
        size="md"
      >
        <form onSubmit={handleReassignSubmit} className="space-y-6 pt-[2rem]">
          {hitlistDetails.type === "Dormancy" ? (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Assigned user
              </label>
              <Select
                name="assignedUser"
                value={reassignForm.assignedUser}
                onChange={(val) => handleReassignChange("assignedUser", val)}
                options={users}
                isLoading={usersLoading}
                isDisabled={usersLoading}
                className="react-select-container"
                classNamePrefix="react-select"
                placeholder="Select user"
                isSearchable
              />
            </div>
          ) : (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  First2 & Third2 Phase
                </label>
                <Select
                  name="first2"
                  value={reassignForm.first2}
                  onChange={(val) => handleReassignChange("first2", val)}
                  options={users}
                  isLoading={usersLoading}
                  isDisabled={usersLoading}
                  className="react-select-container"
                  classNamePrefix="react-select"
                  placeholder="Select user"
                  isSearchable
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Second2 Phase
                </label>
                <Select
                  name="second2"
                  value={reassignForm.second2}
                  onChange={(val) => handleReassignChange("second2", val)}
                  options={users}
                  isLoading={usersLoading}
                  isDisabled={usersLoading}
                  className="react-select-container"
                  classNamePrefix="react-select"
                  placeholder="Select user"
                  isSearchable
                />
              </div>
              <div className="hidden">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Third2 assigned user
                </label>
                <Select
                  name="third2"
                  value={reassignForm.third2}
                  onChange={(val) => handleReassignChange("third2", val)}
                  options={users}
                  isLoading={usersLoading}
                  isDisabled={usersLoading}
                  className="react-select-container"
                  classNamePrefix="react-select"
                  placeholder="Select user"
                  isSearchable
                />
              </div>
            </>
          )}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={handleReassignCancel}
              className="px-4 py-2 rounded-lg border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isReassignLoading}
              className={`px-4 py-2 rounded-lg text-white transition-colors flex items-center ${
                isReassignLoading
                  ? "bg-green-400 cursor-not-allowed"
                  : "bg-green-600 hover:bg-green-700"
              }`}
            >
              {isReassignLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Reassigning...
                </>
              ) : (
                "Reassign"
              )}
            </button>
          </div>
        </form>
      </Modal>
      {/* Edit Modal */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={handleEditCancel}
        title="Edit Customer Record"
        size="md"
      >
        <form onSubmit={handleEditSubmit} className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Customer Name
            </label>
            <input
              type="text"
              name="customerName"
              value={editForm.customerName}
              onChange={handleEditChange}
              className="w-full px-4 py-3 border rounded-lg bg-white text-gray-900 placeholder-gray-500 outline-none transition-colors duration-200 border-gray-300 focus:border-green-500 hover:border-gray-400"
              placeholder="Enter customer name"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Phone Number
            </label>
            <input
              type="tel"
              name="phoneNumber"
              value={editForm.phoneNumber}
              onChange={handleEditChange}
              className="w-full px-4 py-3 border rounded-lg bg-white text-gray-900 placeholder-gray-500 outline-none transition-colors duration-200 border-gray-300 focus:border-green-500 hover:border-gray-400"
              placeholder="Enter phone number"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Account Number
            </label>
            <input
              type="text"
              name="accountNumber"
              value={editForm.accountNumber}
              onChange={handleEditChange}
              className="w-full px-4 py-3 border rounded-lg bg-white text-gray-900 placeholder-gray-500 outline-none transition-colors duration-200 border-gray-300 focus:border-green-500 hover:border-gray-400"
              placeholder="Enter account number"
              required
            />
          </div>
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={handleEditCancel}
              className="px-4 py-2 rounded-lg border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isEditLoading}
              className={`px-4 py-2 rounded-lg text-white transition-colors flex items-center ${
                isEditLoading
                  ? "bg-green-400 cursor-not-allowed"
                  : "bg-green-600 hover:bg-green-700"
              }`}
            >
              {isEditLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                "Save"
              )}
            </button>
          </div>
        </form>
      </Modal>
      {/* Call History Modal */}
      {isHistoryModalOpen && selectedItem && (
        <Modal
          isOpen={isHistoryModalOpen}
          onClose={() => {
            setIsHistoryModalOpen(false);
            setSelectedItem(null);
          }}
          title={`Call History - ${
            selectedItem?.customer_name || selectedItem?.name
          }`}
          size="xl"
        >
          <CustomCallHistoryModal
            item={selectedItem}
            onClose={() => {
              setIsHistoryModalOpen(false);
              setSelectedItem(null);
            }}
          />
        </Modal>
      )}
    </PrivateLayout>
  );
};

export default HitlistDetails;
