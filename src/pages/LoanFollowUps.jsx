import { useState, useEffect } from "react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import LoanCallForm from "../components/forms/LoanCallForm";
import { loanService, formatLoanFollowUpsForTable } from "../services/loanService";
import { loanActivitiesService, formatFollowupsForTable, formatLoanActivitiesAsFollowups } from "../services/loanActivitiesService";
import { toast } from 'react-toastify';

const LoanFollowUps = () => {
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [followUps, setFollowUps] = useState([]);
  const [selectedItem, setSelectedItem] = useState(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });
  const [filters, setFilters] = useState({
    status: '' // '', 'pending', 'completed', 'cancelled'
  });

  // Date range filter states
  const [fromDate, setFromDate] = useState("");
  const [toDate, setToDate] = useState("");

  // Filter configuration for the DataTable
  const filterConfig = [
    {
      field: "status",
      label: "Status",
      type: "select",
      options: [
        { value: "pending", label: "Pending" },
        { value: "completed", label: "Completed" },
        { value: "cancelled", label: "Cancelled" },
      ],
    },
  ];

  // Filter change handlers
  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleClearFilters = () => {
    setFilters({
      status: "",
    });
    setFromDate("");
    setToDate("");
  };

  // Date change handlers for DataTable
  const handleFromDateChange = (date) => {
    setFromDate(date);
  };

  const handleToDateChange = (date) => {
    setToDate(date);
  };

  // Define table columns
  const columns = [
    {
      key: "customerName",
      title: "CUSTOMER NAME",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
  
    {
      key: "scheduledDate",
      title: "SCHEDULED DATE",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
    {
      key: "status",
      title: "STATUS",
      render: (value) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          value === "completed" ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400" :
          value === "pending" ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400" :
          value === "canceled" ? "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400" :
          "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400"
        }`}>
          {value}
        </span>
      ),
    },
    {
      key: "assignedOfficer",
      title: "ASSIGNED OFFICER",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
    {
      key: "createdDate",
      title: "CREATED DATE",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
  ];

  // Fetch follow-ups data from API using new loan activities followups endpoint
  const fetchFollowUps = async () => {
    try {
      setLoading(true);
      console.log("Fetching loan activity follow-ups data...");

      // Temporarily use loan activities endpoint directly until followups endpoint is available
      const fallbackFilters = {};
      if (filters.status) {
        fallbackFilters.followup_status = filters.status;
      }

      const response = await loanActivitiesService.getAll(
        pagination.page,
        pagination.limit,
        fallbackFilters
      );

      // Format the data for the table - using loan activities format
      const formattedFollowUps = formatLoanActivitiesAsFollowups(response);

      setFollowUps(formattedFollowUps);
      setPagination(prev => ({
        ...prev,
        total: response.total,
        totalPages: response.totalPages
      }));

      console.log("Loan activity follow-ups data loaded successfully:", formattedFollowUps.length, "follow-ups");
    } catch (error) {
      console.error("Error fetching loan activity follow-ups:", error);

      // Provide more specific error messages
      let errorMessage = "Failed to load follow-ups data";
      if (error.response?.status === 400) {
        errorMessage = "Invalid request parameters. Please check your filters.";
      } else if (error.response?.status === 404) {
        errorMessage = "Follow-ups endpoint not found. Using fallback data.";
      } else if (error.response?.status >= 500) {
        errorMessage = "Server error. Please try again later.";
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount and when filters/pagination change
  useEffect(() => {
    fetchFollowUps();
  }, [pagination.page, pagination.limit, filters.status]);

  // Handle load more
  const handleLoadMore = () => {
    setLoadingMore(true);
    // Simulate loading more data
    setTimeout(() => {
      setLoadingMore(false);
    }, 1000);
  };

  // Handle status update
  const handleStatusUpdate = async (activityId, newStatus) => {
    try {
      console.log(`Updating followup ${activityId} status to ${newStatus}`);

      // Update using the loan activities service
      await loanActivitiesService.updateFollowupStatus(activityId, newStatus);

      // Update the followup status locally
      setFollowUps(prevFollowUps =>
        prevFollowUps.map(followup =>
          followup.id === activityId
            ? { ...followup, status: newStatus }
            : followup
        )
      );

      toast.success(`Follow-up status updated to ${newStatus}`);
    } catch (error) {
      console.error("Error updating followup status:", error);
      toast.error(error.message || "Failed to update follow-up status");
    }
  };

  // Handle custom actions (call, set-completed, set-cancelled)
  const handleCustomAction = async (action, followUp) => {
    console.log(`=== CUSTOM ACTION: ${action} ===`);
    console.log('Follow-up data:', followUp);
    console.log('================================');

    setSelectedItem(followUp);

    switch (action) {
      case "set-completed":
        return await handleStatusUpdate(followUp.id, "completed");
      case "set-cancelled":
        return await handleStatusUpdate(followUp.id, "cancelled");
      case "call":
        // This will be handled by the modal form
        break;
      default:
        console.log(`Unhandled custom action: ${action}`);
    }
  };

  // Handle call action (similar to loan hitlist)
  const handleCallSubmit = async (callData, selectedItem, files = []) => {
    try {
      console.log("Creating call for loan followup:", callData);
      console.log("Selected item:", selectedItem);
      console.log("Files:", files);

      // Create the call using the loan service
      const newCall = await loanService.calls.create(callData, files);

      // Refresh the followups data to get updated information
      await fetchFollowUps();

      toast.success("Call logged successfully!");
      return true;
    } catch (error) {
      console.error("Error creating call:", error);
      toast.error(error.message || "Failed to log call");
      return false;
    }
  };

  // Handle create follow-up
  const handleCreateSubmit = async (formData) => {
    try {
      console.log("Creating new loan follow-up:", formData);
      const newFollowUp = await loanService.followUps.create(formData);
      
      // Add the new follow-up to the state
      const formattedFollowUp = formatLoanFollowUpsForTable({ data: [newFollowUp] })[0];
      setFollowUps(prevFollowUps => [formattedFollowUp, ...prevFollowUps]);
      
      toast.success("Loan follow-up created successfully!");
      return true;
    } catch (error) {
      console.error("Error creating loan follow-up:", error);
      toast.error(error.message || "Failed to create loan follow-up");
      return false;
    }
  };

  // Handle reschedule follow-up
  const handleRescheduleSubmit = async (formData) => {
    try {
      console.log("Rescheduling loan follow-up:", formData);
      const updatedFollowUp = await loanService.followUps.update(selectedItem.id, formData);
      
      // Update the follow-up in state
      const formattedFollowUp = formatLoanFollowUpsForTable({ data: [updatedFollowUp] })[0];
      setFollowUps(prevFollowUps => 
        prevFollowUps.map(followUp => 
          followUp.id === selectedItem.id ? formattedFollowUp : followUp
        )
      );
      
      toast.success("Loan follow-up rescheduled successfully!");
      return true;
    } catch (error) {
      console.error("Error rescheduling loan follow-up:", error);
      toast.error(error.message || "Failed to reschedule loan follow-up");
      return false;
    }
  };

  // Handle log outcome
  const handleLogOutcomeSubmit = async (formData) => {
    try {
      console.log("Logging outcome for loan follow-up:", formData);
      const updatedFollowUp = await loanService.followUps.update(selectedItem.id, {
        ...formData,
        status: "completed"
      });
      
      // Update the follow-up in state
      const formattedFollowUp = formatLoanFollowUpsForTable({ data: [updatedFollowUp] })[0];
      setFollowUps(prevFollowUps => 
        prevFollowUps.map(followUp => 
          followUp.id === selectedItem.id ? formattedFollowUp : followUp
        )
      );
      
      toast.success("Outcome logged successfully!");
      return true;
    } catch (error) {
      console.error("Error logging outcome:", error);
      toast.error(error.message || "Failed to log outcome");
      return false;
    }
  };

  // Handle cancel follow-up
  const handleCancelConfirm = async (followUp) => {
    try {
      console.log("Canceling loan follow-up:", followUp);
      const updatedFollowUp = await loanService.followUps.update(followUp.id, {
        status: "canceled"
      });
      
      // Update the follow-up in state
      const formattedFollowUp = formatLoanFollowUpsForTable({ data: [updatedFollowUp] })[0];
      setFollowUps(prevFollowUps => 
        prevFollowUps.map(f => 
          f.id === followUp.id ? formattedFollowUp : f
        )
      );
      
      toast.success("Loan follow-up canceled successfully!");
    } catch (error) {
      console.error("Error canceling loan follow-up:", error);
      toast.error(error.message || "Failed to cancel loan follow-up");
    }
  };

  // Handle view follow-up
  const handleView = (followUp) => {
    console.log("View loan follow-up:", followUp);
    setSelectedItem(followUp);
  };

  return (
    <PrivateLayout pageTitle="Loan Follow-ups">
      <div className="">
        {/* Data Table */}
        <DataTable
          columns={columns}
          data={followUps}
          searchPlaceholder="Search loan follow-ups..."
          onView={handleView}
          onStatusUpdate={handleStatusUpdate}
          onCustomAction={handleCustomAction}
          actions={["call", "set-completed", "set-cancelled"]}
          loading={loading}
          loadingMore={loadingMore}
          loadMoreText="Load More Follow-ups"
          onLoadMore={handleLoadMore}
          showLoadMore={true}
          highlightField="status"
          highlightColors={{
            Pending: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",
            Completed: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
            Canceled: "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400",
          }}
          // Data count
          showDataCount={true}
          dataCountLabel="loan follow-ups"
          // Filters
          showFilters={true}
          filterConfig={filterConfig}
          onFilterChange={handleFilterChange}
          onClearFilters={handleClearFilters}
          // Date range filter
          showDateRangeFilter={true}
          dateRangeField="date"
          fromDate={fromDate}
          toDate={toDate}
          onFromDateChange={handleFromDateChange}
          onToDateChange={handleToDateChange}
          // Modal forms
          callForm={({ item, onClose }) => {
            // Create a loan client object for the LoanCallForm
            const loanClientItem = {
              id: item?.customerId || item?.id,
              customer_name: item?.customerName,
              name: item?.customerName,
              // Add any other fields that LoanCallForm might need
            };

            return (
              <LoanCallForm
                item={loanClientItem}
                onClose={onClose}
                onSubmit={handleCallSubmit}
              />
            );
          }}

          callModalTitle={
            selectedItem
              ? `Call ${selectedItem.customerName}`
              : "Make Call"
          }
          modalSize="lg"
          callModalSize="lg"
        />
      </div>
    </PrivateLayout>
  );
};

export default LoanFollowUps;
