import { useState } from "react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import DeleteConfirmation from "../components/forms/DeleteConfirmation";
import TypeForm from "../components/forms/TypeForm";

const Types = () => {
  const [loading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);

  // Define columns of the table here and what to render
  const columns = [
    {
      key: "name",
      title: "TYPE",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "addedBy",
      title: "ADDED BY",
      render: (value) => (
        <span className="inline-flex px-2 py-1 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
          {value}
        </span>
      ),
    },

    {
      key: "addedOn",
      title: "ADDED ON",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
  ];

  // Data to be rendered in the table, should match the columns above
  const sampleTypes = [
    {
      id: "00000003",
      name: "BUSINESS",
      addedBy: "RENOIR",
      addedOn: "July 16 2025",
    },
    {
      id: "00000002",
      name: "INDIVIDUAL",
      addedBy: "RENOIR",
      addedOn: "July 16 2025",
    },
    {
      id: "00000001",
      name: "GROUP",
      addedBy: "RENOIR",
      addedOn: "July 16 2025",
    },
  ];

  // Form submission handlers
  const handleCreateSubmit = (formData) => {
    console.log("Creating type:", formData);
    // Here you would typically make an API call to create the type
    // After successful creation, you might want to refresh the data
  };

  const handleEditSubmit = (formData) => {
    console.log("Updating type:", formData);
    // Here you would typically make an API call to update the type
    // After successful update, you might want to refresh the data
  };

  const handleDeleteConfirm = (type) => {
    console.log("Deleting type:", type);
    // Here you would typically make an API call to delete the type
    // After successful deletion, you might want to refresh the data
  };

  const handleView = (type) => {
    console.log("View type:", type);
    // Here you would typically navigate to view page or show view modal
  };

  const handleLoadMore = () => {
    setLoadingMore(true);
    // Simulate API call
    setTimeout(() => {
      setLoadingMore(false);
      console.log("Load more types");
    }, 2000);
  };

  return (
    <PrivateLayout>
      {/* Data Table */}
      <div className="">
        <DataTable
          columns={columns}
          data={sampleTypes}
          searchPlaceholder="Search ..."
          addButtonText="New Type"
          onView={handleView}
          actions={["view", "edit", "delete"]}
          loading={loading}
          loadingMore={loadingMore}
          loadMoreText="Load More Types"
          onLoadMore={handleLoadMore}
          showLoadMore={true}
          highlightField="addedBy"
          highlightColors={{
            BUSINESS:
              "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
            PERSONAL:
              "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",
          }}
          // Modal forms
          createForm={({ onClose }) => (
            <TypeForm onClose={onClose} onSubmit={handleCreateSubmit} />
          )}
          editForm={({ item, onClose }) => (
            <TypeForm
              item={item}
              onClose={onClose}
              onSubmit={handleEditSubmit}
            />
          )}
          deleteForm={({ item, onClose }) => (
            <DeleteConfirmation
              item={item}
              onClose={onClose}
              onConfirm={handleDeleteConfirm}
              itemName="Type"
            />
          )}
          createModalTitle="Create New Type"
          editModalTitle="Type"
          deleteModalTitle=""
          modalSize="lg"
          deleteModalSize="sm"
        />
      </div>
    </PrivateLayout>
  );
};

export default Types;
