import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Mail, Phone, Shield } from "lucide-react";
import PublicLayout from "../components/layouts/PublicLayout";
import { authService } from "../services/authService";
import { getMFAMethods, getUserData } from "../utils/cookieUtils";
import logoSmall from "../assets/images/logo.png";
import logoText from "../assets/images/logo-text.png";

const MFAMethodSelection = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [mfaMethods, setMfaMethods] = useState([]);
  const [user, setUser] = useState(null);
  const [selectedMethod, setSelectedMethod] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  useEffect(() => {
    // Get MFA methods and user from navigation state or cookies
    if (location.state?.mfaMethods && location.state?.user) {
      // Use data from navigation state (fresh from login)
      setMfaMethods(location.state.mfaMethods);
      setUser(location.state.user);
    } else {
      // Try to get data from cookies (user navigated back or refreshed)
      const storedMfaMethods = getMFAMethods();
      const storedUser = getUserData();

      if (storedMfaMethods && storedUser) {
        setMfaMethods(storedMfaMethods);
        setUser(storedUser);
      } else {
        // If no data available, redirect back to login
        navigate("/login");
      }
    }
  }, [location.state, navigate]);

  const handleMethodSelect = (method) => {
    setSelectedMethod(method);
  };

  const handleContinue = async () => {
    if (!selectedMethod) {
      setError("Please select a verification method");
      return;
    }

    setIsLoading(true);
    setError("");

    try {
      // Send MFA code to the selected method
      await authService.sendMFACode(selectedMethod.id);

      // Navigate to OTP verification page
      navigate("/mfa/verify-otp", {
        state: {
          method: selectedMethod,
          user: user,
        },
      });
    } catch (err) {
      let errorMessage = "Failed to send verification code. Please try again.";

      if (err.response) {
        errorMessage = err.response.data?.message || errorMessage;
      }

      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const getMethodIcon = (method) => {
    switch (method.method) {
      case "EMAIL":
        return <Mail className="h-6 w-6" />;
      case "SMS":
        return <Phone className="h-6 w-6" />;
      default:
        return <Shield className="h-6 w-6" />;
    }
  };

  const getMethodDescription = (method) => {
    switch (method.method) {
      case "EMAIL":
        return `Send code to ${method.contact}`;
      case "SMS":
        return `Send code to ${method.contact}`;
      default:
        return "Verification method";
    }
  };

  return (
    <PublicLayout>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <div className="bg-white dark:bg-gray-800 shadow-lg rounded-lg border-0">
            {/* Logo Section */}
            <div className="flex items-center justify-center h-24 px-4">
              <div className="flex items-center gap-4">
                <span className="h-11 flex-shrink-0">
                  <img
                    className="logo-abbr h-full w-auto"
                    src={logoSmall}
                    alt=""
                  />
                </span>
                <span className="h-10 transition-opacity duration-300">
                  <img
                    className="brand-title h-full w-auto"
                    src={logoText}
                    alt=""
                  />
                </span>
              </div>
            </div>

            {/* Header */}
            <div className="p-6 pb-4">
              <div className="text-center mb-6">
                <div className="mx-auto w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mb-4">
                  <Shield className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                </div>
                <h2 className="text-xl font-semibold text-center text-gray-900 dark:text-white mb-2">
                  Two-Factor Authentication
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-400 text-center">
                  Choose how you'd like to receive your verification code
                </p>
              </div>
            </div>

            {/* Method Selection */}
            <div className="px-6 pb-6">
              {error && (
                <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-lg text-sm mb-4">
                  {error}
                </div>
              )}

              <div className="space-y-3 mb-6">
                {mfaMethods.map((method) => (
                  <div
                    key={method.id}
                    onClick={() => handleMethodSelect(method)}
                    className={`p-4 border rounded-lg cursor-pointer transition-all duration-200 ${
                      selectedMethod?.id === method.id
                        ? "border-green-500 bg-green-50 dark:bg-green-900/20"
                        : "border-gray-300 dark:border-gray-600 hover:border-green-400 hover:bg-gray-50 dark:hover:bg-gray-700"
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <div
                        className={`p-2 rounded-full ${
                          selectedMethod?.id === method.id
                            ? "bg-green-100 dark:bg-green-800 text-green-600 dark:text-green-400"
                            : "bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400"
                        }`}
                      >
                        {getMethodIcon(method)}
                      </div>
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-900 dark:text-white">
                          {method.method === "EMAIL" ? "Email" : "SMS"}
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {getMethodDescription(method)}
                        </p>
                      </div>
                      <div
                        className={`w-4 h-4 rounded-full border-2 ${
                          selectedMethod?.id === method.id
                            ? "border-green-500 bg-green-500"
                            : "border-gray-300 dark:border-gray-600"
                        }`}
                      >
                        {selectedMethod?.id === method.id && (
                          <div className="w-full h-full rounded-full bg-white scale-50"></div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Continue Button */}
              <button
                onClick={handleContinue}
                disabled={isLoading || !selectedMethod}
                className="w-full bg-[#1c5b41] hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 focus:outline-none"
              >
                {isLoading ? (
                  <div className="flex items-center justify-center gap-2">
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    Sending Code...
                  </div>
                ) : (
                  "Continue"
                )}
              </button>

              {/* Back to Login */}
              <div className="text-center mt-4">
                <button
                  type="button"
                  onClick={() => navigate("/login")}
                  className="text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 font-medium transition-colors duration-200 text-sm"
                >
                  Back to Login
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PublicLayout>
  );
};

export default MFAMethodSelection;
