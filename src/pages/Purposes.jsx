import { useState, useEffect } from "react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import DeleteConfirmation from "../components/forms/DeleteConfirmation";
import PurposeForm from "../components/forms/PurposeForm";
import SuccessModal from "../components/modals/SuccessModal";
import { hasPermission } from "../utils/permissionUtils";

import {
  purposesService,
  formatPurposesForTable,
  getPurposeUsageColor,
} from "../services/purposesService";

const Purposes = () => {
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [purposes, setPurposes] = useState([]);
  const [error, setError] = useState(null);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");

  // Define columns of the table here and what to render
  const columns = [
    {
      key: "name",
      title: "Purpose",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "addedBy",
      title: "ADDED BY",
      render: (value) => (
        <span className="inline-flex px-2 py-1 font-semibold rounded-full ">
          {value}
        </span>
      ),
    },

    {
      key: "addedOn",
      title: "ADDED ON",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
    {
      key: "categoryName",
      title: "CATEGORY",
      render: (value) => (
        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
          {value || "Unknown Category"}
        </span>
      ),
    },
    {
      key: "totalActivitiesCount",
      title: "USAGE",
      render: (value, row) => (
        <div className="flex flex-col">
          <span
            className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPurposeUsageColor(
              row.isInUse,
              value
            )}`}
          >
            {value || 0} activities
          </span>
          {row.isInUse && (
            <span className="text-xs text-green-600 dark:text-green-400 mt-1">
              In use
            </span>
          )}
        </div>
      ),
    },
  ];

  // Fetch purposes data from API
  const fetchPurposes = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log("Fetching purposes data...");

      const response = await purposesService.getAll();
      const formattedPurposes = formatPurposesForTable(response);

      setPurposes(formattedPurposes);
      console.log(
        "Purposes data loaded successfully:",
        formattedPurposes.length,
        "purposes"
      );
    } catch (error) {
      console.error("Error fetching purposes:", error);
      setError(error.message);
      console.error(error.message || "Failed to load purposes data");
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchPurposes();
  }, []);

  // Form submission handlers
  const handleCreateSubmit = async (formData) => {
    try {
      console.log("Creating PURPOSE:", formData);

      const newPurpose = await purposesService.create(formData);
      console.log("Raw API response for new purpose:", newPurpose);

      // Format the new purpose using the existing formatting function
      const formattedPurposes = formatPurposesForTable({ data: [newPurpose] });
      const formattedPurpose = formattedPurposes[0];

      console.log("Formatted purpose for table:", formattedPurpose);

      // Add the formatted purpose to the state
      setPurposes((prevPurposes) => [formattedPurpose, ...prevPurposes]);

      setSuccessMessage("Purpose created successfully!");
      setShowSuccessModal(true);
      console.log("Purpose added to table state with proper formatting");
    } catch (error) {
      console.error("Error creating purpose:", error);
    }
  };

  const handleEditSubmit = async (updatedPurposeData, originalItem) => {
    try {
      console.log("Updating purpose in table:", {
        originalItem,
        updatedData: updatedPurposeData,
      });

      const updatedPurpose = await purposesService.update(
        originalItem.id,
        updatedPurposeData
      );
      console.log("Raw API response for updated purpose:", updatedPurpose);

      // Format the updated purpose using the existing formatting function
      const formattedPurposes = formatPurposesForTable({
        data: [updatedPurpose],
      });
      const formattedUpdatedPurpose = formattedPurposes[0];

      console.log(
        "Formatted updated purpose for table:",
        formattedUpdatedPurpose
      );

      // Update the purpose in state with formatted data
      setPurposes((prevPurposes) =>
        prevPurposes.map((purpose) =>
          purpose.id === originalItem.id ? formattedUpdatedPurpose : purpose
        )
      );

      setSuccessMessage("Purpose updated successfully!");
      setShowSuccessModal(true);
      console.log("Purpose updated in table state with proper formatting");
    } catch (error) {
      console.error("Error updating purpose in table:", error);
    }
  };

  const handleDeleteConfirm = async (purpose) => {
    try {
      console.log("Deleting purpose:", purpose);
      console.log(
        `Making DELETE request to /purpose-of-activities/${purpose.id}`
      );

      const success = await purposesService.delete(purpose.id);

      if (success) {
        // Remove the purpose from state
        setPurposes((prevPurposes) =>
          prevPurposes.filter((p) => p.id !== purpose.id)
        );
        setSuccessMessage("Purpose deleted successfully!");
        setShowSuccessModal(true);
        console.log("Purpose removed from table state");
      } else {
        throw new Error("Delete operation did not return success status");
      }
    } catch (error) {
      console.error("Error deleting purpose:", error);
    }
  };

  const handleView = (purpose) => {
    console.log("View purpose:", purpose);
    // Here you would typically navigate to view page or show view modal
  };

  const handleLoadMore = async () => {
    setLoadingMore(true);
    try {
      console.log("Loading more purposes...");
      // For now, just refresh the data
      // In a real implementation, you might have pagination
      await fetchPurposes();
    } catch (error) {
      console.error("Error loading more purposes:", error);
      console.error("Failed to load more purposes");
    } finally {
      setLoadingMore(false);
    }
  };

  return (
    <PrivateLayout perm_required={["purposes.view"]}>
      {/* Data Table */}
      <div className="">
        <DataTable
          columns={columns}
          data={purposes}
          searchPlaceholder="Search ..."
          addButtonText="New Purpose"
          onView={handleView}
          actions={[
            { name: "view", is_visible: () => hasPermission("purposes.view") },
            { name: "edit", is_visible: () => hasPermission("purposes.edit") },
            { name: "delete", is_visible: () => hasPermission("purposes.delete") },
          ]}
          loading={loading}
          loadingMore={loadingMore}
          loadMoreText="Load More Purposes"
          onLoadMore={handleLoadMore}
          showLoadMore={true}
          showCreateButton = {hasPermission("purposes.create") && true}
          allowMultiSelect={hasPermission("purposes.delete") && true}
          highlightField="addedBy"
          highlightColors={{
            BUSINESS:
              "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
            PERSONAL:
              "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",
          }}
          // Modal forms
          createForm={({ onClose }) => (
            <PurposeForm onClose={onClose} onSubmit={handleCreateSubmit} />
          )}
          editForm={({ item, onClose }) => (
            <PurposeForm
              item={item}
              onClose={onClose}
              onSubmit={handleEditSubmit}
            />
          )}
          deleteForm={({ item, onClose }) => (
            <DeleteConfirmation
              item={item}
              onClose={onClose}
              onConfirm={handleDeleteConfirm}
              itemName="Purpose"
            />
          )}
          createModalTitle="Create New Purpose"
          editModalTitle="Purpose"
          deleteModalTitle=""
          modalSize="lg"
          deleteModalSize="sm"
        />
      </div>

      {/* Success Modal */}
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        title="Success"
        message={successMessage}
      />
    </PrivateLayout>
  );
};

export default Purposes;
