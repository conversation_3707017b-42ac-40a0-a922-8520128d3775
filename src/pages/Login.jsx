import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Eye, EyeOff, Lock, Mail } from "lucide-react";
import { useAuth } from "../contexts/AuthContext";
import { usePermissions } from "../contexts/PermissionContext";
import PublicLayout from "../components/layouts/PublicLayout";
import logoSmall from "../assets/images/logo.png";
import logoText from "../assets/images/logo-text.png";

const Login = () => {
  const navigate = useNavigate();
  const { login } = useAuth();
  const { updatePermissions, updateRole } = usePermissions();
  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [apiError, setApiError] = useState("");

  // Form validation
  const validateForm = () => {
    const newErrors = {};

    // Email validation
    if (!formData.email) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear specific field error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  // This function is no longer needed as we're using authService directly

  const handleSubmit = async (e) => {
    e.preventDefault();
    setApiError("");

    // Validate form before submission
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      // Use the real authentication service with permission callback
      const response = await login(formData, (permissions, role) => {
        updatePermissions(permissions);
        updateRole(role);
      });

      // Check if MFA is required
      if (response.requiresMFA) {
        // Navigate to MFA method selection page
        navigate("/mfa/select-method", {
          state: {
            mfaMethods: response.mfaMethods,
            user: response.user,
          },
        });
      } else {
        localStorage.setItem("logged_in_user", JSON.stringify(response.user));
        navigate("/dashboard");
      }
    } catch (err) {
      // Handle different types of errors
      let errorMessage = "Login failed. Please try again.";

      if (err.response) {
        // Server responded with error status
        const status = err.response.status;
        if (status === 401) {
          errorMessage =
            "Invalid email or password. Please check your credentials.";
        } else if (status === 400) {
          errorMessage = "Please check your email and password format.";
        } else if (status === 500) {
          errorMessage = "Server error. Please try again later.";
        } else {
          errorMessage =
            err.response.data?.message || "Login failed. Please try again.";
        }
      } else if (err.request) {
        // Network error
        errorMessage =
          "Network error. Please check your connection and try again.";
      } else {
        // Other error
        errorMessage = err.message || "Login failed. Please try again.";
      }

      setApiError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <PublicLayout>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <div className="bg-white dark:bg-gray-800 shadow-lg rounded-lg border-0">
            {/* Logo Section */}
            <div className="flex items-center justify-center h-24 px-4">
              <div className="flex items-center gap-4">
                <span className="h-11 flex-shrink-0">
                  <img
                    className="logo-abbr h-full w-auto"
                    src={logoSmall}
                    alt=""
                  />
                </span>

                <span className="h-10 transition-opacity duration-300">
                  <img
                    className="brand-title h-full w-auto"
                    src={logoText}
                    alt=""
                  />
                </span>
              </div>
            </div>

            {/* Login Form */}

            <div className="p-6 pb-4">
              <h2 className="text-xl font-semibold text-center text-gray-900 dark:text-white mb-2">
                Sign in to your account
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400 text-center">
                Enter your credentials to access the banking system
              </p>
            </div>

            <div className="px-6 pb-6">
              <form onSubmit={handleSubmit} className="space-y-4">
                {apiError && (
                  <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-lg text-sm">
                    {apiError}
                  </div>
                )}

                <div className="space-y-2">
                  <label
                    htmlFor="email"
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    Email Address
                  </label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <input
                      id="email"
                      name="email"
                      type="email"
                      placeholder="Enter your email address"
                      value={formData.email}
                      onChange={handleInputChange}
                      className={`w-full pl-10 pr-4 py-3 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none transition-colors duration-200 ${
                        errors.email
                          ? "border-red-500 dark:border-red-400 focus:border-red-500"
                          : "border-gray-300 dark:border-gray-600 focus:border-green-500"
                      }`}
                      required
                    />
                  </div>
                  {errors.email && (
                    <p className="text-red-500 dark:text-red-400 text-sm mt-1">
                      {errors.email}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <label
                    htmlFor="password"
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    Password
                  </label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <input
                      id="password"
                      name="password"
                      type={showPassword ? "text" : "password"}
                      placeholder="Enter your password"
                      value={formData.password}
                      onChange={handleInputChange}
                      className={`w-full pl-10 pr-10 py-3 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none transition-colors duration-200 ${
                        errors.password
                          ? "border-red-500 dark:border-red-400 focus:border-red-500"
                          : "border-gray-300 dark:border-gray-600 focus:border-green-500"
                      }`}
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200"
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </button>
                  </div>
                  {errors.password && (
                    <p className="text-red-500 dark:text-red-400 text-sm mt-1">
                      {errors.password}
                    </p>
                  )}
                </div>

                <div className="flex items-center justify-between text-sm">
                  <label className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      className="rounded border-gray-300 dark:border-gray-600 text-green-600 focus:outline-none bg-white dark:bg-gray-700"
                    />
                    <span className="text-gray-600 dark:text-gray-400">
                      Remember me
                    </span>
                  </label>
                  <button
                    type="button"
                    onClick={() => navigate("/forgot-password")}
                    className="text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 font-medium transition-colors duration-200"
                  >
                    Forgot password?
                  </button>
                </div>

                <button
                  type="submit"
                  disabled={isLoading}
                  className="w-full py-3 bg-[#1c5b41] hover:bg-green-700 disabled:bg-green-700 text-white font-medium rounded-lg transition-colors duration-200 disabled:cursor-not-allowed"
                >
                  {isLoading ? (
                    <div className="flex items-center justify-center space-x-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      <span>Signing in...</span>
                    </div>
                  ) : (
                    "Sign In"
                  )}
                </button>
              </form>

              <div className="mt-6 text-center">
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Need help?{" "}
                  <a
                    href="#"
                    className="text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 font-medium transition-colors duration-200"
                  >
                    Contact Support
                  </a>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PublicLayout>
  );
};

export default Login;
