import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { InputOtp } from "primereact/inputotp";
import { Button } from "primereact/button";
import PublicLayout from "../components/layouts/PublicLayout";
import { authService } from "../services/authService";
import logoSmall from "../assets/images/logo.png";
import logoText from "../assets/images/logo-text.png";

const ResetVerification = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [token, setToken] = useState();
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [apiError, setApiError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [email, setEmail] = useState("");
  const [timeRemaining, setTimeRemaining] = useState(0);
  const [isExpired, setIsExpired] = useState(false);

  useEffect(() => {
    // Get email from navigation state
    if (location.state?.email) {
      setEmail(location.state.email);
    } else {
      // If no email in state, redirect back to request reset
      navigate("/forgot-password");
    }
  }, [location.state, navigate]);

  // Timer functionality
  useEffect(() => {
    const updateTimer = () => {
      const expirationTime = localStorage.getItem("otpExpirationTime");
      if (expirationTime) {
        const now = new Date().getTime();
        const timeLeft = parseInt(expirationTime) - now;

        if (timeLeft > 0) {
          setTimeRemaining(timeLeft);
          setIsExpired(false);
        } else {
          setTimeRemaining(0);
          setIsExpired(true);
        }
      }
    };

    // Update timer immediately
    updateTimer();

    // Update timer every second
    const interval = setInterval(updateTimer, 1000);

    return () => clearInterval(interval);
  }, []);

  // Format time remaining as MM:SS
  const formatTime = (milliseconds) => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${seconds
      .toString()
      .padStart(2, "0")}`;
  };

  const customInput = ({ events, props }) => {
    // Filter out boolean attributes that shouldn't be passed to DOM
    const { invalid, unstyled, ...domProps } = props;

    // Handle input change to auto-capitalize
    const handleInputChange = (e) => {
      const value = e.target.value.toUpperCase();
      e.target.value = value;
      if (events.onInput) {
        events.onInput(e);
      }
    };

    return (
      <>
        <input
          {...events}
          {...domProps}
          type="text"
          className="custom-otp-input"
          onInput={handleInputChange}
          style={{ textTransform: "uppercase" }}
        />
        {props.id === 2 && (
          <div className="px-3">
            <i className="pi pi-minus" />
          </div>
        )}
      </>
    );
  };

  const handleSubmit = async () => {
    setApiError("");
    setSuccessMessage("");

    if (!token || token.length !== 6) {
      setApiError("Please enter the complete 6-digit code");
      return;
    }

    setIsLoading(true);

    try {
      // Log the submitted data to console for now
      console.log("OTP Verification Data:", { email, otp: token });

      // Call the API endpoint
      const response = await authService.verifyResetOTP(email, token, token);

      // Clear the timer since verification was successful
      localStorage.removeItem("otpExpirationTime");

      // Navigate to reset password page
      navigate("/reset-password", { state: { email, token } });
    } catch (err) {
      let errorMessage = "Invalid verification code. Please try again.";

      if (err.response) {
        const status = err.response.status;
        if (status === 400) {
          errorMessage =
            err.response.data?.message ||
            "Invalid or expired verification code.";
        } else if (status === 500) {
          errorMessage = "Server error. Please try again later.";
        } else {
          errorMessage =
            err.response.data?.message ||
            "Invalid verification code. Please try again.";
        }
      } else if (err.request) {
        errorMessage =
          "Network error. Please check your connection and try again.";
      } else {
        errorMessage =
          err.message || "Invalid verification code. Please try again.";
      }

      setApiError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendCode = async () => {
    setApiError("");
    setSuccessMessage("");
    setIsResending(true);

    try {
      // Log the resend request to console for now
      console.log("Resend OTP Request:", { email });

      // Call the API endpoint to resend OTP
      await authService.requestPasswordReset(email);

      // Clear the OTP input
      setToken("");

      // Show success message
      setSuccessMessage("Verification code sent successfully!");
      setTimeout(() => {
        setSuccessMessage("");
      }, 3000);

      // Reset timer for new OTP (5 minutes from now)
      const expirationTime = new Date().getTime() + 5 * 60 * 1000;
      localStorage.setItem("otpExpirationTime", expirationTime.toString());
    } catch (err) {
      setApiError("Failed to resend code. Please try again.");
    } finally {
      setIsResending(false);
    }
  };

  return (
    <PublicLayout>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <div className="bg-white dark:bg-gray-800 shadow-lg rounded-lg border-0">
            {/* Logo Section */}
            <div className="flex items-center justify-center h-24 px-4">
              <div className="flex items-center gap-4">
                <span className="h-11 flex-shrink-0">
                  <img
                    className="logo-abbr h-full w-auto"
                    src={logoSmall}
                    alt=""
                  />
                </span>

                <span className="h-10 transition-opacity duration-300">
                  <img
                    className="brand-title h-full w-auto"
                    src={logoText}
                    alt=""
                  />
                </span>
              </div>
            </div>

            {/* Verification Form */}
            <div className="p-6">
              <div className="flex flex-col items-center">
                {/* Success Badge */}
                {successMessage && (
                  <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-600 dark:text-green-400 px-4 py-4 rounded-lg text-[15px] mb-4 w-full text-center">
                    {successMessage}
                  </div>
                )}

                {/* Error Badge */}
                {apiError && (
                  <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-4 rounded-lg text-[15px] mb-4 w-full text-center">
                    {apiError}
                  </div>
                )}

                <p className="text-[16px] text-center text-gray-900 dark:text-white mb-2">
                  Enter the OTP code we sent to your email{" "}
                  <strong>{email}</strong>
                </p>

                <InputOtp
                  value={token}
                  onChange={(e) =>
                    setToken(e.value ? e.value.toUpperCase() : "")
                  }
                  length={6}
                  inputTemplate={customInput}
                  style={{ gap: 0 }}
                  key="otp-input"
                  className="mb-6 flex flex-col w-full bg-red-200"
                />

                <div className="flex justify-between mt-5 w-full">
                  <div className="flex items-center">
                    {/* Timer Display */}
                    {timeRemaining > 0 && (
                      <p className="text-[15px] ">
                        Resend code in:{" "}
                        <span className=" font-medium">
                          {formatTime(timeRemaining)}
                        </span>
                      </p>
                    )}
                    {timeRemaining <= 0 && (
                      <Button
                        label={isResending ? "Sending..." : "Resend Code"}
                        link
                        className="p-0 text-green-600 font-semibold hover:text-green-700 dark:text-green-400 dark:hover:text-green-300"
                        onClick={handleResendCode}
                        disabled={isResending}
                      />
                    )}
                  </div>
                  <Button
                    label={isLoading ? "Verifying..." : "Submit Code"}
                    className="bg-[#1c5b41] hover:bg-green-700 disabled:opacity-[0.7] text-white border-green-600
                     hover:border-green-700 py-3 px-4 rounded-[0.5rem]"
                    onClick={handleSubmit}
                    disabled={isLoading || !token || token.length !== 6}
                  />
                </div>

                <div className="text-center mt-4">
                  <button
                    type="button"
                    onClick={() => navigate("/forgot-password")}
                    className="text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 font-medium transition-colors duration-200 text-sm"
                  >
                    Back to Request Reset
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PublicLayout>
  );
};

export default ResetVerification;
