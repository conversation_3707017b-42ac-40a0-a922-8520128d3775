import { useState, useEffect } from "react";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import PrivateLayout from "../components/layouts/PrivateLayout";
import {
  Settings,
  FileText,
  CreditCard,
  DollarSign,
  Users,
  Shield,
  TrendingUp,
  Building2,
} from "lucide-react";
import {
  permissionsService,
  rolesService,
  getCategoryIcon,
  getCategoryColors,
} from "../services/permissionsService";

const RoleConfiguration = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { id } = useParams(); // For editing existing roles
  const isEditing = Boolean(id);

  const [formData, setFormData] = useState({
    name: "",
    description: "",
    permissions: [],
  });

  const [selectedPermissions, setSelectedPermissions] = useState(new Set());
  const [permissionCategories, setPermissionCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // Icon mapping for categories
  const getIconComponent = (iconName) => {
    const icons = {
      CreditCard,
      Settings,
      TrendingUp,
      Users,
      Building2,
      FileText,
      DollarSign,
      Shield,
    };
    return icons[iconName] || Settings;
  };

  // Fetch permissions and role data
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch permissions grouped by categories
        const groupedPermissions =
          await permissionsService.getPermissionsGrouped();

        // Add icon and color information to categories
        const categoriesWithIcons = groupedPermissions.map((category) => {
          const iconName = getCategoryIcon(category.id);
          const colors = getCategoryColors(category.id);

          return {
            ...category,
            icon: getIconComponent(iconName),
            color: colors.color,
            bgColor: colors.bgColor,
          };
        });

        setPermissionCategories(categoriesWithIcons);

        // If editing, fetch the role data
        if (isEditing) {
          const roleData = await rolesService.getById(id);
          setFormData({
            name: roleData.name || "",
            description: roleData.description || "",
            permissions: roleData.permissions || [],
          });

          // Set selected permissions from role data
          const rolePermissionIds =
            roleData.permissions?.map((p) => p.id || p) || [];
          setSelectedPermissions(new Set(rolePermissionIds));
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        // Navigate back with error message
        navigate("/admin/roles", {
          state: { errorMessage: "Failed to load permissions data" },
        });

        // Set some mock data so the component doesn't break
        const mockCategories = [
          {
            id: "items-management",
            name: "Items Management",
            description: "items",
            icon: getIconComponent("CreditCard"),
            color: "text-blue-600",
            bgColor: "bg-blue-50",
            permissions: [
              {
                id: "1",
                name: "View Items",
                description: "items",
                action: "view",
                resource: "items",
              },
              {
                id: "2",
                name: "Create Items",
                description: "items",
                action: "create",
                resource: "items",
              },
            ],
          },
          {
            id: "administration-management",
            name: "Administration Management",
            description: "administration",
            icon: getIconComponent("Building2"),
            color: "text-teal-600",
            bgColor: "bg-teal-50",
            permissions: [
              {
                id: "3",
                name: "View Users",
                description: "administration",
                action: "view",
                resource: "users",
              },
              {
                id: "4",
                name: "Create Users",
                description: "administration",
                action: "create",
                resource: "users",
              },
            ],
          },
        ];

        setPermissionCategories(mockCategories);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [isEditing, id]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handlePermissionToggle = (permissionId, categoryId) => {
    const newSelected = new Set(selectedPermissions);

    // Special handling for Dashboard Management category (only one selection allowed)
    if (categoryId === "dashboard-management") {
      // First, remove any existing dashboard management permissions
      const dashboardCategory = permissionCategories.find(
        (cat) => cat.id === "dashboard-management"
      );
      if (dashboardCategory) {
        dashboardCategory.permissions.forEach((p) => newSelected.delete(p.id));
      }
      // Then add the newly selected permission
      newSelected.add(permissionId);
    } else {
      // Normal behavior for other categories
      if (newSelected.has(permissionId)) {
        newSelected.delete(permissionId);
      } else {
        newSelected.add(permissionId);
      }
    }
    setSelectedPermissions(newSelected);
  };

  const handleCategoryToggle = (category) => {
    // Dashboard Management doesn't have select all functionality
    if (category.id === "dashboard-management") {
      return;
    }

    const categoryPermissions = category.permissions.map((p) => p.id);
    const allSelected = categoryPermissions.every((p) =>
      selectedPermissions.has(p)
    );

    const newSelected = new Set(selectedPermissions);
    if (allSelected) {
      // Unselect all in category
      categoryPermissions.forEach((p) => newSelected.delete(p));
    } else {
      // Select all in category
      categoryPermissions.forEach((p) => newSelected.add(p));
    }
    setSelectedPermissions(newSelected);
  };

  const isCategorySelected = (category) => {
    // Dashboard Management category is never considered "fully selected" since only one permission is allowed
    if (category.id === "dashboard-management") {
      return false;
    }
    const categoryPermissions = category.permissions.map((p) => p.id);
    return categoryPermissions.every((p) => selectedPermissions.has(p));
  };

  const getCategorySelectedCount = (category) => {
    // For Dashboard Management, only one permission can be selected
    if (category.id === "dashboard-management") {
      const selected = category.permissions.filter((p) =>
        selectedPermissions.has(p.id)
      );
      return selected.length > 0 ? 1 : 0;
    }
    return category.permissions.filter((p) => selectedPermissions.has(p.id))
      .length;
  };

  const handleSave = async () => {
    if (!formData.name.trim()) {
      // Show error in a more appropriate way
      console.error("Role name is required");
      return;
    }

    if (selectedPermissions.size === 0) {
      // Show error in a more appropriate way
      console.error("At least one permission must be selected");
      return;
    }

    setSaving(true);
    try {
      const roleData = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        permissions: Array.from(selectedPermissions),
      };

      console.log(
        "Creating role:",
        roleData.name,
        "with",
        roleData.permissions.length,
        "permissions"
      );

      if (isEditing) {
        await rolesService.update(id, roleData);
        // Navigate back to roles list after successful save with success message
        navigate("/admin/roles", {
          state: { successMessage: "Role updated successfully!" },
        });
      } else {
        await rolesService.create(roleData);
        // Navigate back to roles list after successful save with success message
        navigate("/admin/roles", {
          state: { successMessage: "Role created successfully!" },
        });
      }
    } catch (error) {
      console.error("Error saving role:", error);
      // Error handling is done by the service
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    navigate("/admin/roles");
  };

  if (loading) {
    return (
      <PrivateLayout perm_required={["roles.create"]}>
        <div className="max-w-full mx-auto space-y-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <div className="animate-pulse space-y-4">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
              <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div className="h-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <div className="animate-pulse space-y-4">
              <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
              <div className="space-y-3">
                {[1, 2, 3].map((i) => (
                  <div
                    key={i}
                    className="h-16 bg-gray-200 dark:bg-gray-700 rounded"
                  ></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </PrivateLayout>
    );
  }

  return (
    <PrivateLayout>
      <div className="max-w-full mx-auto space-y-6">
        {/* Role Configuration Section */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-2">
              <Settings className="h-5 w-5 text-gray-600 dark:text-gray-400" />
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                Role Configuration
              </h2>
            </div>
          </div>

          <div className="p-6">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Role Name *
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="e.g., Branch Manager, Teller, Loan Officer"
                  className="w-full px-3 py-2 border border-gray-300 outline-none dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Description
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder="Describe the role and its responsibilities"
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 outline-none dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white "
                />
              </div>
            </div>
          </div>
        </div>

        {/* System Permissions Section */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <FileText className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                  System Permissions
                </h2>
              </div>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {selectedPermissions.size} selected
              </span>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {/* Configure access permissions for this role */}
            </p>
          </div>

          <div className="p-6 space-y-6">
            {permissionCategories.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500 dark:text-gray-400">
                  No permissions available. Please check your backend
                  configuration.
                </p>
              </div>
            ) : (
              permissionCategories.map((category) => {
                const IconComponent = category.icon;
                const selectedCount = getCategorySelectedCount(category);
                const isAllSelected = isCategorySelected(category);

                return (
                  <div
                    key={category.id}
                    className="border border-gray-200 dark:border-gray-600 rounded-lg"
                  >
                    <div className="p-4 border-b border-gray-200 dark:border-gray-600">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div
                            className={`p-2 rounded-lg ${category.bgColor} dark:bg-gray-700`}
                          >
                            <IconComponent
                              className={`h-4 w-4 ${category.color} dark:text-gray-300`}
                            />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900 dark:text-white">
                              {category.name}
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              {selectedCount}/{category.permissions.length}{" "}
                              permissions
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {category.id !== "dashboard-management" ? (
                            <>
                              <input
                                type="checkbox"
                                id={`select-all-${category.id}`}
                                checked={isAllSelected}
                                onChange={() => handleCategoryToggle(category)}
                                className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                              />
                              <label
                                htmlFor={`select-all-${category.id}`}
                                className="text-sm font-medium text-gray-900 dark:text-white cursor-pointer"
                              >
                                Select All
                              </label>
                            </>
                          ) : (
                            <span className="text-sm text-gray-500 dark:text-gray-400">
                              Only one permission allowed
                            </span>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="p-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                        {category.permissions.map((permission) => (
                          <div
                            key={permission.id}
                            className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600"
                          >
                            <input
                              type="checkbox"
                              id={permission.id}
                              checked={selectedPermissions.has(permission.id)}
                              onChange={() =>
                                handlePermissionToggle(
                                  permission.id,
                                  category.id
                                )
                              }
                              className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                            />
                            <label
                              htmlFor={permission.id}
                              className="text-sm font-medium text-gray-900 dark:text-white cursor-pointer flex-1"
                            >
                              {permission.name}
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                );
              })
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3">
          <button
            onClick={handleCancel}
            className="px-6 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-200"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={saving}
            className="px-6 py-2 text-sm font-medium text-white bg-[#165026] hover:bg-green-700 rounded-lg transition-colors duration-200 flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {saving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Saving...</span>
              </>
            ) : (
              <>
                <FileText className="h-4 w-4" />
                <span>{isEditing ? "Update Role" : "Create Role"}</span>
              </>
            )}
          </button>
        </div>
      </div>
    </PrivateLayout>
  );
};

export default RoleConfiguration;
