import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

import PublicLayout from "../components/layouts/PublicLayout";
import logoSmall from "../assets/images/logo.png";
import logoText from "../assets/images/logo-text.png";

const ResetSuccess = () => {
  const navigate = useNavigate();
  const [showAnimation, setShowAnimation] = useState(false);

  useEffect(() => {
    // Trigger animation after component mounts
    const timer = setTimeout(() => {
      setShowAnimation(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  const handleBackToLogin = () => {
    navigate("/login");
  };

  return (
    <PublicLayout>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <div className="bg-white dark:bg-gray-800 shadow-lg rounded-lg border-0">
            {/* Logo Section */}
            {/* <div className="flex items-center justify-center h-24 px-4">
              <div className="flex items-center gap-4">
                <span className="h-11 flex-shrink-0">
                  <img
                    className="logo-abbr h-full w-auto"
                    src={logoSmall}
                    alt=""
                  />
                </span>

                <span className="h-10 transition-opacity duration-300">
                  <img
                    className="brand-title h-full w-auto"
                    src={logoText}
                    alt=""
                  />
                </span>
              </div>
            </div> */}

            {/* Success Content */}
            <div className="p-6 text-center">
              <style scoped>
                {`
                  @keyframes checkmark-draw {
                    0% {
                      stroke-dasharray: 0 100;
                      opacity: 0;
                    }
                    50% {
                      opacity: 1;
                    }
                    100% {
                      stroke-dasharray: 100 0;
                      opacity: 1;
                    }
                  }

                  @keyframes checkmark-scale {
                    0% {
                      transform: scale(0);
                    }
                    50% {
                      transform: scale(1.1);
                    }
                    100% {
                      transform: scale(1);
                    }
                  }

                  @keyframes circle {
                    0% {
                      transform: scale(0);
                      opacity: 0;
                    }
                    50% {
                      transform: scale(1.1);
                      opacity: 1;
                    }
                    100% {
                      transform: scale(1);
                      opacity: 1;
                    }
                  }

                  .success-circle {
                    width: 120px;
                    height: 120px;
                    border-radius: 50%;
                    background: linear-gradient(135deg, #10b981, #059669);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 0 auto 2rem;
                    position: relative;
                    box-shadow: 0 10px 30px rgba(16, 185, 129, 0.3);
                  }

                  .success-circle::before {
                    content: '';
                    position: absolute;
                    width: 140px;
                    height: 140px;
                    border-radius: 50%;
                    background: rgba(16, 185, 129, 0.1);
                    animation: circle 0.6s ease-out;
                  }

                  .success-circle.animate {
                    animation: circle 0.6s ease-out;
                  }

                  .checkmark-container {
                    position: relative;
                    z-index: 1;
                    width: 60px;
                    height: 60px;
                  }

                  .checkmark-container.animate {
                    animation: checkmark-scale 0.6s ease-out 0.3s both;
                  }

                  .checkmark-svg {
                    width: 100%;
                    height: 100%;
                  }

                  .checkmark-path {
                    fill: none;
                    stroke: white;
                    stroke-width: 6;
                    stroke-linecap: round;
                    stroke-linejoin: round;
                    stroke-dasharray: 100;
                    stroke-dashoffset: 100;
                  }

                  .checkmark-path.animate {
                    animation: checkmark-draw 0.8s ease-out 0.5s both;
                  }


                `}
              </style>

              {/* Animated Success Icon */}
              <div
                className={`success-circle ${showAnimation ? "animate" : ""}`}
              >
                <div
                  className={`checkmark-container ${
                    showAnimation ? "animate" : ""
                  }`}
                >
                  <svg className="checkmark-svg" viewBox="0 0 100 100">
                    <path
                      className={`checkmark-path ${
                        showAnimation ? "animate" : ""
                      }`}
                      d="M20,50 L40,70 L80,30"
                    />
                  </svg>
                </div>
              </div>

              {/* Success Message */}
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-3">
                  Success
                </h2>
              </div>

              <div>
                <p className="text-gray-600 dark:text-gray-400 mb-8 text-lg">
                  Password Reset Successfully
                </p>
              </div>

              {/* Action Button */}
              <div>
                <button
                  onClick={handleBackToLogin}
                  className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 transform  focus:outline-none focus:ring-4 focus:ring-green-200 dark:focus:ring-green-800"
                >
                  Back to Login
                </button>
              </div>

              {/* Additional Info */}
              <div className="mt-6">
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  You can now sign in with your new password
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PublicLayout>
  );
};

export default ResetSuccess;
