import React, { useState, useEffect } from "react";
import {
  Bell,
  Check,
  Trash2,
  Filter,
  Calendar,
  User,
  AlertCircle,
  CheckCircle,
  Info,
  X,
  AlertTriangle,
  FileText,
  TrendingUp,
} from "lucide-react";
import PrivateLayout from "../components/layouts/PrivateLayout";

const Notifications = () => {
  const [notifications, setNotifications] = useState([]);
  const [filter, setFilter] = useState("all"); // all, unread, read
  const [loading, setLoading] = useState(true);

  // Demo data - replace with API call later
  const demoNotifications = [
    {
      id: 1,
      title: "High-Risk Loan Application",
      message:
        "Loan application #LA-2024-1589 requires immediate review. Credit score below threshold (580) with high debt-to-income ratio.",
      type: "error",
      read: false,
      timestamp: new Date(Date.now() - 2 * 60 * 1000), // 2 minutes ago
      category: "Loans",
      icon: "AlertTriangle",
    },
    {
      id: 2,
      title: "Customer Meeting Scheduled",
      message:
        "Premium client <PERSON> (Account #AC-44891) has scheduled a meeting today at 2:00 PM to discuss investment portfolio restructuring.",
      type: "warning",
      read: false,
      timestamp: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
      category: "Customer",
      icon: "Calendar",
    },
    {
      id: 3,
      title: "Compliance Document Required",
      message:
        "KYC documentation pending for business account #BA-78954. Customer has 48 hours to submit required documents.",
      type: "info",
      read: false,
      timestamp: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago
      category: "Compliance",
      icon: "FileText",
    },
    {
      id: 4,
      title: "Monthly Target Achievement",
      message:
        "Congratulations! You have achieved 92% of your monthly loan origination target with 5 days remaining in the cycle.",
      type: "success",
      read: true,
      timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000), // 3 hours ago
      category: "Performance",
      icon: "TrendingUp",
    },
  ];

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setNotifications(demoNotifications);
      setLoading(false);
    }, 1000);
  }, []);

  const getNotificationIcon = (notification) => {
    const iconProps = { className: "w-5 h-5" };

    switch (notification.icon) {
      case "AlertTriangle":
        return <AlertTriangle {...iconProps} />;
      case "Calendar":
        return <Calendar {...iconProps} />;
      case "FileText":
        return <FileText {...iconProps} />;
      case "TrendingUp":
        return <TrendingUp {...iconProps} />;
      default:
        return <Info {...iconProps} />;
    }
  };

  const getCategoryColor = (category) => {
    switch (category) {
      case "Loans":
        return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
      case "Customer":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400";
      case "Compliance":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
      case "Performance":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  const formatTimestamp = (timestamp) => {
    const now = new Date();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 60) {
      return `${minutes}m ago`;
    } else if (hours < 24) {
      return `${hours}h ago`;
    } else {
      return `${days}d ago`;
    }
  };

  const markAsRead = (id) => {
    setNotifications((prev) =>
      prev.map((notif) => (notif.id === id ? { ...notif, read: true } : notif))
    );
  };

  const markAllAsRead = () => {
    setNotifications((prev) => prev.map((notif) => ({ ...notif, read: true })));
  };

  const deleteNotification = (id) => {
    setNotifications((prev) => prev.filter((notif) => notif.id !== id));
  };

  const filteredNotifications = notifications.filter((notif) => {
    if (filter === "unread") return !notif.read;
    if (filter === "read") return notif.read;
    return true;
  });

  const unreadCount = notifications.filter((notif) => !notif.read).length;

  if (loading) {
    return (
      <PrivateLayout pageTitle="Notifications">
        <div className="space-y-4">
          {[1, 2, 3, 4, 5].map((item) => (
            <div
              key={item}
              className="bg-white dark:bg-gray-800 rounded-lg p-4 animate-pulse"
            >
              <div className="flex items-start space-x-4">
                <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </PrivateLayout>
    );
  }

  return (
    <PrivateLayout pageTitle="Notifications">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center space-x-4">
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
              Notifications
            </h1>
            {unreadCount > 0 && (
              <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-500 text-white">
                {unreadCount} unread
              </span>
            )}
          </div>

          <div className="flex items-center space-x-3">
            {/* Filter */}
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-green-500 focus:border-transparent"
            >
              <option value="all">All</option>
              <option value="unread">Unread</option>
              <option value="read">Read</option>
            </select>

            {/* Mark all as read */}
            {unreadCount > 0 && (
              <button
                onClick={markAllAsRead}
                className="inline-flex items-center px-4 py-2 bg-green-500 hover:bg-green-600 text-white text-sm rounded-lg transition-colors duration-200 font-medium"
              >
                Mark all read
              </button>
            )}
          </div>
        </div>

        {/* Notifications List */}
        <div className="space-y-3">
          {filteredNotifications.length === 0 ? (
            <div className="text-center py-12">
              <Bell className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                No notifications
              </h3>
              <p className="text-gray-500 dark:text-gray-400">
                {filter === "unread"
                  ? "You're all caught up! No unread notifications."
                  : filter === "read"
                  ? "No read notifications to show."
                  : "You don't have any notifications yet."}
              </p>
            </div>
          ) : (
            filteredNotifications.map((notification) => (
              <div
                key={notification.id}
                className={`bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 transition-all duration-200 hover:shadow-md ${
                  !notification.read ? "shadow-sm" : ""
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3 flex-1">
                    <div className="flex-shrink-0 mt-1">
                      {getNotificationIcon(notification)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <h3 className="text-base font-medium text-gray-900 dark:text-white">
                          {notification.title}
                        </h3>
                        {!notification.read && (
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        )}
                        <span
                          className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getCategoryColor(
                            notification.category
                          )}`}
                        >
                          {notification.category}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                        {notification.message}
                      </p>
                      <p className="text-xs text-gray-400 dark:text-gray-500">
                        {formatTimestamp(notification.timestamp)}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 ml-4">
                    {!notification.read && (
                      <button
                        onClick={() => markAsRead(notification.id)}
                        className="p-1 text-gray-400 hover:text-green-600 transition-colors duration-200"
                        title="Mark as read"
                      >
                        <Check className="w-4 h-4" />
                      </button>
                    )}
                    <button
                      onClick={() => deleteNotification(notification.id)}
                      className="p-1 text-gray-400 hover:text-red-600 transition-colors duration-200"
                      title="Delete notification"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </PrivateLayout>
  );
};

export default Notifications;
