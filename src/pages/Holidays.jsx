import { useState, useEffect } from "react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import DeleteConfirmation from "../components/forms/DeleteConfirmation";
import HolidayForm from "../components/forms/HolidayForm";
import SuccessModal from "../components/modals/SuccessModal";
import ErrorModal from "../components/modals/ErrorModal";
import { holidaysService } from "../services/holidaysService";

const Holidays = () => {
  const [holidays, setHolidays] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  // Filter states
  const [filters, setFilters] = useState({
    status: "", // past, upcoming
  });

  // Load holidays from API
  useEffect(() => {
    fetchHolidays();
  }, []);

  const fetchHolidays = async () => {
    try {
      setLoading(true);
      const data = await holidaysService.getAll();
      setHolidays(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error("Error fetching holidays:", error);
      setErrorMessage("Failed to load holidays. Please try again.");
      setShowErrorModal(true);
      setHolidays([]);
    } finally {
      setLoading(false);
    }
  };

  // Format date for display (date only) - day/month/year format
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    try {
      return new Date(dateString).toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric",
      });
    } catch {
      return "Invalid Date";
    }
  };

  // Format datetime for display (added_on) - day/month/year format
  const formatDateTime = (dateString) => {
    if (!dateString) return "N/A";
    try {
      const date = new Date(dateString);
      const dateStr = date.toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric",
      });
      const timeStr = date.toLocaleTimeString("en-GB", {
        hour: "2-digit",
        minute: "2-digit",
      });
      return `${dateStr} ${timeStr}`;
    } catch {
      return "Invalid Date";
    }
  };

  // Define columns of the table
  const columns = [
    {
      key: "name",
      title: "HOLIDAY NAME",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "date",
      title: "DATE",
      render: (value) => (
        <span className="text-gray-700 dark:text-gray-300">
          {formatDate(value)}
        </span>
      ),
    },
    {
      key: "user",
      title: "ADDED BY",
      render: (value) => (
        <span className="text-gray-700 dark:text-gray-300">
          {value || "N/A"}
        </span>
      ),
    },
    {
      key: "created_at",
      title: "ADDED ON",
      render: (value) => (
        <span className="text-gray-700 dark:text-gray-300">
          {formatDateTime(value)}
        </span>
      ),
    },
  ];

  // Filter configuration
  const filterConfig = [
    {
      key: "status",
      label: "Status",
      field: "status",
      placeholder: "All Holidays",
      selectedValue: filters.status,
      options: [
        { value: "past", label: "Past" },
        { value: "upcoming", label: "Upcoming" },
      ],
    },
  ];

  // Filter data based on status
  const filteredHolidays = holidays.filter((holiday) => {
    if (filters.status === "") return true;

    const holidayDate = new Date(holiday.date);
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Reset time to start of day

    if (filters.status === "past") {
      return holidayDate < today;
    } else if (filters.status === "upcoming") {
      return holidayDate >= today;
    }

    return true;
  });

  // Filter handlers
  const handleFilterChange = (filterKey, value) => {
    setFilters((prev) => ({
      ...prev,
      [filterKey]: value,
    }));
  };

  const handleClearFilters = () => {
    setFilters({
      status: "",
    });
  };

  // CRUD handlers
  const handleCreateSubmit = async (newHoliday) => {
    try {
      console.log("Created holiday:", newHoliday);

      // Add to state
      setHolidays((prev) => {
        return [...prev, newHoliday];
      });

      setSuccessMessage("Holiday created successfully!");
      setShowSuccessModal(true);
    } catch (error) {
      console.error("Error creating holiday:", error);
      setErrorMessage(error.message || "Failed to create holiday");
      setShowErrorModal(true);
    }
  };

  const handleEditSubmit = async (updatedHoliday) => {
    try {
      console.log("Updated holiday:", updatedHoliday);

      // Update state
      setHolidays((prev) =>
        prev.map((holiday) =>
          holiday.id === updatedHoliday.id ? updatedHoliday : holiday
        )
      );

      setSuccessMessage("Holiday updated successfully!");
      setShowSuccessModal(true);
    } catch (error) {
      console.error("Error updating holiday:", error);
      setErrorMessage(error.message || "Failed to update holiday");
      setShowErrorModal(true);
    }
  };

  const handleDeleteConfirm = async (holiday) => {
    try {
      await holidaysService.delete(holiday.id);
      console.log("Deleted holiday:", holiday);

      // Remove from state
      setHolidays((prev) =>
        prev.filter((h) => {
          setSuccessMessage("Holiday deleted successfully!");
          setShowSuccessModal(true);
          return h.id !== holiday.id;
        })
      );
    } catch (error) {
      console.error("Error deleting holiday:", error);
      setErrorMessage(error.message || "Failed to delete holiday");
      setShowErrorModal(true);
    }
  };

  // Export and print handlers
  const handleExport = () => {
    // Export functionality can be implemented here
    console.log("Export functionality will be implemented");
  };

  const handlePrint = () => {
    // Print functionality can be implemented here
    console.log("Print functionality will be implemented");
  };

  return (
    <PrivateLayout pageTitle="Holidays & Offdays">
      <div className="">
        <DataTable
          columns={columns}
          data={filteredHolidays}
          searchPlaceholder="Search holidays..."
          addButtonText="New Holiday"
          actions={["edit", "delete"]}
          loading={loading}
          dataCountLabel="holidays"
          showDataCount={true}
          // Filters
          showFilters={true}
          filterConfig={filterConfig}
          onFilterChange={handleFilterChange}
          onClearFilters={handleClearFilters}
          // Import/Export - only export and print
          showImportExport={true}
          showImport={false}
          onExport={handleExport}
          onPrint={handlePrint}
          // Modal forms
          createForm={({ onClose }) => (
            <HolidayForm onClose={onClose} onSubmit={handleCreateSubmit} />
          )}
          editForm={({ item, onClose }) => (
            <HolidayForm
              item={item}
              onClose={onClose}
              onSubmit={handleEditSubmit}
            />
          )}
          deleteForm={({ item, onClose }) => (
            <DeleteConfirmation
              item={item}
              onClose={onClose}
              onConfirm={handleDeleteConfirm}
              itemName="Holiday"
            />
          )}
          createModalTitle="Create New Holiday"
          editModalTitle="Edit Holiday"
          deleteModalTitle=""
          modalSize="lg"
          deleteModalSize="sm"
        />
      </div>

      {/* Success Modal */}
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        title="Success"
        message={successMessage}
      />

      {/* Error Modal */}
      <ErrorModal
        isOpen={showErrorModal}
        onClose={() => setShowErrorModal(false)}
        title="Error"
        message={errorMessage}
      />
    </PrivateLayout>
  );
};

export default Holidays;
