import { useState, useEffect, useCallback } from "react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import DeleteConfirmation from "../components/forms/DeleteConfirmation";
import CustomerCategoryForm from "../components/forms/CustomerCategoryForm";
import SuccessModal from "../components/modals/SuccessModal";
import { customerCategoriesService } from "../services/customerCategoriesService";
import { formatDateTimeDisplay } from "../utils/dateUtils";
import { hasPermission } from "../utils/permissionUtils";

const CustomerCategories = () => {
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [categories, setCategories] = useState([]);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");

  // Define columns of the table here and what to render
  const columns = [
    {
      key: "name",
      title: "NAME",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "customers_count",
      title: "CUSTOMERS COUNT",
      render: (value) => (
        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full dark:text-blue-400">
          {value || 0}
        </span>
      ),
    },
    {
      key: "added_on",
      title: "ADDED ON",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {formatDateTimeDisplay(value)}
        </span>
      ),
    },
    {
      key: "added_by",
      title: "ADDED BY",
      render: (value) => (
        <span className="inline-flex px-2 py-1 font-semibold rounded-full dark:bg-green-900/20">
          {value || "N/A"}
        </span>
      ),
    },
  ];

  // Fetch categories data
  const fetchCategories = useCallback(async () => {
    setLoading(true);
    try {
      const data = await customerCategoriesService.getAll();
      // Ensure data is always an array and sort by added_on
      const categoriesArray = Array.isArray(data.data) ? data.data : [];
      const sortedCategories = categoriesArray.sort(
        (a, b) => new Date(a.added_on) - new Date(b.added_on)
      );
      setCategories(sortedCategories);
    } catch (error) {
      console.error("Error fetching customer categories:", error);
      // Set empty array on error to prevent filter issues
      setCategories([]);
      // Error is handled by the API context interceptor
    } finally {
      setLoading(false);
    }
  }, []);

  // Load categories on component mount
  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  // Form submission handlers
  const handleCreateSubmit = (result) => {
    console.log("Created customer category:", result);
    // Add to state instead of refreshing - access result.data
    setCategories((prev) => {
      const newCategories = [...prev, result.data];
      // Sort by added_on field in ascending order
      return newCategories.sort(
        (a, b) => new Date(a.added_on) - new Date(b.added_on)
      );
    });
    setSuccessMessage("Customer category created successfully!");
    setShowSuccessModal(true);
  };

  const handleEditSubmit = (result) => {
    console.log("Updated customer category:", result);
    // Update in state instead of refreshing - access result.data
    setCategories((prev) => {
      const updatedCategories = prev.map((category) =>
        category.id === result.id
          ? { ...category, name: result.name }
          : category
      );
      // Sort by added_on field in ascending order
      return updatedCategories.sort(
        (a, b) => new Date(a.added_on) - new Date(b.added_on)
      );
    });
    setSuccessMessage("Customer category updated successfully!");
    setShowSuccessModal(true);
  };

  const handleDeleteConfirm = async (category) => {
    try {
      await customerCategoriesService.delete(category.id);
      console.log("Deleted customer category:", category);
      // Remove from state instead of refreshing
      setCategories((prev) => prev.filter((cat) => cat.id !== category.id));
      setSuccessMessage("Customer category deleted successfully!");
      setShowSuccessModal(true);
    } catch (error) {
      console.error("Error deleting customer category:", error);
      // Error is handled by the API context interceptor
    }
  };

  const handleView = (category) => {
    console.log("View customer category:", category);
    // Here you would typically navigate to view page or show view modal
  };

  const handleLoadMore = () => {
    setLoadingMore(true);
    // Simulate API call
    setTimeout(() => {
      setLoadingMore(false);
      console.log("Load more customer categories");
    }, 2000);
  };

  // Export/Print handlers
  const handleExport = () => {
    console.log("Exporting customer categories data");
    // API integration logic here
  };

  const handlePrint = () => {
    console.log("Printing customer categories data");
    // Print functionality here
  };

  return (
    <PrivateLayout perm_required={["customer.categories.view"]}>
      {/* Data Table */}
      <div className="">
        <DataTable
          columns={columns}
          data={categories}
          searchPlaceholder="Search customer categories..."
          addButtonText="New Customer Category"
          onView={handleView}
          actions={[ 
                      {name:"view",is_visible:()=> hasPermission("customer.categories.view")}, 
                      {name:"edit",is_visible:()=> hasPermission("customer.categories.edit")}, 
                      {name:"delete",is_visible:()=> hasPermission("customer.categories.delete")}, 
                    ]}
          loading={loading}
          loadingMore={loadingMore}
          loadMoreText="Load More Categories"
          onLoadMore={handleLoadMore}
          showLoadMore={true}
          // Export functionality - only export and print
          showImportExport={hasPermission("customer.categories.create") && true}
          showImport={false}
          showCreateButton = {hasPermission("customer.categories.create") && true}
          allowMultiSelect={hasPermission("customer.categories.delete") && true}
          onExport={handleExport}
          onPrint={handlePrint}
          // Modal forms
          createForm={({ onClose }) => (
            <CustomerCategoryForm
              onClose={onClose}
              onSubmit={handleCreateSubmit}
            />
          )}
          editForm={({ item, onClose }) => (
            <CustomerCategoryForm
              item={item}
              onClose={onClose}
              onSubmit={handleEditSubmit}
            />
          )}
          deleteForm={({ item, onClose }) => (
            <DeleteConfirmation
              item={item}
              onClose={onClose}
              onConfirm={handleDeleteConfirm}
              itemName="Customer Category"
            />
          )}
          createModalTitle="Create New Customer Category"
          editModalTitle="Edit Customer Category"
          deleteModalTitle=""
          modalSize="lg"
          deleteModalSize="sm"
        />
      </div>

      {/* Success Modal */}
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        title="Success"
        message={successMessage}
      />
    </PrivateLayout>
  );
};

export default CustomerCategories;
