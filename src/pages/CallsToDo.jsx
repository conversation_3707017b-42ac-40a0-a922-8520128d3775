import { useState, useEffect, useCallback } from "react";
import { Phone, History } from "lucide-react";
import { useNavigate, useLocation, useSearchParams } from "react-router-dom";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import Modal from "../components/common/Modal";
import CallToDoForm from "../components/forms/CallToDoForm";
import CustomCallHistoryModal from "../components/forms/profile/CustomCallHistoryModal";
import SuccessModal from "../components/modals/SuccessModal";
import { customerServiceService } from "../services/customerServiceService";
import { toast } from "react-toastify";
import noCallsPlaceholder from "../assets/images/no-calls-placeholder.png";

const CallsToDo = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams, setSearchParams] = useSearchParams();

  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [showCallModal, setShowCallModal] = useState(false);
  const [showHistoryModal, setShowHistoryModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [callsToDoData, setCallsToDoData] = useState([]);
  const [isInitialized, setIsInitialized] = useState(false);
  const [statisticsCounts, setStatisticsCounts] = useState({
    pending: 0,
    completed: 0,
    upcoming: 0,
    overdue: 0,
  });

  // Filter state
  const [filters, setFilters] = useState({
    type: "",
  });

  // Get active statistic from URL parameter
  const getActiveStatisticFromParams = () => {
    const type = searchParams.get("type");
    if (["pending", "completed", "upcoming", "overdue"].includes(type)) {
      return type;
    }
    return "pending"; // default
  };

  // Statistics state
  const [activeStatistic, setActiveStatistic] = useState(
    getActiveStatisticFromParams()
  );

  // Fetch statistics counts for all types
  const fetchStatisticsCounts = async () => {
    try {
      const data = await customerServiceService.getCallsToDoCounts();
      setStatisticsCounts({
        pending: data.pending_count || 0,
        completed: data.completed_count || 0,
        upcoming: data.upcoming_count || 0,
        overdue: data.overdue_count || 0,
      });
    } catch (error) {
      setStatisticsCounts({
        pending: 0,
        completed: 0,
        upcoming: 0,
        overdue: 0,
      });
    }
  };

  // Statistics data
  const getStatistics = () => {
    return [
      {
        key: "pending",
        label: "Pending calls",
        value: statisticsCounts.pending || 0,
      },
      {
        key: "completed",
        label: "Completed calls",
        value: statisticsCounts.completed || 0,
      },
      {
        key: "upcoming",
        label: "Upcoming calls",
        value: statisticsCounts.upcoming || 0,
      },
      {
        key: "overdue",
        label: "Overdue calls",
        value: statisticsCounts.overdue || 0,
      },
    ];
  };

  // Fetch data for a specific tab type
  const fetchTabData = async (statisticKey) => {
    setLoading(true);

    try {
      // Fetch data from API with type parameter
      console.log(`Making API call with type parameter: ${statisticKey}`);
      const data = await customerServiceService.getCallsToDo({
        type: statisticKey,
      });
      console.log(
        `Received ${data.length} items for type: ${statisticKey}`,
        data
      );

      // Transform API data to match table format
      const transformedData = data.map((item, index) => ({
        id: item.id,
        uniqueKey: `${item.id}-${index}`,
        customer_name: item.customerName,
        account_number: item.accountNumber,
        phone_number: item.phoneNumber,
        hitlist_type: item.hitlistType,
        priority: item.priority || "Medium", // Default priority
        phase_step: item.phase ? formatPhase(item.phase) : "-",
        scheduled: item.scheduled,
        status: item.status?.value || "Pending",
      }));

      console.log(`Transformed ${transformedData.length} items for display`);
      setCallsToDoData(transformedData);
    } catch (error) {
      console.error("Error fetching calls to do:", error);
      toast.error("Failed to fetch calls data");
    } finally {
      setLoading(false);
    }
  };

  // Handle statistic tab change
  const handleStatisticChange = async (statisticKey) => {
    console.log(
      `handleStatisticChange called with: ${statisticKey}, current activeStatistic: ${activeStatistic}`
    );

    // Prevent unnecessary updates if already on the same tab
    if (statisticKey === activeStatistic) {
      console.log(`Already on ${statisticKey} tab, skipping update`);
      return;
    }

    setActiveStatistic(statisticKey);

    // Update URL parameter
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("type", statisticKey);
    setSearchParams(newSearchParams);
    console.log(
      `URL updated to: /customer-service/calls-to-do?type=${statisticKey}`
    );

    // Fetch data for this tab
    await fetchTabData(statisticKey);
  };

  // Filter configuration
  const filterConfig = [
    {
      key: "type",
      label: "Type",
      field: "hitlist_type",
      placeholder: "All Types",
      selectedValue: filters.type,
      options: [
        { value: "2by2by2", label: "2by2by2" },
        { value: "Dormancy", label: "Dormancy" },
      ],
    },
  ];

  // Handle filter changes
  const handleFilterChange = (filterKey, value) => {
    setFilters((prev) => ({
      ...prev,
      [filterKey]: value,
    }));
  };

  // Handle clearing filters
  const handleClearFilters = () => {
    setFilters({
      type: "",
      status: "Pending", // Reset to default pending
    });
  };

  // Helper function to format phase names
  const formatPhase = (phase) => {
    switch (phase) {
      case "first2":
        return "First 2";
      case "second2":
        return "Second 2";
      case "third2":
        return "Third 2";
      default:
        return phase;
    }
  };

  // Format date function
  const formatDate = (dateString) => {
    if (!dateString) return "-";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
  };

  // Status badge component
  const StatusBadge = ({ status }) => {
    const getStatusColor = (status) => {
      switch (status?.toLowerCase()) {
        case "scheduled":
          return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
        case "pending":
          return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
        case "overdue":
          return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
        case "completed":
          return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
        default:
          return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
      }
    };

    return (
      <span
        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(
          status
        )}`}
      >
        {status || "-"}
      </span>
    );
  };

  // Priority badge component
  const PriorityBadge = ({ priority }) => {
    const getPriorityColor = (priority) => {
      switch (priority?.toLowerCase()) {
        case "urgent":
        case "overdue":
          return "#DC2626"; // Red
        case "high":
          return "#F97316"; // Orange
        case "medium":
          return "#EAB308"; // Yellow
        case "low":
          return "#3B82F6"; // Blue
        default:
          return "#7e7e7e"; // Gray
      }
    };

    return (
      <span
        className="inline-flex items-center px-2 py-1 text-xs font-semibold text-white rounded-md"
        style={{
          backgroundColor: getPriorityColor(priority),
        }}
      >
        {priority || "Medium"}
      </span>
    );
  };

  // Customer hover tooltip component
  const CustomerCell = ({
    customer_name,
    account_number,
    phone_number,
    onClick,
  }) => {
    return (
      <div className="group relative">
        <div className="cursor-pointer" onClick={onClick}>
          <div className="font-medium text-gray-900 dark:text-white">
            {customer_name}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400">
            {account_number}
          </div>
        </div>

        {/* Hover tooltip */}
        <div className="absolute left-0 top-full mt-2 w-64 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg p-3 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
          <div className="space-y-2">
            <div>
              <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                Customer:
              </span>
              <div className="text-sm font-medium text-gray-900 dark:text-white">
                {customer_name}
              </div>
            </div>
            <div>
              <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                Account Number:
              </span>
              <div className="text-sm text-gray-700 dark:text-gray-300">
                {account_number}
              </div>
            </div>
            <div>
              <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                Phone Number:
              </span>
              <div className="text-sm text-gray-700 dark:text-gray-300">
                {phone_number}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Handle history modal
  const handleShowHistory = (item) => {
    setSelectedItem(item);
    setShowHistoryModal(true);
  };

  // Define columns for the table
  const columns = [
    {
      key: "customer",
      title: "CUSTOMER",
      render: (value, row) => (
        <CustomerCell
          customer_name={row.customer_name}
          account_number={row.account_number}
          phone_number={row.phone_number}
          onClick={() => handleShowHistory(row)}
        />
      ),
    },
    {
      key: "phone_number",
      title: "PHONE NUMBER",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value || "-"}
        </span>
      ),
    },
    {
      key: "hitlist_type",
      title: "CALL TYPE",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value || "-"}
        </span>
      ),
    },
    {
      key: "phase_step",
      title: "PHASE / STEP",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value || "-"}
        </span>
      ),
    },
    {
      key: "scheduled",
      title: "DUE DATE",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {formatDate(value)}
        </span>
      ),
    },
    {
      key: "priority",
      title: "PRIORITY",
      render: (value) => {
        // Hide priority for completed tab, show dash instead
        if (activeStatistic === "completed") {
          return (
            <span className="text-sm text-gray-600 dark:text-gray-400">-</span>
          );
        }
        return <PriorityBadge priority={value} />;
      },
    },
    {
      key: "status",
      title: "STATUS",
      render: (value) => <StatusBadge status={value} />,
    },
    {
      key: "action",
      title: "ACTION",
      render: (value, row) => (
        <div className="flex items-center gap-2">
          <button
            onClick={() => handleCallNow(row)}
            className="inline-flex items-center px-2 py-1 bg-green-600 hover:bg-green-700 text-white text-xs font-medium rounded-lg transition-colors duration-200"
          >
            <Phone size={12} className="mr-1" />
            Call Now
          </button>
          <button
            onClick={() => handleShowHistory(row)}
            className="inline-flex items-center p-1 text-gray-500 hover:text-gray-700 transition-colors duration-200"
            title="View Call History"
          >
            <History size={16} />
          </button>
        </div>
      ),
    },
  ];

  // Event handlers
  const handleView = (item) => {
    setSelectedItem(item);
    console.log("View item:", item);
  };

  const handleLoadMore = () => {
    setLoadingMore(true);
    setTimeout(() => {
      setLoadingMore(false);
      console.log("Load more calls to do");
    }, 2000);
  };

  const handleCallNow = (item) => {
    setSelectedItem(item);
    setShowCallModal(true);
    console.log("Initiating call for:", item.customer_name);
  };

  // Refresh all data (statistics and current tab)
  const refreshAllData = async () => {
    console.log("Refreshing all data...");
    const currentType = getActiveStatisticFromParams();
    await Promise.all([fetchStatisticsCounts(), fetchTabData(currentType)]);
  };

  // Initialize page - fetch counts and data for the active tab only
  useEffect(() => {
    const initializePage = async () => {
      // Set default URL parameter if none exists
      if (!searchParams.get("type")) {
        const newSearchParams = new URLSearchParams(searchParams);
        newSearchParams.set("type", "pending");
        setSearchParams(newSearchParams, { replace: true });
        return;
      }

      // Get the active tab from URL
      const currentType = getActiveStatisticFromParams();
      setActiveStatistic(currentType);

      // Fetch counts and data for the active tab only
      console.log(
        `Initializing page - fetching counts and ${currentType} data...`
      );
      await Promise.all([fetchStatisticsCounts(), fetchTabData(currentType)]);

      setIsInitialized(true);
    };

    initializePage();
  }, []);

  // Handle URL parameter changes - fetch data only when tab changes after initialization
  useEffect(() => {
    if (!isInitialized) return; // Don't fetch data during initial load

    const currentType = getActiveStatisticFromParams();
    console.log(
      `URL useEffect triggered - currentType: ${currentType}, activeStatistic: ${activeStatistic}`
    );

    if (currentType !== activeStatistic && searchParams.get("type")) {
      console.log(
        `URL parameter changed from ${activeStatistic} to ${currentType} - fetching data`
      );
      // Only update state and fetch data, don't call handleStatisticChange to avoid URL update loop
      setActiveStatistic(currentType);
      fetchTabData(currentType);
    }
  }, [searchParams]);

  const handleCallSubmit = async (callData, customer, error) => {
    console.log("Call submitted:", { callData, customer, error });

    if (!error && callData && customer) {
      // console.log("Call created successfully");
      // Show success message
      // toast.success("Call recorded successfully");

      // Remove the called record from the current data without page refresh
      if (selectedItem && selectedItem.id) {
        setCallsToDoData((prevData) =>
          prevData.filter((item) => item.id !== selectedItem.id)
        );

        // Update statistics counts by decrementing the current tab count
        setStatisticsCounts((prevCounts) => ({
          ...prevCounts,
          [activeStatistic]: Math.max(
            0,
            (prevCounts[activeStatistic] || 0) - 1
          ),
        }));
      }

      // Show success modal
      setShowSuccessModal(true);
    } else if (error) {
      // Show error message
      toast.error(error.response?.data?.message || "Failed to record call");
    }

    setShowCallModal(false);
    setSelectedItem(null);
  };

  // Import/Export handlers
  const handleExport = () => {
    console.log("Exporting calls to do data");
    // API integration logic here
  };

  const handlePrint = () => {
    console.log("Printing calls to do data");
    // Print functionality here
  };

  return (
    <PrivateLayout
      pageTitle="Calls To Do"
      perm_required={["customer.service.personal.calls.view"]}
    >
      <div className="space-y-6">
        {/* Data Table */}
        <DataTable
          columns={columns}
          data={callsToDoData.filter((item) => {
            // Type filter only
            if (filters.type && item.hitlist_type !== filters.type) {
              return false;
            }
            return true;
          })}
          searchPlaceholder="Search calls to do..."
          onView={handleView}
          actions={[]} // Hide default actions column
          loading={loading}
          loadingMore={loadingMore}
          loadMoreText="Load More Calls"
          onLoadMore={handleLoadMore}
          showLoadMore={true}
          dataCountLabel="calls to do"
          showDataCount={true}
          highlightField="status"
          highlightColors={{
            Scheduled:
              "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",
            Pending:
              "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",
            Overdue:
              "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400",
            Completed:
              "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
          }}
          // Export functionality - only export and print
          showImportExport={true}
          showImport={false}
          onExport={handleExport}
          onPrint={handlePrint}
          // Filter functionality
          showFilters={true}
          filterConfig={filterConfig}
          onFilterChange={handleFilterChange}
          onClearFilters={handleClearFilters}
          // Statistics functionality
          showStatistics={true}
          statistics={getStatistics()}
          activeStatistic={activeStatistic}
          onStatisticChange={handleStatisticChange}
          // Custom empty state
          emptyStateComponent={
            <div className="flex flex-col items-center justify-center py-12 space-y-4">
              <img
                src={noCallsPlaceholder}
                alt="No calls"
                className="w-32 h-32 opacity-50"
              />
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                You have no {activeStatistic} calls for today!
              </h3>
              <p className="text-gray-600 dark:text-gray-400 text-center max-w-md">
                {activeStatistic === "pending"
                  ? "All caught up! No pending calls at the moment."
                  : activeStatistic === "completed"
                  ? "No completed calls yet. Start making calls to see them here."
                  : activeStatistic === "upcoming"
                  ? "No upcoming calls scheduled. Check back later."
                  : "No overdue calls. Great job staying on top of your schedule!"}
              </p>
            </div>
          }
        />
        {/* Call Modal */}
        {showCallModal && selectedItem && (
          <Modal
            isOpen={showCallModal}
            onClose={() => {
              setShowCallModal(false);
              setSelectedItem(null);
            }}
            title={`Make Call - ${selectedItem?.customer_name}`}
            size="lg"
          >
            <CallToDoForm
              item={selectedItem}
              onClose={() => {
                setShowCallModal(false);
                setSelectedItem(null);
              }}
              onSubmit={handleCallSubmit}
            />
          </Modal>
        )}
        {/* Call History Modal */}
        {showHistoryModal && selectedItem && (
          <Modal
            isOpen={showHistoryModal}
            onClose={() => {
              setShowHistoryModal(false);
              setSelectedItem(null);
            }}
            title={`Call History - ${selectedItem?.customer_name}`}
            size="xl"
          >
            <CustomCallHistoryModal
              item={selectedItem}
              onClose={() => {
                setShowHistoryModal(false);
                setSelectedItem(null);
              }}
            />
          </Modal>
        )}
        {/* Success Modal */}
        <SuccessModal
          isOpen={showSuccessModal}
          onClose={() => setShowSuccessModal(false)}
          title="Call Recorded Successfully"
          message="The call has been recorded successfully and removed from your pending list."
        />
      </div>
    </PrivateLayout>
  );
};

export default CallsToDo;
