import { useState, useEffect } from "react";
import { Shield, Mail, Phone, X } from "lucide-react";
import { InputOtp } from "primereact/inputotp";
import { But<PERSON> } from "primereact/button";
import IntlTelInput from "intl-tel-input/react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import MFAVerificationModal from "../components/modals/MFAVerificationModal";
import SuccessModal from "../components/modals/SuccessModal";
import MFAMethodSkeleton from "../components/skeletons/MFAMethodSkeleton";
import { authService } from "../services/authService";
import { useAuth } from "../contexts/AuthContext";
import {
  getUserEmail,
  setUserEmail as setUserEmailCookie,
} from "../utils/cookieUtils";
import "intl-tel-input/build/css/intlTelInput.css";
const MultiFactorAuth = () => {
  const { user } = useAuth();
  const [showVerificationModal, setShowVerificationModal] = useState(false);
  const [showSetup, setShowSetup] = useState(false);
  const [showPhoneModal, setShowPhoneModal] = useState(false);
  const [showOTPModal, setShowOTPModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");

  const [phoneNumber, setPhoneNumber] = useState("");
  const [pendingPhoneNumber, setPendingPhoneNumber] = useState(""); // Store phone number until verification
  const [pendingMethodId, setPendingMethodId] = useState(""); // Store method ID for phone verification
  const [otpToken, setOtpToken] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState(0);
  const [mfaMethods, setMfaMethods] = useState([]);
  const [userEmail, setUserEmail] = useState("");
  const [isLoadingMethods, setIsLoadingMethods] = useState(false);
  const [loadingMethodId, setLoadingMethodId] = useState(null); // Track which method is being toggled
  const [otpError, setOtpError] = useState(""); // Error state for OTP verification

  // Function to load MFA methods (can be called multiple times)
  const loadMFAMethods = async () => {
    try {
      setIsLoadingMethods(true);
      const response = await authService.getMFAMethods();

      // Store email in both state and cookies
      setUserEmail(response.email);
      setUserEmailCookie(response.email); // Store in cookies using the utility function

      setMfaMethods(response.mfaMethods);
      // Don't automatically show setup - let user click "Enable Multi-Factor Authentication" first
    } catch (error) {
      console.error("Failed to load MFA methods:", error);
      // Handle error - maybe show error message
    } finally {
      setIsLoadingMethods(false);
    }
  };

  // Load MFA methods on component mount
  useEffect(() => {
    // Check if email exists in cookies first
    const emailFromCookie = getUserEmail();
    if (emailFromCookie) {
      setUserEmail(emailFromCookie);
    }
  }, []);

  // Timer functionality for OTP
  useEffect(() => {
    if (timeRemaining > 0) {
      const timer = setTimeout(() => {
        setTimeRemaining(timeRemaining - 1000);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [timeRemaining]);

  // Format time display
  const formatTime = (milliseconds) => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  };

  // Custom OTP input template (borrowed from reset-verification)
  const customInput = ({ events, props }) => {
    const { invalid, unstyled, ...domProps } = props;

    const handleInputChange = (e) => {
      const value = e.target.value.toUpperCase();
      e.target.value = value;
      if (events.onInput) {
        events.onInput(e);
      }
    };

    return (
      <>
        <input
          {...events}
          {...domProps}
          type="text"
          className="custom-otp-input"
          onInput={handleInputChange}
          style={{ textTransform: "uppercase" }}
        />
        {props.id === 2 && (
          <div className="px-3">
            <i className="pi pi-minus" />
          </div>
        )}
      </>
    );
  };

  const handleEnableMFA = () => {
    setShowVerificationModal(true);
  };

  const handlePasswordVerification = async (password) => {
    try {
      // Get email from cookies (stored during login or MFA page visit)
      const emailFromCookie = getUserEmail();
      const emailToUse = emailFromCookie || userEmail;

      if (!emailToUse) {
        throw new Error("Email not found. Please try logging in again.");
      }

      // Use the same login endpoint to verify password
      await authService.login({
        email: emailToUse,
        password: password,
      });

      setShowVerificationModal(false);

      // After successful password verification, fetch fresh MFA data
      await loadMFAMethods();

      setShowSetup(true);
    } catch (error) {
      // Re-throw the error to be handled by the modal
      throw new Error("Invalid password. Please try again.");
    }
  };

  const handleEnableMethod = async (method) => {
    if (method.method === "SMS" && !method.contact) {
      setShowPhoneModal(true);
      setPhoneNumber(""); // Reset phone input when opening modal
      setPendingMethodId(method.id); // Store method ID for later use
    } else {
      try {
        setLoadingMethodId(method.id); // Set loading state for this specific method

        // Call backend to toggle MFA method
        await authService.toggleMFAMethod(method.id, !method.enabled);

        // Update local state
        setMfaMethods((prev) =>
          prev.map((m) =>
            m.id === method.id ? { ...m, enabled: !m.enabled } : m
          )
        );

        console.log(
          `${method.method} method ${method.enabled ? "disabled" : "enabled"}`
        );
      } catch (error) {
        console.error("Failed to toggle MFA method:", error);
        // You could show an error message here
      } finally {
        setLoadingMethodId(null); // Clear loading state
      }
    }
  };

  const handleClosePhoneModal = () => {
    setShowPhoneModal(false);
    setPhoneNumber("");
  };

  const handleAddPhone = async () => {
    // Simple validation - just check if phone number is not empty
    if (!phoneNumber || phoneNumber.trim().length === 0) {
      return;
    }

    try {
      setIsLoading(true);

      // Call backend to add phone number
      await authService.addPhoneNumber(phoneNumber, pendingMethodId);

      console.log("Adding phone number:", phoneNumber);

      // Store phone number temporarily until verification
      setPendingPhoneNumber(phoneNumber);

      setShowPhoneModal(false);
      setShowOTPModal(true);
      setTimeRemaining(5 * 60 * 1000); // 5 minutes timer
    } catch (error) {
      console.error("Failed to add phone number:", error);
      // You could show an error message here
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyOTP = async () => {
    if (!otpToken || otpToken.length !== 6) return;

    try {
      setIsLoading(true);
      setOtpError(""); // Clear any previous errors
      console.log("Verifying OTP:", otpToken);

      // Call backend to verify phone number
      await authService.verifyPhoneNumber(otpToken);

      // Update SMS method with the phone number and mark as verified
      setMfaMethods((prev) =>
        prev.map((method) =>
          method.method === "SMS"
            ? {
                ...method,
                contact: pendingPhoneNumber,
                verified: true,
                enabled: true,
                active: true,
              }
            : method
        )
      );

      setSuccessMessage(
        `Phone Verified!\nSMS authentication has been successfully enabled for\n${pendingPhoneNumber}`
      );
      setShowOTPModal(false);
      setShowSuccessModal(true);
      setOtpToken("");
      setPendingPhoneNumber(""); // Clear pending phone number
      setPhoneNumber(""); // Reset phone input
      setPendingMethodId(""); // Clear pending method ID
    } catch (error) {
      console.error("Failed to verify OTP:", error);

      // Set error message for user
      let errorMessage = "Failed to verify code. Please try again.";
      if (error.response) {
        const status = error.response.status;
        const responseMessage = error.response.data?.message || "";

        if (status === 400) {
          errorMessage = responseMessage || "Invalid verification code.";
        } else if (status === 410) {
          errorMessage =
            "Verification code has expired. Please request a new one.";
        } else {
          errorMessage =
            responseMessage || "Failed to verify code. Please try again.";
        }
      }

      setOtpError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendOTP = async () => {
    try {
      setIsResending(true);

      // Call backend to resend phone verification code
      const response = await authService.resendPhoneCode();

      console.log("Resending OTP to:", pendingPhoneNumber);

      // Show success message if provided by backend
      if (response.message) {
        setSuccessMessage(response.message);
        setTimeout(() => {
          setSuccessMessage("");
        }, 3000);
      }

      setOtpToken("");
      setTimeRemaining(5 * 60 * 1000); // Reset timer to 5 minutes
    } catch (error) {
      console.error("Failed to resend OTP:", error);
      // You could show an error message here
    } finally {
      setIsResending(false);
    }
  };

  return (
    <PrivateLayout pageTitle="Multi-Factor Authentication">
      <div className="max-w-6xl mx-auto min-h-[70vh]">
        {isLoadingMethods ? (
          <div className="bg-white dark:bg-gray-800 shadow-lg rounded-lg border border-gray-200 dark:border-gray-700">
            {/* Header */}
            <div className="p-6 dark:border-gray-700 w-[50%]">
              <div className="flex items-center gap-3 mb-2">
                <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                  <Shield className="h-6 w-6 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-48"></div>
                </div>
              </div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-96 mt-2"></div>
            </div>

            {/* MFA Methods Skeleton */}
            <div className="p-8">
              <MFAMethodSkeleton count={2} />
            </div>
          </div>
        ) : !showSetup ? (
          <div className="bg-white dark:bg-gray-800 shadow-lg rounded-lg border border-gray-200 dark:border-gray-700">
            {/* Header */}
            <div className="p-6 dark:border-gray-700 w-[50%]">
              <div className="flex items-center gap-3 mb-2">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Secure Your Account
                </h2>
              </div>

              {/* Description */}
              <div className="mb-8 ">
                <p className="text-gray-600 dark:text-gray-400 ">
                  Add an extra layer of security to your account. When enabled,
                  you'll need to provide a verification code in addition to your
                  password when signing in.
                </p>
              </div>

              {/* Enable Button */}
              <button
                onClick={handleEnableMFA}
                className="bg-[#1c5b41] hover:bg-green-700 text-white font-medium py-3 px-8 rounded-xl transition-colors duration-200 focus:outline-none"
              >
                Enable Multi-Factor Authentication
              </button>
            </div>

            {/* Content */}
            <div className="p-6">
              <div className="text-center py-8">
                {/* Note */}
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-4">
                  You can disable MFA at any time from this page
                </p>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-white dark:bg-gray-800 shadow-lg rounded-lg border border-gray-200 dark:border-gray-700">
            {/* Header */}
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center gap-3 mb-2">
                <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                  <Shield className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Multi-Factor Authentication
                </h2>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Choose your preferred authentication methods below.
              </p>
            </div>

            {/* MFA Methods */}
            <div className="p-8">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-8">
                {mfaMethods.map((method) => (
                  <div
                    key={method.id}
                    className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-8 border border-gray-200 dark:border-gray-600"
                  >
                    {/* Method Header */}
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <div
                          className={`p-2 rounded-lg ${
                            method.method === "EMAIL"
                              ? "bg-blue-100 dark:bg-blue-900/30"
                              : "bg-green-100 dark:bg-green-900/30"
                          }`}
                        >
                          {method.method === "EMAIL" ? (
                            <Mail
                              className={`h-5 w-5 ${
                                method.method === "EMAIL"
                                  ? "text-blue-600 dark:text-blue-400"
                                  : "text-green-600 dark:text-green-400"
                              }`}
                            />
                          ) : (
                            <Phone className="h-5 w-5 text-green-600 dark:text-green-400" />
                          )}
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-900 dark:text-white">
                            {method.method === "EMAIL"
                              ? "Email Authentication"
                              : "SMS Authentication"}
                          </h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {method.description ||
                              (method.method === "EMAIL"
                                ? "Secure verification via email"
                                : "Secure verification via text message")}
                          </p>
                        </div>
                      </div>
                      <span
                        className={`px-2 py-1 text-xs font-medium rounded-full ${
                          method.enabled
                            ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                            : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-400"
                        }`}
                      >
                        {method.enabled ? "Enabled" : "Disabled"}
                      </span>
                    </div>

                    {/* Contact Info */}
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {method.method === "EMAIL"
                          ? "Email Address"
                          : "Phone Number"}
                      </label>
                      <input
                        type="text"
                        value={
                          method.contact ||
                          (method.method === "SMS" ? "Not configured" : "")
                        }
                        readOnly
                        className="w-full read-only:text-gray-400 px-3 py-2 border outline-0 border-gray-300 dark:border-gray-600 rounded-lg
                         bg-gray-100 dark:bg-gray-600 text-gray-900 dark:text-white"
                      />
                    </div>

                    {/* Action Button */}
                    <button
                      onClick={() => handleEnableMethod(method)}
                      disabled={loadingMethodId === method.id}
                      className={`w-full py-3 px-4 rounded-lg font-medium text-[15px] mt-[1rem] transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed ${
                        method.enabled
                          ? "bg-gray-200 hover:bg-gray-300 text-gray-700 dark:bg-gray-600 dark:hover:bg-gray-500 dark:text-gray-300"
                          : "bg-[#1c5b41] hover:bg-green-700 text-white"
                      }`}
                    >
                      <div className="flex items-center justify-center gap-2">
                        {loadingMethodId === method.id ? (
                          <>
                            <div className="w-4 h-4 border-2 border-current/30 border-t-current rounded-full animate-spin"></div>
                            {method.enabled ? "Disabling..." : "Enabling..."}
                          </>
                        ) : method.enabled ? (
                          "Disable"
                        ) : (
                          "Enable"
                        )}
                      </div>
                    </button>
                  </div>
                ))}
              </div>

              {/* Security Recommendation */}
              <div className="mt-8 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-6">
                <div className="flex items-start gap-3">
                  <div className="p-1 bg-orange-100 dark:bg-orange-900/30 rounded-full">
                    <Shield className="h-4 w-4 text-orange-600 dark:text-orange-400" />
                  </div>
                  <div>
                    <h4 className="font-medium text-orange-800 dark:text-orange-300 mb-1">
                      Security Recommendation
                    </h4>
                    <p className="text-sm text-orange-700 dark:text-orange-400">
                      We recommend enabling at least one additional
                      authentication method. Having both email and SMS enabled
                      provides the best security for your account.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* MFA Verification Modal */}
      <MFAVerificationModal
        isOpen={showVerificationModal}
        onClose={() => setShowVerificationModal(false)}
        onVerify={handlePasswordVerification}
      />

      {/* Phone Number Modal */}
      {showPhoneModal && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full p-6 relative">
            {/* Close Button */}
            <button
              onClick={handleClosePhoneModal}
              className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            >
              <X className="h-5 w-5" />
            </button>

            {/* Header */}
            <div className="text-center mb-6">
              <div className="mx-auto w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mb-4">
                <Phone className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                Add Phone Number
              </h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                Enter your phone number to enable SMS authentication
              </p>
            </div>

            {/* Form */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Phone Number
                </label>
                <IntlTelInput
                  initialValue={phoneNumber}
                  initOptions={{
                    initialCountry: "ke",
                    autoPlaceholder: "aggressive",
                    customPlaceholder: () => "e.g 757346777",
                    placeholderNumberType: "FIXED_LINE",
                    separateDialCode: true,
                    utilsScript:
                      "https://cdn.jsdelivr.net/npm/intl-tel-input@18.2.1/build/js/utils.js",
                  }}
                  inputProps={{
                    className:
                      "w-full pl-4 pr-4 py-3 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none transition-colors duration-200 border-gray-300 dark:border-gray-600 focus:border-green-500",
                    value: phoneNumber,
                    onChange: (e) => {
                      const value = e.target.value;
                      // Only allow numbers and limit to 9 digits
                      const numericValue = value.replace(/\D/g, "");
                      if (numericValue.length <= 9) {
                        console.log("Input onChange:", numericValue);
                        setPhoneNumber(numericValue);
                      }
                    },
                    onInput: (e) => {
                      const value = e.target.value;
                      // Only allow numbers and limit to 9 digits
                      const numericValue = value.replace(/\D/g, "");
                      if (numericValue.length <= 9) {
                        console.log("Input onInput:", numericValue);
                        setPhoneNumber(numericValue);
                        e.target.value = numericValue;
                      } else {
                        // Prevent input if it exceeds 9 digits
                        e.preventDefault();
                        e.target.value = phoneNumber;
                      }
                    },
                  }}
                />
              </div>

              {/* Buttons */}
              <div className="flex gap-3 pt-4">
                <button
                  type="button"
                  onClick={handleClosePhoneModal}
                  className="flex-1 px-4 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 font-medium"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleAddPhone}
                  disabled={
                    isLoading || !phoneNumber || phoneNumber.trim().length < 9
                  }
                  className="flex-1 bg-[#1c5b41] hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 focus:outline-none"
                >
                  <div className="flex items-center justify-center gap-2">
                    {isLoading ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                        Sending...
                      </>
                    ) : (
                      <>
                        Send Code
                        <span>→</span>
                      </>
                    )}
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* OTP Verification Modal */}
      {showOTPModal && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full p-6 relative">
            {/* Close Button */}
            <button
              onClick={() => setShowOTPModal(false)}
              className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            >
              <X className="h-5 w-5" />
            </button>

            {/* Header */}
            <div className="text-center mb-6">
              <div className="mx-auto w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mb-4">
                <Phone className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                Verify Your Phone
              </h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                Enter the 6-digit code sent to
              </p>
              <p className="text-gray-900 dark:text-white font-medium">
                {pendingPhoneNumber}
              </p>
            </div>

            {/* Error Message */}
            {otpError && (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-lg text-sm mb-4">
                {otpError}
              </div>
            )}

            {/* OTP Input */}
            <div className="space-y-4">
              <InputOtp
                value={otpToken}
                onChange={(e) => {
                  setOtpToken(e.value ? e.value.toUpperCase() : "");
                  // Clear error when user starts typing
                  if (otpError) setOtpError("");
                }}
                length={6}
                inputTemplate={customInput}
                style={{ gap: 0 }}
                key="otp-input"
                className="mb-6 flex flex-col w-full"
              />

              <div className="flex justify-between mt-5 w-full">
                <div className="flex items-center">
                  {/* Timer Display */}
                  {timeRemaining > 0 && (
                    <p className="text-[15px]">
                      Resend code in:{" "}
                      <span className="font-medium">
                        {formatTime(timeRemaining)}
                      </span>
                    </p>
                  )}
                  {timeRemaining <= 0 && (
                    <Button
                      label={isResending ? "Sending..." : "Resend Code"}
                      link
                      className="p-0 text-green-600 font-semibold hover:text-green-700 dark:text-green-400 dark:hover:text-green-300"
                      onClick={handleResendOTP}
                      disabled={isResending}
                    />
                  )}
                </div>
                <Button
                  label={isLoading ? "Verifying..." : "Verify"}
                  className="bg-[#1c5b41] hover:bg-green-700 disabled:opacity-[0.7] text-white border-green-600 hover:border-green-700 py-3 px-4 rounded-[0.5rem]"
                  onClick={handleVerifyOTP}
                  disabled={isLoading || !otpToken || otpToken.length !== 6}
                />
              </div>

              <div className="text-center mt-4">
                <button
                  type="button"
                  onClick={() => setShowOTPModal(false)}
                  className="text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 font-medium transition-colors duration-200 text-sm"
                >
                  Back
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Success Modal */}
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        title="Phone Verified!"
        message={successMessage}
      />
    </PrivateLayout>
  );
};

export default MultiFactorAuth;
