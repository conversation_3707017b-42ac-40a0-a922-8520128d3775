import PrivateLayout from "../components/layouts/PrivateLayout";
import BankerDashboard from "../components/dashboards/BankerDashboard";
import BranchManagerDashboard from "../components/dashboards/BranchManagerDashboard";
import CustomerServiceDashboard from "../components/dashboards/CustomerServiceDashboard";
import ServiceDeliverySupervisorDashboard from "../components/dashboards/ServiceDeliverySupervisorDashboard";
import RegionalManagerDashboard from "../components/dashboards/RegionalManagerDashboard";
import SegmentHeadDashboard from "../components/dashboards/SegmentHeadDashboard";
import FinanceAnalystDashboard from "../components/dashboards/FinanceAnalystDashboard";
import CreditOfficerDashboard from "../components/dashboards/CreditOfficerDashboard";
import PermissionGate from "../components/common/PermissionGate";

function Dashboard() {
  // Dashboard type switcher - manually change this value to switch between dashboards
  // Options: "banker", "branch-manager", "customer-service", "service-delivery-supervisor", "customer-experience-officer", "regional-manager"
 // const dashboardType = "customer-experience-officer";

  // const renderDashboardContent = () => {

  // switch (dashboardType) {
  //   case "banker":
  //     return <BankerDashboard />;
  //   case "branch-manager":
  //     return <BranchManagerDashboard />;
  //   case "customer-service":
  //     return <CustomerServiceDashboard />;
  //   case "service-delivery-supervisor":
  //     return (
  //       <ServiceDeliverySupervisorDashboard userRole="service-delivery-supervisor" />
  //     );
  //   case "customer-experience-officer":
  //     return (
  //       <ServiceDeliverySupervisorDashboard userRole="customer-experience-officer" />
  //     );
  //   case "regional-manager":
  //     return <RegionalManagerDashboard />;
  //   case "segment-head":
  //     return <SegmentHeadDashboard />;
  //   case "finance-analyst":
  //     return <FinanceAnalystDashboard />;
  //   case "credit-officer":
  //     return <CreditOfficerDashboard />;
  // default:
  //   return (
  //     <div className="p-6">
  //       <h1 className="text-2xl font-bold mb-4">Dashboard</h1>
  //       <p>Please select a valid dashboard type.</p>
  //     </div>
  //   );
  // }
  // };

  return (
    <PrivateLayout>
      {/* Banker Dashboard */}
      <PermissionGate perms_required={["dashboard.banker"]}>
        <BankerDashboard />
      </PermissionGate>

      {/* Branch Manager Dashboard */}
      <PermissionGate perms_required={["dashboard.branch.manager"]}>
        <BranchManagerDashboard />
      </PermissionGate>

      {/* Customer Service Dashboard */}
      <PermissionGate perms_required={["dashboard.customer-service"]}>
        <CustomerServiceDashboard />
      </PermissionGate>

      {/* Service Delivery Supervisor Dashboard */}
      <PermissionGate
        perms_required={["dashboard.service.delivery.supervisor"]}
      >
        <ServiceDeliverySupervisorDashboard userRole="service-delivery-supervisor" />
      </PermissionGate>

      {/* Customer Experience Officer Dashboard */}
      <PermissionGate
        perms_required={["dashboard.cxo"]}
      >
        <ServiceDeliverySupervisorDashboard userRole="customer-experience-officer" />
      </PermissionGate>

      {/* Regional Manager Dashboard */}
      <PermissionGate perms_required={["dashboard.regional.manager"]}>
        <RegionalManagerDashboard />
      </PermissionGate>

      {/* Segment Head Dashboard */}
      <PermissionGate perms_required={["dashboard.segment.head"]}>
        <SegmentHeadDashboard />
      </PermissionGate>

      {/* Finance Analyst Dashboard */}
      <PermissionGate perms_required={["dashboard.finance.analyst"]}>
        <FinanceAnalystDashboard />
      </PermissionGate>

      {/* Credit Officer Dashboard */}
      <PermissionGate perms_required={["dashboard.credit-officer"]}>
        <CreditOfficerDashboard />
      </PermissionGate>
    </PrivateLayout>
  );
}

export default Dashboard;
