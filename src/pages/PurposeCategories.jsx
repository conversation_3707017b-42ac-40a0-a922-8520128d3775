import { useState, useEffect } from "react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import DeleteConfirmation from "../components/forms/DeleteConfirmation";
import PurposeCategoryForm from "../components/forms/PurposeCategoryForm";
import SuccessModal from "../components/modals/SuccessModal";
import { hasPermission } from "../utils/permissionUtils";

import {
  purposeCategoriesService,
  formatPurposeCategoriesForTable,
  getCategoryUsageColor,
} from "../services/purposeCategoriesService";

const PurposeCategories = () => {
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [categories, setCategories] = useState([]);
  const [error, setError] = useState(null);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");

  // Define columns of the table here and what to render
  const columns = [
    {
      key: "name",
      title: "CATEGORY NAME",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "addedBy",
      title: "ADDED BY",
      render: (value) => (
        <span className=" text-gray-600 dark:text-gray-400">{value}</span>
      ),
    },
    {
      key: "addedOn",
      title: "ADDED ON",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
    {
      key: "purposesCount",
      title: "PURPOSES",
      render: (value, row) => (
        <div className="flex flex-col">
          <span
            className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getCategoryUsageColor(
              row.isInUse,
              value
            )}`}
          >
            {value || 0} purposes
          </span>
          {row.isInUse && (
            <span className="text-xs text-green-600 dark:text-green-400 mt-1">
              In use
            </span>
          )}
        </div>
      ),
    },
  ];

  // Fetch categories data from API
  const fetchCategories = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log("Fetching purpose categories data...");

      const response = await purposeCategoriesService.getAll();
      const formattedCategories = formatPurposeCategoriesForTable(response);

      setCategories(formattedCategories);
      console.log(
        "Purpose categories data loaded successfully:",
        formattedCategories.length,
        "categories"
      );
    } catch (error) {
      console.error("Error fetching purpose categories:", error);
      setError(error.message);
      console.error(error.message || "Failed to load purpose categories data");
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchCategories();
  }, []);

  // Form submission handlers
  const handleCreateSubmit = async (formData) => {
    try {
      console.log("Creating purpose category:", formData);

      const newCategory = await purposeCategoriesService.create(formData);
      console.log("Raw API response for new category:", newCategory);

      // Format the new category using the existing formatting function
      const formattedCategories = formatPurposeCategoriesForTable({
        data: [newCategory],
      });
      const formattedCategory = formattedCategories[0];

      console.log("Formatted category for table:", formattedCategory);

      // Add the formatted category to the state
      setCategories((prevCategories) => [formattedCategory, ...prevCategories]);

      setSuccessMessage("Purpose category created successfully!");
      setShowSuccessModal(true);
      console.log("Category added to table state with proper formatting");
    } catch (error) {
      console.error("Error creating purpose category:", error);
    }
  };

  const handleEditSubmit = async (updatedCategoryData, originalItem) => {
    try {
      console.log("Updating purpose category in table:", {
        originalItem,
        updatedData: updatedCategoryData,
      });

      const updatedCategory = await purposeCategoriesService.update(
        originalItem.id,
        updatedCategoryData
      );
      console.log("Raw API response for updated category:", updatedCategory);

      // Format the updated category using the existing formatting function
      const formattedCategories = formatPurposeCategoriesForTable({
        data: [updatedCategory],
      });
      const formattedUpdatedCategory = formattedCategories[0];

      console.log(
        "Formatted updated category for table:",
        formattedUpdatedCategory
      );

      // Update the category in state with formatted data
      setCategories((prevCategories) =>
        prevCategories.map((category) =>
          category.id === originalItem.id ? formattedUpdatedCategory : category
        )
      );

      setSuccessMessage("Purpose category updated successfully!");
      setShowSuccessModal(true);
      console.log("Category updated in table state with proper formatting");
    } catch (error) {
      console.error("Error updating purpose category in table:", error);
    }
  };

  const handleDeleteConfirm = async (category) => {
    try {
      console.log("Deleting purpose category:", category);
      console.log(
        `Making DELETE request to /purpose-categories/${category.id}`
      );

      const success = await purposeCategoriesService.delete(category.id);

      if (success) {
        // Remove the category from state
        setCategories((prevCategories) =>
          prevCategories.filter((c) => c.id !== category.id)
        );
        setSuccessMessage("Purpose category deleted successfully!");
        setShowSuccessModal(true);
        console.log("Category removed from table state");
      } else {
        throw new Error("Delete operation did not return success status");
      }
    } catch (error) {
      console.error("Error deleting purpose category:", error);
    }
  };

  const handleView = (category) => {
    console.log("View purpose category:", category);
    // Here you would typically navigate to view page or show view modal
  };

  const handleLoadMore = async () => {
    setLoadingMore(true);
    try {
      console.log("Loading more purpose categories...");
      // For now, just refresh the data
      // In a real implementation, you might have pagination
      await fetchCategories();
    } catch (error) {
      console.error("Error loading more purpose categories:", error);
      console.error("Failed to load more purpose categories");
    } finally {
      setLoadingMore(false);
    }
  };

  return (
    <PrivateLayout perm_required={["purpose.categories.view"]}>
      {/* Data Table */}
      <div className="">
        <DataTable
          columns={columns}
          data={categories}
          searchPlaceholder="Search categories..."
          addButtonText="New Category"
          onView={handleView}
          actions={[
            { name: "view", is_visible: () => hasPermission("purpose.categories.view") },
            { name: "edit", is_visible: () => hasPermission("purpose.categories.edit") },
            { name: "delete", is_visible: () => hasPermission("purpose.categories.delete") },
          ]}
          loading={loading}
          loadingMore={loadingMore}
          loadMoreText="Load More Categories"
          onLoadMore={handleLoadMore}
          showLoadMore={true}
          showCreateButton = {hasPermission("purpose.categories.create") && true}
          allowMultiSelect={hasPermission("purpose.categories.delete") && true}
          highlightField="addedBy"
          highlightColors={{
            BUSINESS:
              "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
            PERSONAL:
              "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",
          }}
          modalSize="lg"
          createModalTitle="Add Purpose Category"
          editModalTitle="Edit Purpose Category"
          // Modal forms
          createForm={({ onClose }) => (
            <PurposeCategoryForm
              onClose={onClose}
              onSubmit={handleCreateSubmit}
            />
          )}
          editForm={({ item, onClose }) => (
            <PurposeCategoryForm
              item={item}
              onClose={onClose}
              onSubmit={handleEditSubmit}
            />
          )}
          deleteForm={({ item, onClose }) => (
            <DeleteConfirmation
              item={item}
              onClose={onClose}
              onConfirm={handleDeleteConfirm}
              title="Delete Purpose Category"
              message={`Are you sure you want to delete the category "${item?.name}"? This action cannot be undone.`}
              confirmText="Delete Category"
              type="danger"
            />
          )}
        />
      </div>

      {/* Success Modal */}
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        title="Success"
        message={successMessage}
      />
    </PrivateLayout>
  );
};

export default PurposeCategories;
