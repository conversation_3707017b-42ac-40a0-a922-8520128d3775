import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Edit, Trash2, <PERSON>otate<PERSON>cw, History, Loader2 } from "lucide-react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import Modal from "../components/common/Modal";
import { customerServiceService } from "../services/customerServiceService";
import { usersService } from "../services/userService";
import Select from "react-select";
import CustomCallHistoryModal from "../components/forms/profile/CustomCallHistoryModal";
import { hasPermission } from "../utils/permissionUtils";

const AllHitlistRecords = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [records, setRecords] = useState([]);
  const [selectedItem, setSelectedItem] = useState(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isReassignModalOpen, setIsReassignModalOpen] = useState(false);
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);
  const [users, setUsers] = useState([]);
  const [usersLoading, setUsersLoading] = useState(false);
  const [isEditLoading, setIsEditLoading] = useState(false);
  const [isDeleteLoading, setIsDeleteLoading] = useState(false);
  const [isReassignLoading, setIsReassignLoading] = useState(false);
  const [selectedType, setSelectedType] = useState(null);

  const [editForm, setEditForm] = useState({
    customerName: "",
    phoneNumber: "",
    accountNumber: "",
  });

  const [reassignForm, setReassignForm] = useState({
    assignedUser: null,
    first2: null,
    second2: null,
    third2: null,
  });

  // Fetch all hitlist records
  useEffect(() => {
    const fetchAllRecords = async () => {
      try {
        setLoading(true);
        const data = await customerServiceService.getAllHitlistRecords();
        setRecords(data);
      } catch (error) {
        console.error("Error fetching all hitlist records:", error);
        // TODO: Show error message to user
      } finally {
        setLoading(false);
      }
    };

    fetchAllRecords();
  }, []);

  // Status badge component
  const StatusBadge = ({ status }) => {
    const getStatusColor = (status) => {
      switch (status?.toLowerCase()) {
        case "completed":
          return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
        case "pending":
          return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
        case "not started":
          return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
        default:
          return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
      }
    };

    return (
      <span
        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(
          status
        )}`}
      >
        {status || "-"}
      </span>
    );
  };

  // Transform records for display
  const transformedRecords = records.map((record) => ({
    ...record,
    // Transform data for consistent display
    customer_name: record.customer_name,
    phone_number: record.phone_number,
    account_number: record.account_number || "N/A",
    // No need to transform assigned_agents as it's already an array in the response
    status_display:
      record.parent_hitlist_type === "Dormancy"
        ? record.call_status
        : record.phase_status || [],
  }));

  // Export/Print handlers
  const handleExport = () => {
    console.log("Exporting records");
    // TODO: Implement export functionality
  };

  const handlePrint = () => {
    console.log("Printing records");
    // TODO: Implement print functionality
  };

  // Multiselect delete handler
  const handleMultiDelete = (selectedIds) => {
    console.log("Multi-delete called with IDs:", selectedIds);
    // TODO: Implement actual API call for bulk delete
    console.log(`Would delete ${selectedIds.length} selected records`);
  };

  // Filter configuration
  const filterConfig = [
    {
      id: "type",
      label: "Type",
      type: "select",
      options: [
        { value: "all", label: "All Types" },
        { value: "Dormancy", label: "Dormancy" },
        { value: "2by2by2", label: "2by2by2" },
      ],
      value: selectedType,
      onChange: setSelectedType,
    },
  ];

  // Filter records based on selected type
  const filteredRecords = records.filter((record) => {
    if (!selectedType || selectedType.value === "all") return true;
    return record.parent_hitlist_type === selectedType.value;
  });

  // Define columns
  const getColumns = () => {
    return [
      {
        key: "parent_hitlist_code",
        title: "HITLIST CODE",
        render: (value) => (
          <span className="font-medium text-gray-900">{value}</span>
        ),
      },
      {
        key: "parent_hitlist_type",
        title: "HITLIST TYPE",
        render: (value) => (
          <span className="font-medium text-gray-900">{value}</span>
        ),
      },
      {
        key: "customer_name",
        title: "CUSTOMER NAME",
        render: (value) => (
          <span className="font-medium text-gray-900 dark:text-white">
            {value}
          </span>
        ),
      },
      {
        key: "phone_number",
        title: "PHONE NUMBER",
        render: (value) => (
          <span className="text-sm text-gray-600 dark:text-gray-400">
            {value || "-"}
          </span>
        ),
      },
      {
        key: "assigned_agents",
        title: "ASSIGNED AGENTS",
        render: (agents) => (
          <div className="h-[72px] overflow-y-auto space-y-1">
            {Array.isArray(agents) && agents.length > 0 ? (
              agents.map((agent, index) => (
                <div
                  key={index}
                  className="text-sm text-gray-600 dark:text-gray-400"
                >
                  {agent}
                </div>
              ))
            ) : (
              <div className="text-sm text-gray-600 dark:text-gray-400">-</div>
            )}
          </div>
        ),
      },
      {
        key: "status_display",
        title: "STATUS",
        render: (value, record) => {
          if (record.parent_hitlist_type === "Dormancy") {
            return <StatusBadge status={record.call_status || "Not Started"} />;
          } else {
            return (
              <div className="space-y-1">
                {record.phase_status?.map((phase, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <span className="text-sm text-gray-500 w-16">
                      {phase.phase}:
                    </span>
                    <StatusBadge status={phase.value} />
                  </div>
                )) || (
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    -
                  </div>
                )}
              </div>
            );
          }
        },
      },
    ];
  };

  // Event handlers
  const handleEdit = (item) => {
    setSelectedItem(item);
    setEditForm({
      customerName: item.customer_name || "",
      phoneNumber: item.phone_number || "",
      accountNumber: item.account_number || "",
    });
    setIsEditModalOpen(true);
  };

  const handleEditChange = (e) => {
    const { name, value } = e.target;
    setEditForm((prev) => ({ ...prev, [name]: value }));
  };

  const handleEditSubmit = async (e) => {
    e.preventDefault();
    if (!selectedItem) return;
    setIsEditLoading(true);
    try {
      const updated = await customerServiceService.updateHitlistRecord(
        selectedItem.id,
        {
          customerName: editForm.customerName,
          phoneNumber: editForm.phoneNumber,
          accountNumber: editForm.accountNumber,
        }
      );
      // Update the record in state
      setRecords((prev) =>
        prev.map((rec) =>
          rec.id === selectedItem.id ? { ...rec, ...updated } : rec
        )
      );
      setIsEditModalOpen(false);
      setSelectedItem(null);
    } catch (error) {
      alert("Failed to update record. Please try again.");
    } finally {
      setIsEditLoading(false);
    }
  };

  const handleEditCancel = () => {
    setIsEditModalOpen(false);
    setSelectedItem(null);
  };

  const handleDelete = (item) => {
    setSelectedItem(item);
    setIsDeleteModalOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!selectedItem) return;
    setIsDeleteLoading(true);
    try {
      const success = await customerServiceService.deleteHitlistRecord(
        selectedItem.id
      );
      if (success) {
        setRecords((prev) => prev.filter((rec) => rec.id !== selectedItem.id));
        setIsDeleteModalOpen(false);
        setSelectedItem(null);
      } else {
        alert("Failed to delete record. Please try again.");
      }
    } catch (error) {
      alert("Failed to delete record. Please try again.");
    } finally {
      setIsDeleteLoading(false);
    }
  };

  const handleDeleteCancel = () => {
    setIsDeleteModalOpen(false);
    setSelectedItem(null);
  };

  const handleCustomAction = (action, item) => {
    setSelectedItem(item);
    switch (action) {
      case "reassign":
        setIsReassignModalOpen(true);
        break;
      case "call-history":
        setIsHistoryModalOpen(true);
        break;
      default:
        console.log("Unhandled custom action:", action);
    }
  };

  // Fetch users and set initial reassign form values when modal opens
  useEffect(() => {
    if (isReassignModalOpen && selectedItem) {
      setUsersLoading(true);
      usersService
        .getAll()
        .then((data) => {
          const userOptions = data.map((user) => ({
            value: user.id,
            label: `${user.name} (${user.rm_code || user.rmCode || "No RM"})`,
            user,
          }));
          setUsers(userOptions);

          // Auto-select current user(s) if present
          if (selectedItem.parent_hitlist_type === "Dormancy") {
            const match = userOptions.find((u) =>
              u.label.includes(selectedItem.assigned_agents[0])
            );
            setReassignForm((prev) => ({
              ...prev,
              assignedUser: match || null,
            }));
          } else {
            // For 2by2by2, we'll use the same user for first2 and third2
            const first2Match = userOptions.find((u) =>
              u.label.includes(selectedItem.assigned_agents[0])
            );
            const second2Match = userOptions.find((u) =>
              u.label.includes(selectedItem.assigned_agents[1])
            );
            setReassignForm({
              first2: first2Match || null,
              second2: second2Match || null,
              third2: first2Match || null, // Same as first2
              assignedUser: null,
            });
          }
        })
        .finally(() => setUsersLoading(false));
    }
  }, [isReassignModalOpen, selectedItem]);

  const handleReassignChange = (field, value) => {
    setReassignForm((prev) => ({ ...prev, [field]: value }));
  };

  const handleReassignSubmit = async (e) => {
    e.preventDefault();
    if (!selectedItem) return;
    let payload = {};
    if (selectedItem.parent_hitlist_type === "Dormancy") {
      if (!reassignForm.assignedUser) {
        alert("Please select a user to reassign.");
        return;
      }
      payload = { newUser: reassignForm.assignedUser.value };
    } else {
      payload = {
        ...(reassignForm.first2
          ? { newFirst2: reassignForm.first2.value }
          : {}),
        ...(reassignForm.second2
          ? { newSecond2: reassignForm.second2.value }
          : {}),
        ...(reassignForm.third2
          ? { newThird2: reassignForm.third2.value }
          : {}),
      };
      if (!payload.newFirst2 && !payload.newSecond2 && !payload.newThird2) {
        alert("Please select at least one user to reassign.");
        return;
      }
    }
    setIsReassignLoading(true);
    try {
      const updated = await customerServiceService.reassignHitlistRecord(
        selectedItem.id,
        payload
      );
      setRecords((prev) =>
        prev.map((rec) =>
          rec.id === selectedItem.id ? { ...rec, ...updated } : rec
        )
      );
      setIsReassignModalOpen(false);
      setSelectedItem(null);
    } catch (error) {
      alert("Failed to reassign record. Please try again.");
    } finally {
      setIsReassignLoading(false);
    }
  };

  const handleReassignCancel = () => {
    setIsReassignModalOpen(false);
    setSelectedItem(null);
  };

  return (
    <PrivateLayout
      pageTitle="All Hitlist Records"
      perm_required={["customer.service.hitlists.view"]}
    >
      <div className="space-y-6">
        {/* Data Table */}
        <DataTable
          showFilters={true}
          filterConfig={filterConfig}
          columns={getColumns()}
          data={filteredRecords}
          searchPlaceholder="Search records..."
          actions={["edit", "delete", "reassign", "call-history"]}
          loading={loading}
          dataCountLabel="records"
          showDataCount={true}
          onCustomAction={handleCustomAction}
          customActionLabels={{
            reassign: "Reassign",
            "call-history": "See Call History",
          }}
          showImportExport={
            hasPermission("customer.service.import.export.print") && true
          }
          showImport={false}
          onExport={handleExport}
          onPrint={handlePrint}
          onEdit={handleEdit}
          onDelete={handleDelete}
          // Multiselect functionality
          allowMultiSelect={true}
          onMultiDelete={handleMultiDelete}
        />
      </div>

      {/* Edit Modal */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={handleEditCancel}
        title="Edit Customer Record"
        size="md"
      >
        <form onSubmit={handleEditSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Customer Name
            </label>
            <input
              type="text"
              name="customerName"
              value={editForm.customerName}
              onChange={handleEditChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Phone Number
            </label>
            <input
              type="tel"
              name="phoneNumber"
              value={editForm.phoneNumber}
              onChange={handleEditChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Account Number
            </label>
            <input
              type="text"
              name="accountNumber"
              value={editForm.accountNumber}
              onChange={handleEditChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={handleEditCancel}
              className="px-4 py-2 rounded-lg bg-gray-200 text-gray-800 hover:bg-gray-300 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isEditLoading}
              className={`px-4 py-2 rounded-lg text-white transition-colors flex items-center ${
                isEditLoading
                  ? "bg-blue-400 cursor-not-allowed"
                  : "bg-blue-600 hover:bg-blue-700"
              }`}
            >
              {isEditLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                "Save Changes"
              )}
            </button>
          </div>
        </form>
      </Modal>

      {/* Delete Modal */}
      <Modal
        isOpen={isDeleteModalOpen}
        onClose={handleDeleteCancel}
        title="Delete Customer Record"
        size="sm"
      >
        <div className="space-y-4">
          <p className="text-gray-600">
            Are you sure you want to delete this customer record? This action
            cannot be undone.
          </p>
          <div className="flex justify-end space-x-3">
            <button
              onClick={handleDeleteCancel}
              className="px-4 py-2 rounded-lg bg-gray-200 text-gray-800 hover:bg-gray-300 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleDeleteConfirm}
              disabled={isDeleteLoading}
              className={`px-4 py-2 rounded-lg text-white transition-colors flex items-center ${
                isDeleteLoading
                  ? "bg-red-400 cursor-not-allowed"
                  : "bg-red-600 hover:bg-red-700"
              }`}
            >
              {isDeleteLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete"
              )}
            </button>
          </div>
        </div>
      </Modal>

      {/* Reassign Modal */}
      <Modal
        isOpen={isReassignModalOpen}
        onClose={handleReassignCancel}
        title="Reassign Customer Record"
        size="md"
      >
        <form onSubmit={handleReassignSubmit} className="space-y-6 pt-[2rem]">
          {selectedItem?.parent_hitlist_type === "Dormancy" ? (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Assigned User
              </label>
              <Select
                name="assignedUser"
                value={reassignForm.assignedUser}
                onChange={(value) =>
                  handleReassignChange("assignedUser", value)
                }
                options={users}
                isLoading={usersLoading}
                placeholder="Select user..."
                className="react-select-container"
                classNamePrefix="react-select"
              />
            </div>
          ) : (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  First2 & Third2 Phase
                </label>
                <Select
                  name="first2"
                  value={reassignForm.first2}
                  onChange={(value) => handleReassignChange("first2", value)}
                  options={users}
                  isLoading={usersLoading}
                  placeholder="Select user..."
                  className="react-select-container"
                  classNamePrefix="react-select"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Second2 Phase
                </label>
                <Select
                  name="second2"
                  value={reassignForm.second2}
                  onChange={(value) => handleReassignChange("second2", value)}
                  options={users}
                  isLoading={usersLoading}
                  placeholder="Select user..."
                  className="react-select-container"
                  classNamePrefix="react-select"
                />
              </div>
              <div className="hidden">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Third2 assigned user
                </label>
                <Select
                  name="third2"
                  value={reassignForm.third2}
                  onChange={(value) => handleReassignChange("third2", value)}
                  options={users}
                  isLoading={usersLoading}
                  placeholder="Select user..."
                  className="react-select-container"
                  classNamePrefix="react-select"
                />
              </div>
            </>
          )}
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={handleReassignCancel}
              className="px-4 py-2 rounded-lg bg-gray-200 text-gray-800 hover:bg-gray-300 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isReassignLoading}
              className={`px-4 py-2 rounded-lg text-white transition-colors flex items-center ${
                isReassignLoading
                  ? "bg-green-400 cursor-not-allowed"
                  : "bg-green-600 hover:bg-green-700"
              }`}
            >
              {isReassignLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Reassigning...
                </>
              ) : (
                "Reassign"
              )}
            </button>
          </div>
        </form>
      </Modal>

      {/* Call History Modal */}
      <Modal
        isOpen={isHistoryModalOpen}
        onClose={() => setIsHistoryModalOpen(false)}
        title="Call History"
        size="lg"
      >
        <CustomCallHistoryModal
          item={selectedItem}
          onClose={() => setIsHistoryModalOpen(false)}
        />
      </Modal>
    </PrivateLayout>
  );
};

export default AllHitlistRecords;
