import { useState, useEffect } from "react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";

import { loanService, formatLoanCallsForTable } from "../services/loanService";
import { loanActivitiesService, formatLoanActivitiesForTable } from "../services/loanActivitiesService";
import { toast } from 'react-toastify';

const LoanCalls = () => {
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [calls, setCalls] = useState([]);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });
  const [filters, setFilters] = useState({
    loan_client_id: '',
    rm_user_id: '',
    purpose_id: '',
    interaction_type: '',
    followup_status: '',
    from_date: '',
    to_date: ''
  });

  // Define table columns (matching leads calls structure)
  const columns = [
    {
      key: "name",
      title: "NAME",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "anchor",
      title: "ANCHOR",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "mobile",
      title: "MOBILE",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
    {
      key: "status",
      title: "STATUS",
      render: (value) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          value === "Success" ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400" :
          value === "Pending" ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400" :
          value === "Failed" ? "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400" :
          "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400"
        }`}>
          {value || 'Unknown'}
        </span>
      ),
    },
    {
      key: "date",
      title: "DATE",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
  ];

  // Fetch calls data from API using new loan activities endpoint
  const fetchCalls = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log("Fetching loan activities data...");

      // Convert datetime-local to ISO string for API
      const apiFilters = { ...filters };
      if (apiFilters.from_date) {
        apiFilters.from_date = new Date(apiFilters.from_date).toISOString();
      }
      if (apiFilters.to_date) {
        apiFilters.to_date = new Date(apiFilters.to_date).toISOString();
      }

      const response = await loanActivitiesService.getAll(
        pagination.page,
        pagination.limit,
        apiFilters
      );

      // Format the data for the table
      const formattedCalls = formatLoanActivitiesForTable(response);

      setCalls(formattedCalls);
      setPagination(prev => ({
        ...prev,
        total: response.total,
        totalPages: response.totalPages
      }));

      console.log("Loan activities data loaded successfully:", formattedCalls.length, "activities");
    } catch (error) {
      console.error("Error fetching loan activities:", error);
      setError(error.message);
      toast.error(error.message || "Failed to load loan activities data");
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount and when filters/pagination change
  useEffect(() => {
    fetchCalls();
  }, [pagination.page, pagination.limit, filters]);

  // Handle load more
  const handleLoadMore = () => {
    setLoadingMore(true);
    // Simulate loading more data
    setTimeout(() => {
      setLoadingMore(false);
    }, 1000);
  };

  // Handle create call
  const handleCreateSubmit = async (formData) => {
    try {
      console.log("Creating new loan call:", formData);
      const newCall = await loanService.calls.create(formData);
      
      // Add the new call to the state
      const formattedCall = formatLoanCallsForTable({ data: [newCall] })[0];
      setCalls(prevCalls => [formattedCall, ...prevCalls]);
      
      toast.success("Loan call created successfully!");
      return true;
    } catch (error) {
      console.error("Error creating loan call:", error);
      toast.error(error.message || "Failed to create loan call");
      return false;
    }
  };





  // Handle view call
  const handleView = (call) => {
    console.log("View loan call:", call);
    // Here you would typically navigate to view page or show view modal
  };



  return (
    <PrivateLayout pageTitle="Loan Calls">
      {/* Data Table */}
      <div className="">
        <DataTable
          columns={columns}
          data={calls}
          searchPlaceholder="Search loan calls..."
          onView={handleView}
          loading={loading}
          loadingMore={loadingMore}
          loadMoreText="Load More Calls"
          onLoadMore={handleLoadMore}
          showLoadMore={true}
          highlightField="status"
          highlightColors={{
            Success: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
            Pending: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",
            Failed: "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400",
          }}
          // Data count
          showDataCount={true}
          dataCountLabel="loan calls"
        />
      </div>
    </PrivateLayout>
  );
};

export default LoanCalls;
