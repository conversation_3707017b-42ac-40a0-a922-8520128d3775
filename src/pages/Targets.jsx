import { useState, useEffect } from "react";
import { Eye, Plus } from "lucide-react";
import { useNavigate, useLocation, useSearchParams } from "react-router-dom";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import Modal from "../components/common/Modal";
import ProgressBreakdownModal from "../components/modals/ProgressBreakdownModal";
import TargetForm from "../components/forms/TargetForm";
import DeleteConfirmation from "../components/forms/DeleteConfirmation";
import { formatDateRange } from "../utils/dateUtils";
import { targetsService } from "../services/targetsService";
import { getTargetActivityDisplayName } from "../utils/targetActivityMapper";
import { toast } from "react-toastify";
import SuccessModal from "../components/modals/SuccessModal";
import { Check, Clock, Archive } from "lucide-react";
import { hasPermission } from "../utils/permissionUtils";

const Targets = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams, setSearchParams] = useSearchParams();

  // Get active tab from URL search params
  const getActiveTabFromParams = () => {
    const type = searchParams.get("type");
    if (type === "individual") return "individual";
    if (type === "role") return "role";
    return "role"; // default to role
  };

  const [activeTab, setActiveTab] = useState(getActiveTabFromParams());
  const [loading, setLoading] = useState(true);
  const [showProgressModal, setShowProgressModal] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedTarget, setSelectedTarget] = useState(null);
  const [targets, setTargets] = useState([]);

  // Filter states
  const [filters, setFilters] = useState({
    metric: "",
    status: "",
  });

  // Sync activeTab with URL parameter changes and set default if none
  useEffect(() => {
    const newActiveTab = getActiveTabFromParams();
    setActiveTab(newActiveTab);

    // If no type parameter is set, default to role
    if (!searchParams.get("type")) {
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.set("type", "role");
      setSearchParams(newSearchParams, { replace: true });
    }
  }, [searchParams, setSearchParams]);

  // Fetch targets on component mount and when filters change
  useEffect(() => {
    fetchTargets();
  }, [filters.status]);

  const fetchTargets = async () => {
    try {
      setLoading(true);
      const status = filters.status || null;
      const response = await targetsService.getAll(status);
      // console.log("Fetched targets:", response);
      setTargets(response || []);
    } catch (error) {
      console.error("Error fetching targets:", error);
      toast.error("Failed to load targets");
      setTargets([]);
    } finally {
      setLoading(false);
    }
  };

  // Statistics data
  const getStatistics = () => {
    const roleTargets = targets.filter(
      (target) => target.scope === "role"
    ).length;
    const individualTargets = targets.filter(
      (target) => target.scope === "individual"
    ).length;
    const totalTargets = targets.length;

    return [
      // { key: "total", label: "Total", value: totalTargets },
      { key: "role", label: "Role targets", value: roleTargets },
      {
        key: "individual",
        label: "Individual targets",
        value: individualTargets,
      },
    ];
  };

  // Filter data based on active tab and filters
  // Note: Status filtering is handled by the API call in fetchTargets()
  const filteredTargets = targets.filter((target) => {
    const matchesTab =
      activeTab === "role"
        ? target.scope === "role"
        : activeTab === "individual"
        ? target.scope === "individual"
        : true; // Show all when activeTab is "total"
    const matchesMetric =
      filters.metric === "" || target.metric === filters.metric;

    return matchesTab && matchesMetric;
  });

  // Handle statistic change
  const handleStatisticChange = (statisticKey) => {
    setActiveTab(statisticKey);
    // Update URL parameter based on selected statistic
    const newSearchParams = new URLSearchParams(searchParams);
    if (statisticKey === "individual") {
      newSearchParams.set("type", "individual");
    } else if (statisticKey === "role") {
      newSearchParams.set("type", "role");
    } else {
      // For "total", remove the type parameter or set to default
      newSearchParams.delete("type");
    }
    setSearchParams(newSearchParams);
  };

  // Custom action handler for DataTable
  const handleCustomAction = (action, row) => {
    if (action === "view-breakdown") {
      setSelectedTarget(row);
      setShowProgressModal(true);
    }
  };

  // Filter configuration for DataTable
  const filterConfig = [
    {
      key: "metric",
      label: "Type",
      field: "metric",
      placeholder: "All Types",
      selectedValue: filters.metric,
      options: [
        { value: "Call", label: "Call" },
        { value: "Visit", label: "Visit" },
      ],
    },
    {
      key: "status",
      label: "Status",
      field: "status",
      placeholder: "All Statuses",
      selectedValue: filters.status,
      options: [
        { value: "active", label: "Active" },
        { value: "archived", label: "Archived" },
        // { value: "expired", label: "Expired" },
      ],
    },
  ];

  // Filter handlers
  const handleFilterChange = (filterKey, value) => {
    setFilters((prev) => ({
      ...prev,
      [filterKey]: value,
    }));
  };

  const handleClearFilters = () => {
    setFilters({
      metric: "",
      status: "",
    });
  };

  // Create target handler
  const handleCreateTarget = async (targetData) => {
    try {
      console.log("Creating target:", targetData);

      // Call the API service
      const response = await targetsService.create(targetData);
      console.log("Target created successfully:", response);

      // Add the new target to the state instead of refreshing
      setTargets((prevTargets) => [{ ...response }, ...prevTargets]);

      // Show success modal
      setShowSuccessModal(true);
      setShowCreateModal(false);
    } catch (error) {
      console.error("Error creating target:", error);
      // toast.error("Failed to create target. Please try again.");
      throw error; // Re-throw to let the form handle it
    }
  };

  // Edit target handler
  const handleEditTarget = async (targetData, targetId) => {
    try {
      console.log("Updating target:", targetId, targetData);

      // Call the API service
      const response = await targetsService.update(targetId, targetData);
      // console.log("Target updated successfully:", response);

      // Refresh the targets list to get the latest data
      await fetchTargets();

      // toast.success("Target updated successfully!");
      setShowEditModal(false);
      setSelectedTarget(null);
    } catch (error) {
      console.error("Error updating target:", error);
      toast.error("Failed to update target. Please try again.");
      throw error; // Re-throw to let the form handle it
    }
  };

  // Delete target handler
  const handleDeleteTarget = async (target) => {
    try {
      console.log("Deleting target:", target.id);

      // Call the API service
      await targetsService.delete(target.id);
      console.log("Target deleted successfully");

      // Remove from state instead of refetching
      setTargets((prev) => prev.filter((t) => t.id !== target.id));

      toast.success("Target deleted successfully!");
    } catch (error) {
      console.error("Error deleting target:", error);
      toast.error("Failed to delete target. Please try again.");
    }
  };

  // Helper function to format period display
  const formatPeriod = (startDate, endDate) => {
    if (!endDate) return "Everyday";
    return formatDateRange(startDate, endDate, true); // true to include year
  };

  // Helper function to format target display
  const formatTarget = (value, metric) => {
    const unit = metric === "Call" ? "call" : "visit";
    const plural = value === 1 ? unit : `${unit}s`;
    return `${value} ${plural}`;
  };

  // Helper function to render status
  const renderStatus = (status) => {
    if (!status) return <span className="text-gray-400">-</span>;

    const statusConfig = {
      Active: { icon: Check, color: "text-green-600", bgColor: "bg-green-100" },
      Archived: {
        icon: Archive,
        color: "text-gray-600",
        bgColor: "bg-gray-100",
      },
      Expired: { icon: Clock, color: "text-red-600", bgColor: "bg-red-100" },
    };

    const config = statusConfig[status];
    if (!config) return <span className="text-gray-400">-</span>;

    const IconComponent = config.icon;

    return (
      <div
        className={`inline-flex items-center px-2 py-1 rounded-full ${config.bgColor}`}
      >
        <IconComponent size={14} className={`mr-1 ${config.color}`} />
        <span className={`text-xs font-medium ${config.color}`}>{status}</span>
      </div>
    );
  };

  // Define columns for the table
  const columns = [
    {
      key: "activity",
      title: "ACTIVITY",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {getTargetActivityDisplayName(value)}
        </span>
      ),
    },
    {
      key: "assigned_to",
      title: "ASSIGNED TO",
      render: (value, row) => {
        if (activeTab === "role") {
          // For role targets, just show the role name
          return (
            <span className="font-medium text-gray-900 dark:text-white">
              {typeof value === "string" ? value : value?.name || "Unknown"}
            </span>
          );
        } else {
          // For individual targets, show user name with role below in gray
          const userName = typeof value === "object" ? value?.name : value;
          const userRole =
            typeof value === "object" ? value?.role : row.assigned_to_role;

          return (
            <div className="flex flex-col">
              <span className="font-medium text-gray-900 dark:text-white">
                {userName || "Unknown User"}
              </span>
              <span className="text-xs text-gray-500">
                {userRole || "Unknown Role"}
              </span>
            </div>
          );
        }
      },
    },
    {
      key: "period",
      title: "PERIOD",
      render: (_, row) => (
        <span className="text-sm text-gray-700 dark:text-gray-300">
          {formatPeriod(row.start_date, row.end_date)}
        </span>
      ),
    },
    {
      key: "target",
      title: "TARGET",
      render: (_, row) => (
        <span className="font-semibold text-gray-900 dark:text-white">
          {formatTarget(row.value, row.metric)}
        </span>
      ),
    },
  ];

  // Add columns specific to role vs individual targets
  if (activeTab === "role") {
    // Role specific columns
    columns.push(
      {
        key: "users_count",
        title: "USERS",
        render: (value, row) => {
          const usersCount = row.users_count || 0;
          const exemptedUsers = row.exempted_users || 0;

          return (
            <div className="flex items-center space-x-2">
              <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
                <span>{usersCount}</span>
                {exemptedUsers > 0 && (
                  <span className="text-xs text-gray-500 ml-1">
                    ({exemptedUsers} exempted)
                  </span>
                )}
              </div>
              {/* Move eye icon here for role targets */}
              <button
                onClick={() => {
                  setSelectedTarget(row);
                  setShowProgressModal(true);
                }}
                className="p-1 hover:bg-gray-100 rounded transition-colors"
                title="View Breakdown"
              >
                <Eye size={16} className="text-gray-500 hover:text-gray-700" />
              </button>
            </div>
          );
        },
      },
      {
        key: "progress_today",
        title: "PROGRESS TODAY",
        render: (value, row) => {
          const achievedUsers = row.achieved_users || 0;
          const totalUsers = row.users_count || 0;

          return (
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {achievedUsers}/{totalUsers} users
            </span>
          );
        },
      },
      {
        key: "status",
        title: "STATUS",
        render: (value) => renderStatus(value),
      }
    );
  } else {
    // Individual specific columns
    columns.push(
      {
        key: "progress_today",
        title: "PROGRESS TODAY",
        render: (value, row) => {
          // Calculate progress based on metric type and API data
          const getCurrentValue = () => {
            if (row.metric === "Call") {
              return row.total_calls_made || 0;
            } else {
              return row.total_visits_made || 0;
            }
          };

          const getTargetValue = () => {
            return row.total_target || row.value || 0;
          };

          const currentValue = getCurrentValue();
          const targetValue = getTargetValue();
          const calculatedProgress =
            targetValue > 0
              ? Math.min(Math.round((currentValue / targetValue) * 100), 100)
              : 0;

          // Calculate overachievement
          const overachievement = Math.max(0, currentValue - targetValue);

          return (
            <div className="flex items-center space-x-2">
              <div className="flex-1 bg-gray-200 rounded-full h-2 dark:bg-gray-700 min-w-[80px]">
                <div
                  className="bg-green-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${calculatedProgress}%` }}
                ></div>
              </div>
              <div className="flex flex-col items-end">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {calculatedProgress}%
                </span>
                <span className="text-xs text-gray-500">
                  ({currentValue}/{targetValue})
                </span>
                {overachievement > 0 && (
                  <span
                    className="text-xs font-medium"
                    style={{ color: "#ff6d4c" }}
                  >
                    +{overachievement} extra
                  </span>
                )}
              </div>
            </div>
          );
        },
      },
      {
        key: "status",
        title: "STATUS",
        render: (value) => renderStatus(value),
      }
    );
  }

  return (
    <PrivateLayout pageTitle="Targets" perm_required={["targets.view"]}>
      <div className="space-y-6">
        {/* Data Table */}
        <DataTable
          columns={columns}
          data={filteredTargets}
          searchPlaceholder="Search targets..."
          addButtonText="Add Target"
          onAdd={() => setShowCreateModal(true)}
          actions={
            activeTab === "role"
              ? [
                  {
                    name: "view-breakdown",
                    is_visible: () => hasPermission(["targets.view"]),
                  },
                  {
                    name: "edit",
                    is_visible: () => hasPermission(["targets.update"]),
                  },
                  {
                    name: "delete",
                    is_visible: () => hasPermission(["targets.delete"]),
                  },
                ]
              : [
                  {
                    name: "edit",
                    is_visible: () => hasPermission(["targets.update"]),
                  },
                  {
                    name: "delete",
                    is_visible: () => hasPermission(["targets.delete"]),
                  },
                ]
          }
          loading={loading}
          showDataCount={true}
          dataCountLabel="targets"
          // Statistics functionality
          showStatistics={true}
          statistics={getStatistics()}
          activeStatistic={activeTab}
          onStatisticChange={handleStatisticChange}
          // Filters
          showFilters={true}
          filterConfig={filterConfig}
          onFilterChange={handleFilterChange}
          onClearFilters={handleClearFilters}
          // Custom actions
          onCustomAction={handleCustomAction}
          customActionLabels={{
            "view-breakdown": "View Breakdown",
          }}
          // showImport={hasPermission("users.import") && true}
          showExportPrint={hasPermission("targets.export.print") && true}
          showCreateButton={hasPermission("targets.create") && true}
          allowMultiSelect={hasPermission("targets.delete") && true}
          // Modal forms
          editForm={({ item, onClose }) => (
            <TargetForm
              item={item}
              onClose={onClose}
              onSubmit={handleEditTarget}
            />
          )}
          deleteForm={({ item, onClose }) => (
            <DeleteConfirmation
              item={item}
              onClose={onClose}
              onConfirm={handleDeleteTarget}
              itemName="target"
              confirmText="delete"
              title="Delete Target"
              message="Are you sure you want to delete this target? This action cannot be undone."
            />
          )}
          editModalTitle="Edit Target"
          deleteModalTitle=""
          modalSize="xl-tall"
          deleteModalSize="sm"
        />

        {/* Create Target Modal */}
        <Modal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          title="Create Daily Target"
          size="xl-tall"
        >
          <TargetForm
            onClose={() => {
              setShowCreateModal(false);
            }}
            onSubmit={handleCreateTarget}
          />
        </Modal>

        <SuccessModal
          isOpen={showSuccessModal}
          onClose={() => setShowSuccessModal(false)}
          title="Target Created"
          message="Target created successfully"
        />

        {/* Progress Breakdown Modal */}
        <ProgressBreakdownModal
          isOpen={showProgressModal}
          onClose={() => {
            setShowProgressModal(false);
            setSelectedTarget(null);
          }}
          target={selectedTarget}
          onTargetUpdate={(newTarget, removedUser, roleTarget) => {
            // Add the new individual target to the targets state
            setTargets((prev) => {
              const updatedTargets = [...prev, newTarget];

              // Update the role target by subtracting the removed user's values
              const roleTargetIndex = updatedTargets.findIndex(
                (t) => t.id === roleTarget.id
              );
              if (roleTargetIndex !== -1) {
                const currentRoleTarget = updatedTargets[roleTargetIndex];
                const userCurrentValue =
                  roleTarget.metric === "Call"
                    ? removedUser.calls_made || 0
                    : removedUser.visits_made || 0;

                updatedTargets[roleTargetIndex] = {
                  ...currentRoleTarget,
                  users_count: (currentRoleTarget.users_count || 1) - 1,
                  exempted_users: (currentRoleTarget.exempted_users || 0) + 1,
                  total_target:
                    (currentRoleTarget.total_target || 0) -
                    (removedUser.target || 0),
                  total_calls_made:
                    roleTarget.metric === "Call"
                      ? (currentRoleTarget.total_calls_made || 0) -
                        userCurrentValue
                      : currentRoleTarget.total_calls_made,
                  total_visits_made:
                    roleTarget.metric === "Visit"
                      ? (currentRoleTarget.total_visits_made || 0) -
                        userCurrentValue
                      : currentRoleTarget.total_visits_made,
                };
              }

              return updatedTargets;
            });
          }}
        />
      </div>
    </PrivateLayout>
  );
};

export default Targets;
