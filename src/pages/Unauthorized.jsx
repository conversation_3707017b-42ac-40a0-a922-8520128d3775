import { useNavigate } from "react-router-dom";
import { Shield, Home, ArrowLeft } from "lucide-react";
import { usePermissions } from "../contexts/PermissionContext";

const Unauthorized = () => {
  const navigate = useNavigate();
  const { role } = usePermissions();

  const handleGoToDashboard = () => {
    navigate("/dashboard");
  };

  const handleGoBack = () => {
    navigate(-1); // Go back to previous page
  };

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 flex items-center justify-center px-4">
      <div className="max-w-lg w-full">
        <div className="bg-white rounded-lg p-8 text-center">
          {/* Icon */}
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 dark:bg-red-900/20 mb-6">
            <Shield className="h-8 w-8 text-red-600 dark:text-red-400" />
          </div>

          {/* Title */}
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Access Denied
          </h1>

          {/* Message */}
          <p className="text-gray-600 dark:text-gray-400 mb-2">
            You don't have permission to access this page.
          </p>

          {/* {role && (
            <p className="text-sm text-gray-500 dark:text-gray-500 mb-6">
              Current role: <span className="font-medium">{role.name}</span>
            </p>
          )} */}

          {/* Action Buttons */}
          <div className="flex gap-[1rem] mt-[2rem]">
            <button
              onClick={handleGoToDashboard}
              className="w-full flex order-2 items-center justify-center px-4 py-2 text-[#165026] bg-gray-100 hover:bg-gray-200 font-medium rounded-lg transition-colors duration-200"
            >
              <Home className="h-4 w-4 mr-2" />
              Go to Dashboard
            </button>

            {/* <button
              onClick={handleGoBack}
              className="w-full order-1 flex items-center justify-center px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium rounded-lg transition-colors duration-200"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Go Back
            </button> */}
          </div>

          {/* Additional Info */}
          <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
            <p className="text-xs text-gray-500 dark:text-gray-500">
              If you believe this is an error, please contact your
              administrator.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Unauthorized;
