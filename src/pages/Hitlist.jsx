import { useState, useEffect, useRef } from "react";
import { Minus } from "lucide-react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import LeadForm from "../components/forms/LeadForm";
import CallForm from "../components/forms/CallForm";
import VisitForm from "../components/forms/VisitForm";
import DeleteConfirmation from "../components/forms/DeleteConfirmation";
import LeadProfile from "../components/forms/LeadProfile";
import ReassignModal from "../components/forms/ReassignModal";
import { leadsService, formatLeadsForTable } from "../services/leadsService";
import { downloadLeadsTemplate } from "../utils/excelUtils";
import { toast } from "react-toastify";
import { Loader2 } from "lucide-react";
import ConvertToClientModal from "../components/forms/ConvertToClientModal";
import { hasPermission } from "../utils/permissionUtils";

const Hitlist = () => {
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);

  // Filter states - now using a single object for all filters
  const [filters, setFilters] = useState({
    branch: "",
    type: "",
  });

  // Date range filter states
  const [fromDate, setFromDate] = useState("");
  const [toDate, setToDate] = useState("");

  // Filter configuration for the DataTable
  const filterConfig = [
    {
      key: "branch",
      label: "Branches",
      field: "branchName", // Field in the data to filter by
      placeholder: "All Branches",
      selectedValue: filters.branch,
      options: [
        { value: "Nairobi Main Branch", label: "Nairobi Main Branch" },
        { value: "Westlands Branch", label: "Westlands Branch" },
        { value: "Kikuyu Branch", label: "Kikuyu Branch" },
        { value: "Kisumu Branch", label: "Kisumu Branch" },
        { value: "Mombasa Branch", label: "Mombasa Branch" },
        { value: "Thika Branch", label: "Thika Branch" },
        { value: "Eldoret Branch", label: "Eldoret Branch" },
        { value: "Karen Branch", label: "Karen Branch" },
      ],
    },
    {
      key: "type",
      label: "Type",
      field: "type", // Field in the data to filter by
      placeholder: "All Types",
      selectedValue: filters.type,
      options: [
        { value: "New", label: "New" },
        { value: "Existing", label: "Existing" },
      ],
    },
  ];

  // Filter handlers
  const handleFilterChange = (filterKey, value) => {
    setFilters((prev) => ({
      ...prev,
      [filterKey]: value,
    }));
  };

  // Date range handlers
  const handleFromDateChange = (date) => {
    setFromDate(date);
  };

  const handleToDateChange = (date) => {
    setToDate(date);
  };

  const handleClearFilters = () => {
    setFilters({
      branch: "",
      type: "",
    });
    setFromDate("");
    setToDate("");
  };

  // Modal states
  const [showConvertModal, setShowConvertModal] = useState(false);
  const [showReassignModal, setShowReassignModal] = useState(false);

  // Define columns of the table here and what to render
  const columns = [
    {
      key: "anchor",
      title: "ANCHOR",
      render: (value) => (
        <span className="font-bold" style={{ color: "#7e7e7e" }}>
          {value && value !== "N/A" ? (
            value
          ) : (
            <Minus size={16} className="text-gray-400" />
          )}
        </span>
      ),
    },
    {
      key: "name",
      title: "NAME",
      render: (value, row) => {
        return (
          <span className="font-medium uppercase" style={{ color: "#7e7e7e" }}>
            {value}
          </span>
        );
      },
    },
    {
      key: "status",
      title: "STATUS",
      render: (value, row) => {
        const isUpdating = updatingLeadIds.has(row.id);

        const getStatusColor = (status) => {
          switch (status) {
            case "Cold":
              return "#1c5b41";
            case "Pending":
              return "#ffb800";
            case "Warm":
              return "#369dc9";
            case "Hot":
              return "#ff0000";
            default:
              return "#7e7e7e";
          }
        };

        const getStatusIcon = (status) => {
          const color = getStatusColor(status);
          return (
            <div
              className="w-2 h-2 rounded-full mr-2"
              style={{ backgroundColor: color }}
            />
          );
        };

        return (
          <div
            className="relative"
            ref={(el) => {
              if (el) {
                statusDropdownRefs.current[row.id] = el;
              }
            }}
          >
            <button
              onClick={(e) => handleStatusDropdownToggle(row.id, e)}
              disabled={isUpdating}
              className="inline-flex items-center px-2 py-1 text-xs font-semibold text-white rounded cursor-pointer disabled:opacity-50 hover:opacity-80 transition-opacity"
              style={{
                backgroundColor: getStatusColor(row.status || "Pending"),
              }}
            >
              {isUpdating && (
                <Loader2 size={12} className="animate-spin mr-1" />
              )}
              {row.status || "Pending"}
            </button>

            {openStatusDropdown === row.id && (
              <div
                className="absolute left-0 w-40 bg-white dark:bg-gray-700 rounded-md shadow-lg z-50 border border-gray-200 dark:border-gray-600"
                style={{
                  top: statusDropdownPosition[row.id]?.top || "100%",
                  bottom: statusDropdownPosition[row.id]?.bottom || "auto",
                  marginTop: statusDropdownPosition[row.id]?.showAbove
                    ? "-0.5rem"
                    : "0.5rem",
                  marginBottom: statusDropdownPosition[row.id]?.showAbove
                    ? "0.5rem"
                    : "0",
                  boxShadow: statusDropdownPosition[row.id]?.showAbove
                    ? "0 -4px 6px -1px rgba(0, 0, 0, 0.1), 0 -2px 4px -1px rgba(0, 0, 0, 0.06)"
                    : "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
                }}
              >
                <div className="py-2">
                  <div className="px-3 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 border-b border-gray-200 dark:border-gray-600">
                    Change Status
                  </div>
                  <div className="px-1 py-1">
                    {["Hot", "Warm", "Pending", "Cold"].map((status) => (
                      <button
                        key={status}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleStatusUpdate(row.id, status);
                          setOpenStatusDropdown(null);
                        }}
                        className={`flex items-center w-full px-3 py-2 text-sm text-left hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-150 rounded ${
                          (row.status || "Pending") === status
                            ? "bg-gray-50 dark:bg-gray-600"
                            : ""
                        }`}
                      >
                        {getStatusIcon(status)}
                        <span style={{ color: getStatusColor(status) }}>
                          {status}
                        </span>
                        {(row.status || "Pending") === status && (
                          <svg
                            className="w-4 h-4 ml-auto text-green-500"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                        )}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        );
      },
    },
    {
      key: "phoneNumber",
      title: "MOBILE",
      render: (value) => (
        <span className="text-sm" style={{ color: "#7e7e7e" }}>
          {value}
        </span>
      ),
    },
    {
      key: "visits",
      title: "VISITS",
      render: (value) => (
        <span className="text-sm" style={{ color: "#7e7e7e" }}>
          {value || 0}
        </span>
      ),
    },
    {
      key: "calls",
      title: "CALLS",
      render: (value) => (
        <span className="text-sm" style={{ color: "#7e7e7e" }}>
          {value || 0}
        </span>
      ),
    },
    {
      key: "lastInteraction",
      title: "LAST INTERACTION",
      render: (value, row) => {
        // Handle null, undefined, or "Never" cases
        if (
          !value ||
          value === "Never" ||
          (typeof value === "object" && !value.date)
        ) {
          return (
            <span className="text-sm" style={{ color: "#7e7e7e" }}>
              Never
            </span>
          );
        }

        const formatInteractionDate = (dateString) => {
          const date = new Date(dateString);
          const time = date.toLocaleTimeString("en-US", {
            hour: "numeric",
            minute: "2-digit",
            hour12: true,
          });
          const dateFormatted = date.toLocaleDateString("en-US", {
            day: "numeric",
            month: "short",
            year: "numeric",
          });
          return `${time} ${dateFormatted}`;
        };

        // Handle new object format: { interaction_type: "visit", date: "2025-07-30T10:08:00.763Z" }
        let interactionType = "call"; // default
        let dateToFormat = value;

        if (typeof value === "object" && value.interaction_type && value.date) {
          interactionType = value.interaction_type;
          dateToFormat = value.date;
        } else if (typeof value === "string") {
          // Fallback for old string format, try to get type from row
          interactionType = row.lastInteractionType || "call";
          dateToFormat = value;
        }

        return (
          <div className="flex flex-col">
            <span
              className="inline-flex px-2 py-1 text-xs font-semibold rounded mb-1 w-fit"
              style={{
                backgroundColor:
                  interactionType === "visit" ? "#fff6e0" : "#e0f2ff",
                color: interactionType === "visit" ? "#ffb800" : "#369dc9",
              }}
            >
              {interactionType}
            </span>
            <span className="text-sm" style={{ color: "#7e7e7e" }}>
              {formatInteractionDate(dateToFormat)}
            </span>
          </div>
        );
      },
    },
    // {
    //   key: "officer",
    //   title: "OFFICER",
    //   render: (value) => (
    //     <span className="text-sm text-center" style={{ color: "#7e7e7e" }}>

    //       John
    //     </span>
    //   ),
    // },
  ];

  // Leads data state
  const [leads, setLeads] = useState([]);

  // Status update state
  const [updatingLeadIds, setUpdatingLeadIds] = useState(new Set());
  const [openStatusDropdown, setOpenStatusDropdown] = useState(null);
  const [statusDropdownPosition, setStatusDropdownPosition] = useState({});
  const statusDropdownRefs = useRef({});

  // Calculate dropdown position
  const calculateStatusDropdownPosition = (buttonElement, rowId) => {
    const buttonRect = buttonElement.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const dropdownHeight = 160; // Height for 4 status options
    const spaceBelow = viewportHeight - buttonRect.bottom;
    const spaceAbove = buttonRect.top;
    const buffer = 20;

    const isInBottomThird = buttonRect.bottom > viewportHeight * 0.7;
    const showAbove =
      (spaceBelow < dropdownHeight + buffer && spaceAbove > spaceBelow) ||
      (isInBottomThird && spaceAbove > dropdownHeight);

    setStatusDropdownPosition({
      [rowId]: {
        showAbove,
        top: showAbove ? "auto" : "100%",
        bottom: showAbove ? "100%" : "auto",
      },
    });
  };

  // Handle status dropdown toggle
  const handleStatusDropdownToggle = (rowId, event) => {
    event.stopPropagation();
    if (openStatusDropdown === rowId) {
      setOpenStatusDropdown(null);
    } else {
      calculateStatusDropdownPosition(event.currentTarget, rowId);
      setOpenStatusDropdown(rowId);
    }
  };

  // Close status dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        openStatusDropdown &&
        statusDropdownRefs.current[openStatusDropdown]
      ) {
        const dropdownElement = statusDropdownRefs.current[openStatusDropdown];
        if (!dropdownElement.contains(event.target)) {
          setOpenStatusDropdown(null);
        }
      }
    };

    const handleScroll = () => {
      if (openStatusDropdown) {
        setOpenStatusDropdown(null);
      }
    };

    const handleResize = () => {
      if (openStatusDropdown) {
        setOpenStatusDropdown(null);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    window.addEventListener("scroll", handleScroll, true);
    window.addEventListener("resize", handleResize);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      window.removeEventListener("scroll", handleScroll, true);
      window.removeEventListener("resize", handleResize);
    };
  }, [openStatusDropdown]);

  // Fetch leads from API
  const fetchLeads = async () => {
    try {
      setLoading(true);
      console.log("Fetching leads from /leads endpoint...");
      const response = await leadsService.getAll();
      console.log("Raw API response:", response);

      const formattedLeads = formatLeadsForTable(response);
      console.log("Formatted leads for table:", formattedLeads);

      setLeads(formattedLeads);
    } catch (error) {
      console.error("Error fetching leads:", error);
      setLeads([]); // Set empty array on error
    } finally {
      setLoading(false);
    }
  };

  // Fetch leads on component mount
  useEffect(() => {
    fetchLeads();
  }, []);

  // Form submission handlers
  const handleCreateSubmit = async (newLeadData) => {
    console.log("Lead created by form, refreshing table:", newLeadData);

    try {
      // LeadForm already created the lead via API, just refresh the table
      await fetchLeads();

      toast.success("Lead created successfully!");
    } catch (error) {
      console.error("Error refreshing leads after creation:", error);
      toast.error("Lead created but failed to refresh table");
    }
  };

  const handleEditSubmit = async (updatedLeadData, originalItem) => {
    console.log("Lead updated by form, refreshing table:", {
      originalItem,
      updatedData: updatedLeadData,
    });

    try {
      // LeadForm already updated the lead via API, just refresh the table
      await fetchLeads();

      toast.success("Lead updated successfully!");
    } catch (error) {
      console.error("Error refreshing leads after update:", error);
      toast.error("Lead updated but failed to refresh table");
    }
  };

  const handleDeleteConfirm = async (lead) => {
    console.log("Deleting lead:", lead);

    try {
      console.log(`Making DELETE request to /leads/${lead.id}`);
      const success = await leadsService.delete(lead.id);

      if (success) {
        console.log("Lead deleted successfully (204 response)");

        // Remove the lead from state
        setLeads((prevLeads) => prevLeads.filter((l) => l.id !== lead.id));
        console.log("Lead removed from table state");

        // Show success toast notification
        toast.success(
          `Lead "${
            lead.name || lead.lead_name || "Unknown Lead"
          }" has been deleted successfully`
        );
      } else {
        throw new Error("Delete operation did not return success status");
      }
    } catch (error) {
      console.error("Error deleting lead:", error);

      // Show error toast notification
      const leadName = lead.name || lead.lead_name || "Unknown Lead";
      toast.error(`Failed to delete lead "${leadName}". Please try again.`);
    }
  };

  // Custom action handler
  const handleCustomAction = (action, item) => {
    console.log("Custom action:", action, "for item:", item);
    setSelectedItem(item);

    switch (action) {
      case "convert-to-client":
        setShowConvertModal(true);
        break;
      case "reassign":
        setShowReassignModal(true);
        break;
      case "set-pending":
      case "set-hot":
      case "set-warm":
      case "set-cold":
        // Handle status changes here if needed
        console.log(`Setting status to ${action} for lead:`, item.id);
        break;
      default:
        console.log("Unhandled custom action:", action);
    }
  };

  // Handle reassign action
  const handleReassign = async (leadId, rmCode) => {
    try {
      // Show loading state
      setLoading(true);
      
      // Call the reassign API
      const response = await leadsService.reassign(leadId, rmCode);
      
      // Show success message
      toast.success("Lead reassigned successfully!");
      
      // Close the modal
      setShowReassignModal(false);
      setSelectedItem(null);
      
      // Refresh the leads list
      await fetchLeads();
    } catch (error) {
      console.error("Error reassigning lead:", error);
      toast.error(error.message || "Failed to reassign lead");
    } finally {
      setLoading(false);
    }
  };

  // Multiselect delete handler
  const handleMultiDelete = (selectedIds) => {
    console.log("Multi-delete called with IDs:", selectedIds);
    // TODO: Implement actual API call for bulk delete
    toast.info(`Would delete ${selectedIds.length} selected records`);
  };

  // Convert to client success handler
  const handleConvertSuccess = (leadId, accountNumber, responseData) => {
    console.log("Lead converted to client:", {
      leadId,
      accountNumber,
      responseData,
    });

    // Remove the converted lead from the leads table immediately
    setLeads((prevLeads) => {
      const filteredLeads = prevLeads.filter((lead) => lead.id !== leadId);
      console.log(
        `Lead ${leadId} removed from table. Remaining leads: ${filteredLeads.length}`
      );
      return filteredLeads;
    });

    console.log("Lead removed from table state after conversion");

    setShowConvertModal(false);
    setSelectedItem(null);
  };

  // Other handlers (call, visit, etc.)
  const handleCallSubmit = async (callData, lead, error) => {
    console.log("Call submitted:", { callData, lead, error });

    // If call was created successfully (no error), update the state
    if (!error && callData && lead) {
      console.log("Call created successfully, updating lead state...");

      // Update the leads state to increment calls count and update last interaction
      setLeads((prevLeads) =>
        prevLeads.map((leadItem) => {
          if (leadItem.id === lead.id) {
            return {
              ...leadItem,
              calls: (leadItem.calls || 0) + 1,
              lastInteraction: {
                interaction_type: "call",
                date: new Date().toISOString(),
              },
            };
          }
          return leadItem;
        })
      );

      console.log("Lead state updated after call creation");
    }
  };

  const handleVisitSubmit = async (visitData, lead, error) => {
    console.log("Visit submitted:", { visitData, lead, error });

    // If visit was created successfully (no error), update the state
    if (!error && visitData && lead) {
      console.log("Visit created successfully, updating lead state...");

      // Update the leads state to increment visits count and update last interaction
      setLeads((prevLeads) =>
        prevLeads.map((leadItem) => {
          if (leadItem.id === lead.id) {
            return {
              ...leadItem,
              visits: (leadItem.visits || 0) + 1,
              lastInteraction: {
                interaction_type: "visit",
                date: new Date().toISOString(),
              },
            };
          }
          return leadItem;
        })
      );

      console.log("Lead state updated after visit creation");
    }
  };

  // Export is now handled by the ExportModal in DataTable component

  const handleDownloadTemplate = () => {
    try {
      downloadLeadsTemplate();
      // toast.success("Template downloaded successfully!");
    } catch (error) {
      console.error("Error downloading template:", error);
      toast.error("Failed to download template");
    }
  };

  const handleImport = async (file) => {
    try {
      console.log("=== HITLIST IMPORT HANDLER ===");
      console.log("Raw file object:", file);
      console.log("File properties:");
      console.log("  - name:", file.name);
      console.log("  - size:", file.size);
      console.log("  - type:", file.type);
      console.log("  - lastModified:", file.lastModified);
      console.log("==============================");

      // Send only the file to backend
      const result = await leadsService.importFromFile(file);
      console.log("Import result:", result);

      // Refresh the leads list
      await fetchLeads();

      // Show success message with count from backend response
      const count = result.inserted || result.count || "some";
      toast.success(`Successfully imported ${count} leads`);
    } catch (error) {
      console.error("Error importing leads:", error);

      // Handle different error types
      if (error.response?.data?.message) {
        toast.error(error.response.data.message);
      } else if (error.message) {
        toast.error(error.message);
      } else {
        toast.error(
          "Failed to import leads. Please check the file format and try again."
        );
      }
    }
  };

  const handleView = async (lead) => {
    setSelectedItem(lead);
    console.log("View lead:", lead);
    // API integration logic here
  };

  // Direct status update handler
  const handleStatusUpdate = async (leadId, newStatus) => {
    try {
      console.log(`Updating lead ${leadId} status to ${newStatus}`);

      // Find the current lead to get original status for rollback
      const currentLead = leads.find((lead) => lead.id === leadId);
      const originalStatus = currentLead?.status || "Pending";

      // Add to updating set for loading state
      setUpdatingLeadIds((prev) => new Set(prev).add(leadId));

      // Optimistic update - immediately update UI
      setLeads((prevLeads) =>
        prevLeads.map((lead) =>
          lead.id === leadId ? { ...lead, status: newStatus } : lead
        )
      );

      // Call API to update status
      const updatedLead = await leadsService.updateStatus(leadId, newStatus);

      // Update with actual response data
      setLeads((prevLeads) =>
        prevLeads.map((lead) =>
          lead.id === leadId
            ? { ...lead, status: updatedLead.status || newStatus }
            : lead
        )
      );

      toast.success(`Lead status updated to ${newStatus}`);
    } catch (error) {
      console.error("Status update failed:", error);

      // Rollback optimistic update on error
      setLeads((prevLeads) =>
        prevLeads.map((lead) =>
          lead.id === leadId ? { ...lead, status: originalStatus } : lead
        )
      );

      toast.error(error.message || "Failed to update status");
    } finally {
      // Remove from updating set
      setUpdatingLeadIds((prev) => {
        const newSet = new Set(prev);
        newSet.delete(leadId);
        return newSet;
      });
    }
  };

  const handleLoadMore = () => {
    setLoadingMore(true);
    setTimeout(() => {
      setLoadingMore(false);
      console.log("Load more leads");
    }, 2000);
  };

  return (
    <PrivateLayout
      pageTitle="Hit list"
      perm_required={["view.all.leads", "view.my.leads", "OR"]}
    >
      <div className="">
        <DataTable
          columns={columns}
          data={leads}
          searchPlaceholder="Search leads..."
          addButtonText="New Lead"
          onView={handleView}
          actions={[

            {name:"call",is_visible:()=> hasPermission("leads.call")}, 
            {name:"visit",is_visible:()=> hasPermission("leads.visit")}, 
            {name:"edit",is_visible:()=> hasPermission("leads.edit")}, 
            {name:"delete",is_visible:()=> hasPermission("leads.delete")}, 
            {name:"convert-to-client",is_visible:()=> hasPermission("leads.convert.to.client")}, 
            {name:"reassign",is_visible:()=> hasPermission("leads.edit")},

            

          ]}
          loading={loading}
          loadingMore={loadingMore}
          loadMoreText="Load More Leads"
          onLoadMore={handleLoadMore}
          showLoadMore={true}
          highlightField="type"
          onStatusUpdate={handleStatusUpdate}
          onCustomAction={handleCustomAction}
          highlightColors={{
            New: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",
            Existing:
              "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
          }}
          // Multiselect functionality
          allowMultiSelect={hasPermission("leads.delete") && true}
          showCreateButton = {hasPermission("leads.create") && true}
          onMultiDelete={handleMultiDelete}
          // Data count
          showDataCount={true}
          dataCountLabel="leads"
          // Filters - new flexible system
          showFilters={true}
          filterConfig={filterConfig}
          onFilterChange={handleFilterChange}
          onClearFilters={handleClearFilters}
          // Date range filter
          showDateRangeFilter={true}
          dateRangeField="created_at"
          fromDate={fromDate}
          toDate={toDate}
          onFromDateChange={handleFromDateChange}
          onToDateChange={handleToDateChange}
          // Modal forms
          createForm={({ onClose }) => (
            <LeadForm onClose={onClose} onSubmit={handleCreateSubmit} />
          )}
          editForm={({ item, onClose }) => (
            <LeadForm
              item={item}
              onClose={onClose}
              onSubmit={handleEditSubmit}
              initialAnchorRelationship={
                item?.anchor_relationship_id && item?.anchor_relationship_name
                  ? {
                      id: item.anchor_relationship_id,
                      name: item.anchor_relationship_name,
                    }
                  : null
              }
              initialCustomerCategory={
                item?.customer_category
                  ? {
                      id: item.customer_category.id,
                      name: item.customer_category.name,
                    }
                  : null
              }
              initialIsicSector={
                item?.isic_sector
                  ? {
                      id: item.isic_sector.id,
                      name: item.isic_sector.name,
                    }
                  : null
              }
              initialBranch={
                item?.branch
                  ? {
                      id: item.branch.id,
                      name: item.branch.name,
                    }
                  : null
              }
            />
          )}
          deleteForm={({ item, onClose }) => (
            <DeleteConfirmation
              item={item}
              onClose={onClose}
              onConfirm={handleDeleteConfirm}
              itemName="Lead"
            />
          )}
          callForm={({ item, onClose }) => (
            <CallForm
              item={item}
              onClose={onClose}
              onSubmit={handleCallSubmit}
            />
          )}
          visitForm={({ item, onClose }) => (
            <VisitForm
              item={item}
              onClose={onClose}
              onSubmit={handleVisitSubmit}
            />
          )}
          profileForm={({ item, onClose }) => (
            <LeadProfile item={item} onClose={onClose} />
          )}
          createModalTitle="Create New Lead"
          editModalTitle="Edit Lead"
          deleteModalTitle=""
          callModalTitle="Make Call"
          visitModalTitle="Schedule Visit"
          profileModalTitle=""
          modalSize="xl"
          deleteModalSize="sm"
          callModalSize="lg"
          visitModalSize="lg"
          profileModalSize="xl"
          // Import/Export functionality
          // Import/Export functionality
          showImport={hasPermission("leads.create") && true}
          showExportPrint={hasPermission("leads.view") && true}
          onImport={handleImport}
          importModalTitle="Import Leads"
          importTemplateFileName="Leads-Template.xlsx"
          importAcceptedFileTypes=".xlsx,.xls,.csv"
          onDownloadTemplate={handleDownloadTemplate}
          // Export modal configuration
          exportModalTitle="Export Leads"
          exportType="leads"
          exportSearchPlaceholder="Enter search terms to filter leads..."
          exportEmptySearchText="Leave empty to export all leads"
          exportButtonText="Export Leads"
          exportSuccessMessage="Leads exported successfully!"
        />
      </div>

      {/* Convert to Client Modal */}
      <ConvertToClientModal
        isOpen={showConvertModal}
        onClose={() => {
          setShowConvertModal(false);
          setSelectedItem(null);
        }}
        lead={selectedItem}
        onSuccess={handleConvertSuccess}
      />
      
      {/* Reassign Modal */}
      <ReassignModal
        isOpen={showReassignModal}
        onClose={() => {
          setShowReassignModal(false);
          setSelectedItem(null);
        }}
        lead={selectedItem}
        onReassign={handleReassign}
        loading={loading}
      />
    </PrivateLayout>
  );
};

export default Hitlist;
