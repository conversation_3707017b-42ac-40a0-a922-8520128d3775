import { useState, useEffect } from "react";
import { Target, TrendingUp, Trophy } from "lucide-react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import TargetSkeleton from "../components/skeletons/TargetSkeleton";
import { targetsService } from "../services/targetsService";
import { toast } from "react-toastify";
import {
  getActivityDisplayName,
  getActivityColor,
  calculateProgress,
  calculateRemaining,
  formatInteractionType,
  getStatusText,
  isActivityOnTrack,
  isActivityCompleted,
} from "../utils/activityMapper";

const MyTargets = () => {
  const [loading, setLoading] = useState(true);
  const [targets, setTargets] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState("All Categories");

  // Load targets from API
  useEffect(() => {
    const loadTargets = async () => {
      setLoading(true);
      try {
        const response = await targetsService.getMyTargets();
        if (response && response.activities) {
          const transformedTargets = response.activities
            .filter((activity) => activity.is_applicable) // Only show applicable activities
            .map((activity) => transformActivity(activity));
          setTargets(transformedTargets);
        } else {
          setTargets([]);
        }
      } catch (error) {
        console.error("Error loading my targets:", error);
        toast.error("Failed to load targets");
        setTargets([]);
      } finally {
        setLoading(false);
      }
    };

    loadTargets();
  }, []);

  // Transform API activity data to component format
  const transformActivity = (activity) => {
    const progress = calculateProgress(
      activity.done_count,
      activity.target_value
    );
    const remaining = calculateRemaining(
      activity.done_count,
      activity.target_value
    );

    return {
      id: activity.id,
      category: getActivityDisplayName(activity.activity),
      target: activity.target_value,
      done: activity.done_count,
      remaining: remaining,
      progress: progress,
      status: getStatusText(progress),
      color: getActivityColor(activity.activity),
      dueBy: "end of day",
      interactionType: activity.interaction_type || "call",
      rawActivity: activity.activity,
    };
  };

  // Calculate overall statistics
  const calculateStats = () => {
    const totalCategories = targets.length;
    const completedCategories = targets.filter((t) =>
      isActivityCompleted(t.progress)
    ).length;
    const onTrackCategories = targets.filter((t) =>
      isActivityOnTrack(t.progress)
    ).length;

    // Calculate overall progress with proper capping
    const overallProgress =
      targets.length > 0
        ? Math.round(
            targets.reduce((sum, t) => {
              // Use the capped progress (max 100%) for overall calculation
              return sum + Math.min(t.progress, 100);
            }, 0) / targets.length
          )
        : 0;

    return {
      totalCategories,
      completedCategories,
      onTrackCategories,
      overallProgress,
    };
  };

  const stats = calculateStats();

  // Filter targets based on selected category
  const filteredTargets =
    selectedCategory === "All Categories"
      ? targets
      : targets.filter((target) => target.category === selectedCategory);

  // Get category options for filter
  const categoryOptions = [
    "All Categories",
    ...new Set(targets.map((t) => t.category)),
  ];

  // Progress bar component
  const ProgressBar = ({ progress, color, target, done }) => {
    const getColorClasses = (color) => {
      switch (color) {
        case "green":
          return "bg-green-600";
        case "orange":
          return "bg-orange-500";
        case "red":
          return "bg-red-500";
        case "blue":
          return "bg-blue-600";
        case "purple":
          return "bg-purple-600";
        default:
          return "bg-gray-400";
      }
    };

    return (
      <div className="space-y-2">
        <div className="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${getColorClasses(
              color
            )}`}
            style={{ width: `${progress}%` }}
          ></div>
        </div>
        <div className="flex justify-between text-xs text-gray-600 dark:text-gray-400">
          <span>0</span>
          <span className="font-medium">
            {progress === 100 ? "100.0%" : `${progress}.0%`}
          </span>
          <span>
            {target} {target === 1 ? "call" : "calls"}
          </span>
        </div>
      </div>
    );
  };

  // Category card component
  const CategoryCard = ({ target }) => {
    const getStatusColor = (color) => {
      switch (color) {
        case "green":
          return "text-green-600";
        case "orange":
          return "text-orange-500";
        case "red":
          return "text-red-500";
        case "blue":
          return "text-blue-600";
        case "purple":
          return "text-purple-600";
        default:
          return "text-gray-500";
      }
    };

    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {target.category}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Due by {target.dueBy}
            </p>
          </div>
          <span
            className={`text-sm font-medium ${getStatusColor(target.color)}`}
          >
            {target.status}
          </span>
        </div>

        <div className="grid grid-cols-3 gap-4 mb-4">
          <div>
            <p className="text-xs font-medium text-gray-500 dark:text-gray-400">
              Target
            </p>
            <p className="text-lg font-semibold text-gray-900 dark:text-white">
              {target.target}{" "}
              {formatInteractionType(target.interactionType, target.target)}
            </p>
          </div>
          <div>
            <p className="text-xs font-medium text-gray-500 dark:text-gray-400">
              Done
            </p>
            <p className="text-lg font-semibold text-green-600">
              {target.done}{" "}
              {formatInteractionType(target.interactionType, target.done)}
            </p>
          </div>
          <div>
            <p className="text-xs font-medium text-gray-500 dark:text-gray-400">
              Remaining
            </p>
            <p className="text-lg font-semibold text-gray-900 dark:text-white">
              {target.remaining}{" "}
              {formatInteractionType(target.interactionType, target.remaining)}
            </p>
          </div>
        </div>

        <ProgressBar
          progress={target.progress}
          color={target.color}
          target={target.target}
          done={target.done}
        />
      </div>
    );
  };

  if (loading) {
    return (
      <PrivateLayout pageTitle="Daily Targets">
        <div className="space-y-6">
          {/* Header skeleton - matching the green header */}
          <div className="bg-[#1c5b41] rounded-lg p-6 animate-pulse">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-white/20 rounded-lg">
                  <div className="w-6 h-6 bg-white/30 rounded"></div>
                </div>
                <div>
                  <div className="h-8 bg-white/30 rounded w-40 mb-2"></div>
                  <div className="h-4 bg-white/20 rounded w-32"></div>
                </div>
              </div>
              <div className="text-right">
                <div className="h-4 bg-white/20 rounded w-32 mb-2"></div>
                <div className="h-8 bg-white/30 rounded w-16"></div>
              </div>
            </div>
          </div>

          {/* Filters skeleton */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="w-5 h-5 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-40 animate-pulse"></div>
            </div>
          </div>

          {/* Summary Statistics skeleton */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[1, 2, 3].map((item) => (
              <div
                key={item}
                className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 animate-pulse"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-24 mb-3"></div>
                    <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-16 mb-2"></div>
                    <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
                  </div>
                  <div className="p-3 bg-gray-100 dark:bg-gray-700 rounded-lg">
                    <div className="w-6 h-6 bg-gray-200 dark:bg-gray-600 rounded"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Targets section title skeleton */}
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-20 animate-pulse mt-12"></div>

          {/* Target Cards skeleton - matching the 2-column grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {Array.from({ length: 4 }).map((_, index) => (
              <div
                key={index}
                className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 animate-pulse"
              >
                {/* Header - Category name, due date, and status */}
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-32 mb-2"></div>
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
                  </div>
                  <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
                </div>

                {/* Stats grid - Target, Done, Remaining */}
                <div className="grid grid-cols-3 gap-4 mb-4">
                  <div>
                    <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-10 mb-1"></div>
                    <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-12"></div>
                  </div>
                  <div>
                    <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-8 mb-1"></div>
                    <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-10"></div>
                  </div>
                  <div>
                    <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-14 mb-1"></div>
                    <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-12"></div>
                  </div>
                </div>

                {/* Progress bar */}
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div
                    className="h-2 bg-gray-300 dark:bg-gray-600 rounded-full"
                    style={{ width: `${Math.random() * 80 + 10}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </PrivateLayout>
    );
  }

  return (
    <PrivateLayout pageTitle="Daily Targets">
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-[#1c5b41] to-green-600  rounded-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-white/20 rounded-lg">
                <Target size={24} />
              </div>
              <div>
                <h1 className="text-2xl font-bold">Daily Targets</h1>
                <p className="text-green-100">
                  {new Date().toLocaleDateString("en-US", {
                    weekday: "short",
                    year: "numeric",
                    month: "short",
                    day: "numeric",
                  })}
                </p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-green-100 text-sm">Categories Completed</p>
              <p className="text-3xl font-bold">
                {stats.completedCategories}/{stats.totalCategories}
              </p>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <svg
              className="w-5 h-5 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z"
              />
            </svg>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-green-500 focus:border-transparent"
            >
              {categoryOptions.map((option) => (
                <option key={option} value={option}>
                  {option}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Summary Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Total Targets
                </p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white mt-2">
                  {stats.totalCategories}
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  Active today
                </p>
              </div>
              <div className="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                <Target className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Overall Progress
                </p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white mt-2">
                  {stats.overallProgress}%
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  Across all categories
                </p>
              </div>
              <div className="p-3 bg-green-100 dark:bg-green-900/20 rounded-lg">
                <TrendingUp className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Targets on Track
                </p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white mt-2">
                  {stats.onTrackCategories}
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  ≥80% complete
                </p>
              </div>
              <div className="p-3 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg">
                <Trophy className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
              </div>
            </div>
          </div>
        </div>
        <h1 className="text-[20px] font-semibold mt-[3rem]"> Targets</h1>
        {/* Target Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {filteredTargets.map((target) => (
            <CategoryCard key={target.id} target={target} />
          ))}
        </div>

        {filteredTargets.length === 0 && !loading && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-12 text-center">
            <Target className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No targets found
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              {selectedCategory === "All Categories"
                ? "No targets are currently active."
                : `No targets found for ${selectedCategory}.`}
            </p>
          </div>
        )}
      </div>
    </PrivateLayout>
  );
};

export default MyTargets;
