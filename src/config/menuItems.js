import { BsQuestionCircle } from "react-icons/bs";
import { FaRegRectangleList } from "react-icons/fa6";
import { FaMoneyCheckAlt } from "react-icons/fa";
import {
  MdDashboard,
  MdPeople,
  MdAnchor,
  MdGroups,
  MdSettings,
  MdSupportAgent,
  MdTrackChanges,
  MdPersonOutline,
  MdSecurity,
} from "react-icons/md";
import { GiPadlock } from "react-icons/gi";
import { userHasPermissions } from "../utils/permissionUtils";

// Centralized menu items configuration for both desktop and mobile sidebars
export const menuItems = [
  {
    id: "dashboard",
    label: "Dashboard",
    icon: MdDashboard,
    href: "/dashboard",
    activePattern: "^/dashboard$",
  },
  {
    id: "items",
    label: "Items",
    icon: FaRegRectangleList,
    href: "#",
    hasChildren: true,
    children: [
      // { id: "products", label: "Products", href: "/items/products" },
      // { id: "categories", label: "Categories", href: "/items/categories" },
      // { id: "segments", label: "Segments", href: "/items/segments" },
      // { id: "types", label: "Types", href: "/items/types" },
      { id: "sectors", label: "ISIC Sectors", href: "/items/sectors", is_visible: () => userHasPermissions(["isic.sector.view"]) },
      { id: "regions", label: "Regions", href: "/items/regions", is_visible: () => userHasPermissions(["regions.view"]) },
      { id: "branches", label: "Branches", href: "/items/branches", is_visible: () => userHasPermissions(["branches.view"]) },
      { id: "purpose", label: "Purposes", href: "/items/purposes", is_visible: () => userHasPermissions(["purposes.view"]) },
      { id: "purpose-categories", label: "Purpose Categories", href: "/items/purpose-categories", is_visible: () => userHasPermissions(["purpose.categories.view"]) },
      { id: "customer-categories", label: "Customer Categories", href: "/items/customer-categories", is_visible: () => userHasPermissions(["customer.categories.view"]) },
      { id: "customer-feedback-categories", label: "Customer Feedback Categories", href: "/items/customer-feedback-categories", is_visible: () => userHasPermissions(["customer.feedback.category.view"]) },
    ],
  },
  {
    id: "leads",
    label: "Leads",
    icon: MdPeople,
    href: "#",
    hasChildren: true,
    children: [
      { id: "hitlist", label: "Leads - Hit List", href: "/leads/hitlist", is_visible: () => userHasPermissions(["view.all.leads", "view.my.leads", "OR"]) },
      { id: "calls", label: "Calls", href: "/leads/calls", is_visible: () => userHasPermissions(["leads.view.calls"]) },
      { id: "visits", label: "Visits", href: "/leads/visits", is_visible: () => userHasPermissions(["leads.view.visits"]) },
      { id: "follow-ups", label: "Follow Ups", href: "/leads/follow-ups", is_visible: () => userHasPermissions(["leads.view.followups"]) },

    ],
  },

  {
    id: "customer-service",
    label: "Customer Service",
    icon: MdSupportAgent,
    href: "#",
    hasChildren: true,
    children: [
      { id: "hitlist", label: "Hitlist", href: "/customer-service/hitlist", activePattern: "^/customer-service/hitlist$", is_visible: () => userHasPermissions(["customer.service.hitlists.view"]) },
      { id: "all-hitlist-records", label: "All", href: "/customer-service/hitlist/all", activePattern: "^/customer-service/hitlist/all$", is_visible: () => userHasPermissions(["customer.service.hitlists.view"]) },
      { id: "calls-to-do", label: "Calls To Do", href: "/customer-service/calls-to-do?type=pending", activePattern: "^/customer-service/calls-to-do", is_visible: () => userHasPermissions(["customer.service.personal.calls.view"]) }
    ],
  },

  {
    id: "loan-activities",
    label: "Loan Activities",
    icon: FaMoneyCheckAlt,
    href: "#",
    hasChildren: true,
    children: [
      { id: "hitlist", label: "Loan - Hit List", href: "/loan/hitlist", is_visible: () => userHasPermissions(["edit.customer"]) },
      { id: "calls", label: "Calls", href: "/loan/calls", is_visible: () => userHasPermissions(["edit.customer"]) },
      { id: "follow-ups", label: "Follow Ups", href: "/loan/follow-ups", is_visible: () => userHasPermissions(["edit.customer"]) },

    ],
  },
  {
    id: "anchors",
    label: "Anchors",
    icon: MdAnchor,
    href: "/anchors",
    activePattern: "^/anchors",
    is_visible: () => userHasPermissions(["anchors.view"]),
  },
  {
    id: "customers",
    label: "Customers",
    icon: MdGroups,
    href: "/customers",
    is_visible: () => userHasPermissions(["view.all.customers", "view.my.customers", "OR"]),
  },
  {
    id: "my-targets",
    label: "My Targets",
    icon: MdTrackChanges,
    href: "/my-targets",
    activePattern: "^/my-targets$",
    is_visible: true,
  },
  {
    id: "administration",
    label: "Administration",
    icon: MdSettings,
    href: "",
    hasChildren: true,
    children: [
      { id: "users", label: "Users", href: "/admin/users", activePattern: "^/admin/users", is_visible: () => userHasPermissions(["users.view"]) },
      { id: "roles", label: "Roles", href: "/admin/roles", activePattern: "^/admin/roles", is_visible: () => userHasPermissions(["roles.view"]) },
      {
        id: "targets",
        label: "Daily Targets",
        href: "/admin/targets",
        activePattern: "^/admin/targets",
        is_visible: () => userHasPermissions(["targets.view"])
      },
      {
        id: "holidays",
        label: "Holidays & Offdays",
        href: "/admin/holidays",
        activePattern: "^/admin/holidays",
        is_visible: () => userHasPermissions(["targets.view"])
      },
      // { id: "permissions", label: "Permissions", href: "#" },
    ],
  },
  {
    id: "security",
    label: "Security",
    icon: MdSecurity,
    href: "#",
    hasChildren: true,
    children: [
      {
        id: "change-password",
        label: "Change Password",
        href: "/change-password",
        icon: GiPadlock,
        activePattern: "^/change-password$",
        is_visible: true
      },
      {
        id: "multi-factor-auth",
        label: "Multi-Factor Authentication",
        href: "/security/mfa",
        icon: MdSecurity,
        activePattern: "^/security/mfa",
        is_visible: true
      },
    ],
  },
  {
    id: "support-tickets",
    label: "Support Tickets",
    icon: BsQuestionCircle,
    href: "/support-tickets",
    activePattern: "^/support-tickets",
    is_visible: true
  },
  // This is here just to show the page title of the notifications page because it uses the label to show the page title shown in the Navbar
  {
    id: "notifications",
    label: "Notifications",
    icon: BsQuestionCircle,
    href: "/notifications",
    activePattern: "^/notifications",
    is_visible: () => userHasPermissions(["doesnt.make.sense"])
  },

];

// Helper function to check if a route is active using regex patterns
export const isRouteActive = (pathname, item) => {
  // If item has activePattern, use regex matching
  if (item.activePattern) {
    const regex = new RegExp(item.activePattern);
    return regex.test(pathname);
  }

  // Fallback to exact match
  return item.href === pathname;
};

// Helper function to get page title from route (supports nested children)
export const getPageTitleFromRoute = (pathname) => {
  // First check direct matches
  const directMatch = menuItems.find(item => item.href === pathname);
  if (directMatch) {
    return directMatch.label;
  }

  // Check children for matches (supports nested children)
  for (const item of menuItems) {
    if (item.children) {
      const childMatch = item.children.find(child => child.href === pathname);
      if (childMatch) {
        return childMatch.label;
      }

      // Check nested children
      for (const child of item.children) {
        if (child.children) {
          const grandchildMatch = child.children.find(grandchild => grandchild.href === pathname);
          if (grandchildMatch) {
            return grandchildMatch.label;
          }
        }
      }
    }
  }

  // Check for role configuration pages
  if (pathname === "/admin/roles/create") {
    return "Create Role";
  }
  if (pathname.startsWith("/admin/roles/edit/")) {
    return "Edit Role";
  }

  // Check for targets pages
  if (pathname === "/admin/targets") {
    return "Targets";
  }

  // Check for holidays pages
  if (pathname === "/admin/holidays") {
    return "Holidays & Offdays";
  }

  // Check for profile page
  if (pathname === "/profile") {
    return "Profile";
  }

  // Check for hitlist page
  if (pathname === "/hitlist") {
    return "Hit list";
  }

  // Check for my targets page
  if (pathname === "/my-targets") {
    return "My Targets";
  }

  // Check for customer categories pages
  if (pathname === "/items/customer-categories") {
    return "Customer Categories";
  }
  if (pathname === "/items/customer-feedback-categories") {
    return "Customer Feedback Categories";
  }

  // Check for customer service pages
  if (pathname === "/customer-service/hitlist") {
    return "Customer Service - Hitlist";
  }
  if (pathname.startsWith("/customer-service/hitlist/")) {
    const hitlistCode = pathname.split("/").pop();
    return `Hitlist Details - ${hitlistCode}`;
  }
  if (pathname === "/customer-service/calls-to-do") {
    return "Customer Service - Calls To Do";
  }

  // Check for loan activity pages
  if (pathname === "/loan/hitlist") {
    return "Loan - Hit List";
  }
  if (pathname === "/loan/calls") {
    return "Loan Calls";
  }
  if (pathname === "/loan/follow-ups") {
    return "Loan Follow-ups";
  }

  // Check for login page
  if (pathname === "/login") {
    return "Login";
  }
  if (pathname === "/change-password") {
    return "Change Password";
  }
  if (pathname === "/security/mfa") {
    return "Multi-Factor Authentication";
  }

  // Default fallback
  return "Dashboard";
};
