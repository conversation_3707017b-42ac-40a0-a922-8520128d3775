import { createContext, useContext, useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import { useCookieStorage } from "../hooks/useCookieStorage";
import instance from "../axios/instance";

// Default fallback permissions
const DEFAULT_PERMISSIONS = [
  // { "id": "edit.customer" },
  // { "id": "delete.customer" }
];

// Default fallback role
const DEFAULT_ROLE = {
  // id: "default-role-id",
  // name: "<PERSON>y",
};

// Create the context
const PermissionContext = createContext();

// Permission Provider component
export const PermissionProvider = ({ children }) => {
  const location = useLocation();

  // State for permissions
  const [permissions, setPermissions] = useCookieStorage(
    "userPermissions",
    DEFAULT_PERMISSIONS
  );

  // State for role using cookie storage
  const [role, setRole] = useCookieStorage("userRole", DEFAULT_ROLE);

  // Loading state for permission fetching
  const [isLoadingPermissions, setIsLoadingPermissions] = useState(false);

  // Function to fetch permissions from API
  const fetchPermissions = async () => {
    setIsLoadingPermissions(true);
    try {
      const response = await instance.get("/permissions/my-permissions");

      if (response.data && response.data) {
        setPermissions(response.data);
      } else {
        // Fallback to default permissions if no data
        setPermissions(DEFAULT_PERMISSIONS);
      }
    } catch (error) {
      console.error("Error fetching permissions:", error);
      // Fallback to default permissions on error
      setPermissions(DEFAULT_PERMISSIONS);
    } finally {
      setIsLoadingPermissions(false);
    }
  };

  // Function to update permissions (called from login)
  const updatePermissions = (newPermissions) => {
    if (newPermissions && Array.isArray(newPermissions)) {
      setPermissions(newPermissions);
    } else {
      setPermissions(DEFAULT_PERMISSIONS);
    }
  };

  // Function to update role (called from login)
  const updateRole = (newRole) => {
    if (newRole && newRole.id && newRole.name) {
      setRole(newRole);
    } else {
      setRole(DEFAULT_ROLE);
    }
  };

  // Function to clear permissions and role (called on logout)
  const clearPermissions = () => {
    setPermissions(DEFAULT_PERMISSIONS);
    setRole(DEFAULT_ROLE);
  };

  // Function to check if user has specific permissions
  const hasPermissions = (requiredPermissions) => {
    if (
      !requiredPermissions ||
      !Array.isArray(requiredPermissions) ||
      requiredPermissions.length === 0
    ) {
      return true; // No permissions required
    }

    // Check if the last element is "OR" for OR logic
    const isOrLogic =
      requiredPermissions[requiredPermissions.length - 1] === "OR";
    const permsToCheck = isOrLogic
      ? requiredPermissions.slice(0, -1)
      : requiredPermissions;

    // Get array of permission IDs from stored permissions
    const userPermissionIds = permissions.map((perm) => perm.id);

    if (isOrLogic) {
      // OR logic: return true if user has ANY of the required permissions
      return permsToCheck.some((requiredPerm) =>
        userPermissionIds.includes(requiredPerm)
      );
    } else {
      // AND logic: return true if user has ALL required permissions
      return permsToCheck.every((requiredPerm) =>
        userPermissionIds.includes(requiredPerm)
      );
    }
  };

  // Effect to fetch permissions on page reload (but not on login page)
  useEffect(() => {
    const isLoginPage =
      location.pathname === "/login" ||
      location.pathname === "/forgot-password" ||
      location.pathname === "/reset-verification" ||
      location.pathname === "/reset-password" ||
      location.pathname === "/reset-success" ||
      location.pathname.startsWith("/mfa/");

    // Only fetch permissions if not on login-related pages
    if (!isLoginPage) {
      fetchPermissions();
    }
  }, [location.pathname]);

  // Context value
  const value = {
    permissions,
    role,
    isLoadingPermissions,
    updatePermissions,
    updateRole,
    clearPermissions,
    hasPermissions,
    fetchPermissions,
  };

  return (
    <PermissionContext.Provider value={value}>
      {children}
    </PermissionContext.Provider>
  );
};

// Custom hook to use the permission context
export const usePermissions = () => {
  const context = useContext(PermissionContext);
  if (!context) {
    throw new Error("usePermissions must be used within a PermissionProvider");
  }
  return context;
};

export default PermissionContext;
