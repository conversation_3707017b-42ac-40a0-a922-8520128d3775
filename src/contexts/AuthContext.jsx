import { createContext, useContext, useState, useEffect } from "react";
import { authService } from "../services/authService";
import {
  getAccessToken,
  isAuthenticated,
  clearAllAuthData,
} from "../utils/cookieUtils";

// Create the context
const AuthContext = createContext();

// Custom hook to use the auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

// Auth provider component
export const AuthProvider = ({ children }) => {
  const [isLoggedIn, setIsLoggedIn] = useState(isAuthenticated());
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showSessionExpiredModal, setShowSessionExpiredModal] = useState(
    localStorage.getItem("sessionExpired") === "true"
  );

  // Check if user is already logged in on app start
  useEffect(() => {
    const token = getAccessToken();
    if (token) {
      setIsLoggedIn(true);
      // You could also validate the token here if needed
    }
    setIsLoading(false);
  }, []);

  // Listen for session expiry events
  useEffect(() => {
    const handleSessionExpired = () => {
      console.log("Session expired event received");
      localStorage.setItem("sessionExpired", "true");
      setShowSessionExpiredModal(true);
    };

    window.addEventListener("sessionExpired", handleSessionExpired);

    return () => {
      window.removeEventListener("sessionExpired", handleSessionExpired);
    };
  }, []);

  const login = async (credentials, onPermissionsUpdate) => {
    try {
      const response = await authService.login(credentials);
      localStorage.removeItem("sessionExpired"); // Clear session expired flag on successful login
      setIsLoggedIn(true);
      setUser(response.user || { email: credentials.email });
      setShowSessionExpiredModal(false); // Hide modal if it was showing
      // console.log("Login response:", response);

      // Update permissions and role if callback provided and data exists
      if (onPermissionsUpdate && typeof onPermissionsUpdate === "function") {
        const permissions =
          response.user.permissions ||
          [
            // { id: "edit.customer" },
            // { id: "delete.customer" },
          ];

        const role =
          response.user.role ||
          {
            // id: "default-role-id",
            // name: "Branchy",
          };

        onPermissionsUpdate(permissions, role);
      }

      return response;
    } catch (error) {
      throw error; // Re-throw to let the component handle the error
    }
  };

  const logout = (onPermissionsClear) => {
    authService.logout();
    localStorage.removeItem("sessionExpired");
    setIsLoggedIn(false);
    setUser(null);
    setShowSessionExpiredModal(false);

    // Clear permissions if callback provided
    if (onPermissionsClear && typeof onPermissionsClear === "function") {
      onPermissionsClear();
    }
  };

  // Handle session expiry modal actions
  const handleSessionExpiredClose = () => {
    localStorage.removeItem("sessionExpired");
    setShowSessionExpiredModal(false);
  };

  const handleSessionExpiredLoginRedirect = () => {
    // Clear all auth state and tokens when user clicks "Go to Login"
    clearAllAuthData();
    localStorage.removeItem("sessionExpired");
    setIsLoggedIn(false);
    setUser(null);
    setShowSessionExpiredModal(false);
  };

  const value = {
    isLoggedIn,
    user,
    isLoading,
    login,
    logout,
    setIsLoggedIn,
    showSessionExpiredModal,
    handleSessionExpiredClose,
    handleSessionExpiredLoginRedirect,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export default AuthContext;
