import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import "./App.css";
import { AuthProvider } from "./contexts/AuthContext";
import { AppProvider } from "./contexts/AppContext";
import { ApiProvider } from "./contexts/ApiContext";
import { PermissionProvider } from "./contexts/PermissionContext";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Login from "./pages/Login";
import RequestReset from "./pages/RequestReset";
import ResetVerification from "./pages/ResetVerification";
import ResetPassword from "./pages/ResetPassword";
import ResetSuccess from "./pages/ResetSuccess";
import Dashboard from "./pages/Dashboard";
import Anchors from "./pages/Anchors";
import MFAMethodSelection from "./pages/MFAMethodSelection";
import MFAOTPVerification from "./pages/MFAOTPVerification";
import Users from "./pages/Users";
import Roles from "./pages/Roles";
import RoleConfiguration from "./pages/RoleConfiguration";
import SupportTickets from "./pages/SupportTickets";
import NotFound from "./pages/NotFound";
import ProtectedRoute from "./components/ProtectedRoute";
import SessionManager from "./components/SessionManager";
import Regions from "./pages/Regions";
import Sectors from "./pages/Sectors";
import Purposes from "./pages/Purposes";
import PurposeCategories from "./pages/PurposeCategories";
import Segments from "./pages/Segments";
import Categories from "./pages/Categories";
import Branches from "./pages/Branches";
import Types from "./pages/Types";
import Products from "./pages/Products";
import Customers from "./pages/Customers";
import FollowUps from "./pages/FollowUps";
import Calls from "./pages/Calls";
import Visits from "./pages/Visits";
import CustomerServiceHitlist from "./pages/CustomerServiceHitlist";
import HitlistDetails from "./pages/HitlistDetails";
import AllHitlistRecords from "./pages/AllHitlistRecords";
import CallsToDo from "./pages/CallsToDo";
import TwoByTwoByTwoActivities from "./pages/TwoByTwoByTwoActivities";
import Hitlist from "./pages/Hitlist";
import Targets from "./pages/Targets";
import MyTargets from "./pages/MyTargets";
import LoanHitlist from "./pages/LoanHitlist";
import LoanCalls from "./pages/LoanCalls";
import LoanFollowUps from "./pages/LoanFollowUps";
import ChangePassword from "./pages/ChangePassword";
import MultiFactorAuth from "./pages/MultiFactorAuth";
import CustomerCategories from "./pages/CustomerCategories";
import CustomerFeedbackCategories from "./pages/CustomerFeedbackCategories";
import Notifications from "./pages/Notifications";
import Unauthorized from "./pages/Unauthorized";
import Holidays from "./pages/Holidays";
import Profile from "./pages/Profile";
import { PrimeReactProvider } from "primereact/api";

import "primeicons/primeicons.css";

function App() {
  return (
    <PrimeReactProvider>
      <AuthProvider>
        <AppProvider>
          <Router>
            <PermissionProvider>
              <Routes>
                <Route path="/" element={<Navigate to="/login" replace />} />
                <Route path="/login" element={<Login />} />
                <Route path="/forgot-password" element={<RequestReset />} />
                <Route
                  path="/reset-verification"
                  element={<ResetVerification />}
                />
                <Route path="/reset-password" element={<ResetPassword />} />
                <Route path="/reset-success" element={<ResetSuccess />} />
                <Route
                  path="/mfa/select-method"
                  element={<MFAMethodSelection />}
                />
                <Route
                  path="/mfa/verify-otp"
                  element={<MFAOTPVerification />}
                />
                <Route
                  path="/change-password"
                  element={
                    <ProtectedRoute>
                      <ChangePassword />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/security/mfa"
                  element={
                    <ProtectedRoute>
                      <MultiFactorAuth />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/dashboard"
                  element={
                    <ProtectedRoute>
                      <Dashboard />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/notifications"
                  element={
                    <ProtectedRoute>
                      <Notifications />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/anchors"
                  element={
                    <ProtectedRoute>
                      <Anchors />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/support-tickets"
                  element={
                    <ProtectedRoute>
                      <SupportTickets />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/admin/users"
                  element={
                    <ProtectedRoute>
                      <Users />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/admin/roles"
                  element={
                    <ProtectedRoute>
                      <Roles />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/admin/roles/create"
                  element={
                    <ProtectedRoute>
                      <RoleConfiguration />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/admin/roles/edit/:id"
                  element={
                    <ProtectedRoute>
                      <RoleConfiguration />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/admin/targets"
                  element={
                    <ProtectedRoute>
                      <Targets />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/admin/holidays"
                  element={
                    <ProtectedRoute>
                      <Holidays />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/profile"
                  element={
                    <ProtectedRoute>
                      <Profile />
                    </ProtectedRoute>
                  }
                />
                <Route path="*" element={<NotFound />} />
                <Route
                  path="/items/regions"
                  element={
                    <ProtectedRoute>
                      <Regions />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/items/sectors"
                  element={
                    <ProtectedRoute>
                      <Sectors />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/items/purposes"
                  element={
                    <ProtectedRoute>
                      <Purposes />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/items/purpose-categories"
                  element={
                    <ProtectedRoute>
                      <PurposeCategories />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/items/segments"
                  element={
                    <ProtectedRoute>
                      <Segments />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/items/categories"
                  element={
                    <ProtectedRoute>
                      <Categories />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/items/branches"
                  element={
                    <ProtectedRoute>
                      <Branches />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/items/types"
                  element={
                    <ProtectedRoute>
                      <Types />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/items/products"
                  element={
                    <ProtectedRoute>
                      <Products />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/items/customer-categories"
                  element={
                    <ProtectedRoute>
                      <CustomerCategories />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/items/customer-feedback-categories"
                  element={
                    <ProtectedRoute>
                      <CustomerFeedbackCategories />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/customers"
                  element={
                    <ProtectedRoute>
                      <Customers />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/leads/follow-ups"
                  element={
                    <ProtectedRoute>
                      <FollowUps />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/leads/calls"
                  element={
                    <ProtectedRoute>
                      <Calls />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/leads/visits"
                  element={
                    <ProtectedRoute>
                      <Visits />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/customer-service/hitlist"
                  element={
                    <ProtectedRoute>
                      <CustomerServiceHitlist />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/customer-service/hitlist/all"
                  element={
                    <ProtectedRoute>
                      <AllHitlistRecords />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/customer-service/hitlist/:hitlistCode"
                  element={
                    <ProtectedRoute>
                      <HitlistDetails />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/customer-service/calls-to-do"
                  element={
                    <ProtectedRoute>
                      <CallsToDo />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/customer-service/2by2by2"
                  element={
                    <ProtectedRoute>
                      <TwoByTwoByTwoActivities />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/customer-service/2by2by2/:type"
                  element={
                    <ProtectedRoute>
                      <TwoByTwoByTwoActivities />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/leads/hitlist"
                  element={
                    <ProtectedRoute>
                      <Hitlist />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/my-targets"
                  element={
                    <ProtectedRoute>
                      <MyTargets />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/loan/hitlist"
                  element={
                    <ProtectedRoute>
                      <LoanHitlist />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/loan/calls"
                  element={
                    <ProtectedRoute>
                      <LoanCalls />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/loan/follow-ups"
                  element={
                    <ProtectedRoute>
                      <LoanFollowUps />
                    </ProtectedRoute>
                  }
                />
                <Route path="/unauthorized" element={<Unauthorized />} />
                <Route path="*" element={<NotFound />} />
              </Routes>
              <SessionManager />
              <ToastContainer position="bottom-right" />
            </PermissionProvider>
          </Router>
        </AppProvider>
      </AuthProvider>
    </PrimeReactProvider>
  );
}

export default App;
