import instance from "../axios/instance.jsx";

export const branchManagerDashboardService = {
  // Fetch first section data (summary cards, hitlist conversion, branch trend, calls & visits, dormant status)
  getFirstSectionData: async () => {
    try {
      const response = await instance.get("/dashboard/branch-manager?section=first");

      // Validate response structure
      if (!response.data) {
        throw new Error("No data received from API");
      }

      return response.data;
    } catch (error) {
      console.error("Error fetching branch manager dashboard first section data:", error);

      // Return default structure if API fails
      if (error.response?.status === 404 || error.code === 'NETWORK_ERROR') {
        console.warn("API endpoint not available, returning mock data");
        return {
          summary_cards: {
            hitlist_size: { all: 0, this_month: 0 },
            converted_customers: { all: 0, this_month: 0 },
            calls_completed: { all: 0, this_month: 0 },
            visits: { all: 0, this_month: 0 }
          },
          branch_conversion_trend: { "2025": [null, null, null, null, null, null, null, null, null, null, null, null] },
          calls_and_visits: {
            total: { leads: { calls: 0, visits: 0 }, customer_service: { calls: 0, visits: 0 }, loans: { calls: 0, visits: 0 } },
            this_month: { leads: { calls: 0, visits: 0 }, customer_service: { calls: 0, visits: 0 }, loans: { calls: 0, visits: 0 } }
          },
          dormant_records: {
            total: { uncontacted: 0, contacted: 0 },
            this_month: { uncontacted: 0, contacted: 0 }
          }
        };
      }

      throw error;
    }
  },

  // Fetch second section data (monthly hitlist progress, calls & visits vs targets, monthly targets per officer)
  getSecondSectionData: async () => {
    try {
      const response = await instance.get("/dashboard/branch-manager?section=second");

      // Validate response structure
      if (!response.data) {
        throw new Error("No data received from API");
      }

      return response.data;
    } catch (error) {
      console.error("Error fetching branch manager dashboard second section data:", error);

      // Return default structure if API fails
      if (error.response?.status === 404 || error.code === 'NETWORK_ERROR') {
        console.warn("API endpoint not available, returning mock data");
        return {
          monthly_hitlist_progress: { contacted: 0, remaining: 0 },
          calls_visits_vs_target: {
            actual: { name: "Actual", data: [0, 0] },
            target: { name: "Target", data: [0, 0] }
          },
          calls_vs_targets_per_officer: [
            { user_name: "No Data Available", calls_made: 0, calls_target: 0 }
          ],
          visits_vs_targets_per_officer: [
            { user_name: "No Data Available", visits_made: 0, visits_target: 0 }
          ]
        };
      }

      throw error;
    }
  },

  // Fetch all dashboard data (both sections)
  getAllData: async () => {
    try {
      const [firstSection, secondSection] = await Promise.all([
        branchManagerDashboardService.getFirstSectionData(),
        branchManagerDashboardService.getSecondSectionData(),
      ]);

      return {
        firstSection,
        secondSection,
      };
    } catch (error) {
      console.error("Error fetching all branch manager dashboard data:", error);
      throw error;
    }
  },
};
