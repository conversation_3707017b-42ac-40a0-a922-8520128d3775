import instance from '../axios/instance.jsx';
import {formatDateTime} from '../utils/formatters.js'

// Purposes API endpoints
const ENDPOINTS = {
  PURPOSES: '/purpose-of-activities',
  PURPOSE_BY_ID: (id) => `/purpose-of-activities/${id}`,
  PURPOSES_STATISTICS: '/purpose-of-activities/statistics',
};

// Purposes service for API calls
export const purposesService = {
  // Get all purposes
  getAll: async (searchQuery = '') => {
    try {
      console.log('=== FETCHING ALL PURPOSES ===');
      console.log(`API Endpoint: GET ${ENDPOINTS.PURPOSES}`);
      if (searchQuery) {
        console.log(`Search Query: ${searchQuery}`);
      }

      const config = {};
      if (searchQuery && searchQuery.trim()) {
        config.params = { search: searchQuery.trim() };
      }

      const response = await instance.get(ENDPOINTS.PURPOSES, config);

      console.log('Purposes response:', response.data);
      console.log('Total purposes:', response.data.total);
      console.log('===============================');

      return response.data;
    } catch (error) {
      console.error('Error fetching purposes:', error);

      // Handle different error types
      if (error.response?.status === 404) {
        throw new Error('Purposes endpoint not found.');
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to view purposes.');
      } else if (error.response?.status >= 500) {
        throw new Error('Server error while fetching purposes. Please try again later.');
      } else {
        throw new Error(error.response?.data?.message || 'Failed to fetch purposes.');
      }
    }
  },

  // Create a new purpose
  create: async (purposeData) => {
    try {
      console.log('=== CREATING NEW PURPOSE ===');
      console.log('Purpose data:', purposeData);
      console.log(`API Endpoint: POST ${ENDPOINTS.PURPOSES}`);

      const response = await instance.post(ENDPOINTS.PURPOSES, purposeData);

      console.log('Purpose creation response:', response.data);
      console.log('==============================');

      return response.data;
    } catch (error) {
      console.error('Error creating purpose:', error);

      // Handle different error types
      if (error.response?.status === 400) {
        throw new Error(error.response?.data?.message || 'Invalid purpose data provided.');
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to create purposes.');
      } else if (error.response?.status === 409) {
        throw new Error('A purpose with this name already exists.');
      } else if (error.response?.status >= 500) {
        throw new Error('Server error while creating purpose. Please try again later.');
      } else {
        throw new Error(error.response?.data?.message || 'Failed to create purpose.');
      }
    }
  },

  // Update an existing purpose
  update: async (id, purposeData) => {
    try {
      console.log('=== UPDATING PURPOSE ===');
      console.log(`Purpose ID: ${id}`);
      console.log('Updated data:', purposeData);
      console.log(`API Endpoint: PATCH ${ENDPOINTS.PURPOSE_BY_ID(id)}`);

      const response = await instance.patch(ENDPOINTS.PURPOSE_BY_ID(id), purposeData);

      console.log('Purpose update response:', response.data);
      console.log('========================');

      return response.data;
    } catch (error) {
      console.error('Error updating purpose:', error);

      // Handle different error types
      if (error.response?.status === 404) {
        throw new Error('Purpose not found. It may have been deleted.');
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to update this purpose.');
      } else if (error.response?.status === 400) {
        throw new Error(error.response?.data?.message || 'Invalid purpose data provided.');
      } else if (error.response?.status === 409) {
        throw new Error('A purpose with this name already exists.');
      } else if (error.response?.status >= 500) {
        throw new Error('Server error while updating purpose. Please try again later.');
      } else {
        throw new Error(error.response?.data?.message || 'Failed to update purpose.');
      }
    }
  },

  // Delete a purpose
  delete: async (id) => {
    try {
      console.log('=== DELETING PURPOSE ===');
      console.log(`Purpose ID: ${id}`);
      console.log(`API Endpoint: DELETE ${ENDPOINTS.PURPOSE_BY_ID(id)}`);

      const response = await instance.delete(ENDPOINTS.PURPOSE_BY_ID(id));

      console.log('Purpose deletion response:', response.status);
      console.log('========================');

      return response.status === 204 || response.status === 200;
    } catch (error) {
      console.error('Error deleting purpose:', error);

      // Handle different error types
      if (error.response?.status === 404) {
        throw new Error('Purpose not found. It may have already been deleted.');
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to delete this purpose.');
      } else if (error.response?.status === 409) {
        throw new Error('Cannot delete purpose. It is currently being used in activities.');
      } else if (error.response?.status >= 500) {
        throw new Error('Server error while deleting purpose. Please try again later.');
      } else {
        throw new Error(error.response?.data?.message || 'Failed to delete purpose.');
      }
    }
  },

  // Get a single purpose by ID
  getById: async (id) => {
    try {
      console.log('=== FETCHING PURPOSE BY ID ===');
      console.log(`Purpose ID: ${id}`);
      console.log(`API Endpoint: GET ${ENDPOINTS.PURPOSE_BY_ID(id)}`);

      const response = await instance.get(ENDPOINTS.PURPOSE_BY_ID(id));

      console.log('Purpose details response:', response.data);
      console.log('==============================');

      return response.data;
    } catch (error) {
      console.error('Error fetching purpose by ID:', error);

      // Handle different error types
      if (error.response?.status === 404) {
        throw new Error('Purpose not found.');
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to view this purpose.');
      } else if (error.response?.status >= 500) {
        throw new Error('Server error while fetching purpose. Please try again later.');
      } else {
        throw new Error(error.response?.data?.message || 'Failed to fetch purpose details.');
      }
    }
  },

  // Get purposes statistics
  getStatistics: async () => {
    try {
      console.log('=== FETCHING PURPOSES STATISTICS ===');
      console.log(`API Endpoint: GET ${ENDPOINTS.PURPOSES_STATISTICS}`);

      const response = await instance.get(ENDPOINTS.PURPOSES_STATISTICS);

      console.log('Purposes statistics response:', response.data);
      console.log('====================================');

      return response.data;
    } catch (error) {
      console.error('Error fetching purposes statistics:', error);

      // Handle different error types
      if (error.response?.status === 404) {
        throw new Error('Statistics endpoint not found.');
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to view statistics.');
      } else if (error.response?.status >= 500) {
        throw new Error('Server error while fetching statistics. Please try again later.');
      } else {
        throw new Error(error.response?.data?.message || 'Failed to fetch statistics.');
      }
    }
  },
};

/**
 * Format purposes data for table display
 * @param {Object} apiResponse - Response from purposes API
 * @returns {Array} - Formatted data for DataTable
 */
export const formatPurposesForTable = (apiResponse) => {
  if (!apiResponse || !apiResponse.data) {
    return [];
  }

  return apiResponse.data.map((purpose, index) => ({
    id: purpose.id || `purpose${index + 1}`,
    name: purpose.name || "Unknown Purpose",
    addedBy: purpose.created_by?.name || purpose.added_by || "Unknown",
    addedOn: formatDateTime(purpose.created_at),
    // Category information
    categoryId: purpose.purpose_category_id,
    categoryName: purpose.purpose_category?.name || "Unknown Category",
    category: purpose.purpose_category,
    // Additional fields for detailed view
    description: purpose.description,
    generalActivitiesCount: purpose.general_activities_count || 0,
    activitiesCount: purpose.activities_count || 0,
    totalActivitiesCount: purpose.total_activities_count || 0,
    isInUse: purpose.is_in_use || false,
    createdAt: purpose.created_at,
  }));
};



/**
 * Get usage status color for purposes
 * @param {boolean} isInUse - Whether purpose is in use
 * @param {number} totalCount - Total activities count
 * @returns {string} - Color classes
 */
export const getPurposeUsageColor = (isInUse, totalCount = 0) => {
  if (!isInUse || totalCount === 0) {
    return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
  } else if (totalCount >= 10) {
    return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
  } else if (totalCount >= 5) {
    return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
  } else {
    return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
  }
};

export default purposesService;
