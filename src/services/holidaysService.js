import instance from "../axios/instance";

// Holidays Service
export const holidaysService = {
  // Get all holidays
  getAll: async () => {
    try {
      const response = await instance.get("/holidays");
      return response.data;
    } catch (error) {
      console.error("Error fetching holidays:", error);
      throw error;
    }
  },

  // Create a new holiday
  create: async (holidayData) => {
    try {
      console.log("Creating holiday with data:", holidayData);
      const response = await instance.post("/holidays", holidayData);
      return response.data;
    } catch (error) {
      console.error("Error creating holiday:", error);
      throw error;
    }
  },

  // Update an existing holiday
  update: async (id, holidayData) => {
    try {
      console.log("Updating holiday with ID:", id, "Data:", holidayData);
      const response = await instance.patch(`/holidays/${id}`, holidayData);
      return response.data;
    } catch (error) {
      console.error("Error updating holiday:", error);
      throw error;
    }
  },

  // Delete a holiday
  delete: async (id) => {
    try {
      const response = await instance.delete(`/holidays/${id}`);
      return response.status === 204;
    } catch (error) {
      console.error("Error deleting holiday:", error);
      throw error;
    }
  },

  // Get a single holiday by ID
  getById: async (id) => {
    try {
      const response = await instance.get(`/holidays/${id}`);
      return response.data;
    } catch (error) {
      console.error("Error fetching holiday by ID:", error);
      throw error;
    }
  },
};

export default holidaysService;
