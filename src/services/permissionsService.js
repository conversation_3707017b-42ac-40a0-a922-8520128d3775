import instance from '../axios/instance.jsx';

// Permissions API endpoints
const ENDPOINTS = {
  PERMISSIONS: '/permissions/all',
  PERMISSION_BY_ID: (id) => `/permissions/${id}`,
};



// Permissions Service
export const permissionsService = {
  // Get all permissions
  getAll: async () => {
    try {
      const response = await instance.get(ENDPOINTS.PERMISSIONS);

      // Handle different response formats
      if (response.data && Array.isArray(response.data.data)) {
        return response.data.data;
      } else if (Array.isArray(response.data)) {
        return response.data;
      } else {
        console.warn('Unexpected permissions response format:', response.data);
        return [];
      }
    } catch (error) {
      console.error('Error fetching permissions:', error);
      throw error;
    }
  },

  // Get permissions grouped by their description field (which serves as category)
  getPermissionsGrouped: async () => {
    try {
      const response = await instance.get(ENDPOINTS.PERMISSIONS);

      // Handle different response formats
      let permissions = [];
      if (response.data && Array.isArray(response.data.data)) {
        permissions = response.data.data;
      } else if (Array.isArray(response.data)) {
        permissions = response.data;
      } else {
        console.warn('Unexpected permissions response format:', response.data);
        return [];
      }

      // Ensure permissions is an array
      if (!Array.isArray(permissions)) {
        console.error('Permissions is not an array:', permissions);

        // Try to extract array from different possible structures
        if (permissions && typeof permissions === 'object') {
          // Check if it's an object with array properties
          const possibleArrayKeys = ['permissions', 'data', 'items', 'results'];
          for (const key of possibleArrayKeys) {
            if (Array.isArray(permissions[key])) {
              permissions = permissions[key];
              break;
            }
          }
        }

        // If still not an array, return empty array
        if (!Array.isArray(permissions)) {
          console.error('Could not extract array from permissions data');
          return [];
        }
      }

      // Group permissions by their description field
      const groupedByDescription = {};

      console.log('Grouping permissions by description:');
      permissions.forEach(permission => {
        const category = permission.description || 'uncategorized';
        console.log(`Permission "${permission.name}" (${permission.id}) -> Category: "${category}"`);

        if (!groupedByDescription[category]) {
          groupedByDescription[category] = [];
        }

        groupedByDescription[category].push(permission);
      });

      console.log('Grouped by description:', groupedByDescription);

      // Convert to array format with category information
      const groupedPermissions = Object.keys(groupedByDescription).map(categoryKey => {
        // Create category ID and name from description
        const categoryId = categoryKey.toLowerCase().replace(/\s+/g, '-') + '-management';
        const categoryName = categoryKey.charAt(0).toUpperCase() + categoryKey.slice(1);

        console.log(`Creating category: "${categoryKey}" -> ID: "${categoryId}", Name: "${categoryName}", Permissions: ${groupedByDescription[categoryKey].length}`);

        return {
          id: categoryId,
          name: categoryName,
          description: categoryKey,
          permissions: groupedByDescription[categoryKey]
        };
      });

      console.log('Final grouped permissions:', groupedPermissions);
      return groupedPermissions;
    } catch (error) {
      console.error('Error fetching grouped permissions:', error);
      throw error;
    }
  },

  // Get permission by ID
  getById: async (id) => {
    try {
      const response = await instance.get(ENDPOINTS.PERMISSION_BY_ID(id));
      return response.data;
    } catch (error) {
      console.error('Error fetching permission by ID:', error);
      throw error;
    }
  },
};

// Roles Service
export const rolesService = {
  // Get all roles
  getAll: async () => {
    try {
      const response = await instance.get('/roles');
      console.log('Raw roles response:', response.data);

      // Handle different response formats
      if (response.data && Array.isArray(response.data.data)) {
        return response.data.data;
      } else if (Array.isArray(response.data)) {
        return response.data;
      } else {
        console.warn('Unexpected roles response format:', response.data);
        return [];
      }
    } catch (error) {
      console.error('Error fetching roles:', error);
      throw error;
    }
  },

  // Get role by ID
  getById: async (id) => {
    try {
      const response = await instance.get(`/roles/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching role by ID:', error);
      throw error;
    }
  },

  // Create a new role
  create: async (roleData) => {
    try {
      console.log('Creating role with data:', roleData);

      // Format payload according to backend expectations
      const payload = {
        name: roleData.name,
        description: roleData.description,
        permissionIds: roleData.permissions // Backend expects 'permissionIds' (already in correct format)
      };
      console.log('Permission IDs being sent:', roleData.permissions);
      console.log('Payload being sent to API:', payload);

      const response = await instance.post('/roles', payload);
      return response.data;
    } catch (error) {
      console.error('Error creating role:', error);
      console.error('Error response data:', error.response?.data);
      console.error('Error status:', error.response?.status);
      console.error('Error code:', error.code);

      // Show more specific error message based on error type
      let errorMessage = 'Failed to create role';

      if (error.code === 'ECONNABORTED') {
        errorMessage = 'Request timed out. The server might be taking longer than expected.';
      } else if (error.code === 'ERR_NETWORK') {
        errorMessage = 'Network error. Please check your connection.';
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      throw error;
    }
  },

  // Update an existing role
  update: async (id, roleData) => {
    try {
      console.log('Updating role with ID:', id, 'and data:', roleData);

      // Format payload according to backend expectations (permission IDs already in correct format)
      const payload = {
        name: roleData.name,
        description: roleData.description,
        permissionIds: roleData.permissions // Backend expects 'permissionIds'
      };
      console.log('Permission IDs being sent:', roleData.permissions);
      console.log('Payload being sent to API:', payload);

      const response = await instance.patch(`/roles/${id}`, payload);
      return response.data;
    } catch (error) {
      console.error('Error updating role:', error);
      console.error('Error response data:', error.response?.data);
      console.error('Error status:', error.response?.status);

      // Show more specific error message if available
      const errorMessage = error.response?.data?.message || 'Failed to update role';
      throw error;
    }
  },

  // Delete a role
  delete: async (id) => {
    try {
      const response = await instance.delete(`/roles/${id}`);
      return response.status === 204;
    } catch (error) {
      console.error('Error deleting role:', error);
      throw error;
    }
  },
};

// Data formatter functions
export const formatPermissionData = (permission) => {
  return {
    id: permission.id,
    name: permission.name,
    description: permission.description || 'N/A',
    action: permission.action || 'N/A',
    resource: permission.resource || 'N/A',
    created_at: permission.created_at,
    updated_at: permission.updated_at,
  };
};

export const formatRoleData = (role) => {
  return {
    id: role.id,
    name: role.name,
    description: role.description || 'N/A',
    permissions: role.permissions || [],
    created_at: role.created_at,
    updated_at: role.updated_at,
  };
};

export const formatPermissionsResponse = (response) => {
  if (!response || !response.data) {
    return [];
  }

  return response.data.map(formatPermissionData);
};

export const formatRolesResponse = (response) => {
  if (!response || !response.data) {
    return [];
  }

  return response.data.map(formatRoleData);
};

// Permission category icons mapping
export const getCategoryIcon = (categoryId) => {
  const iconMap = {
    'items-management': 'CreditCard',
    'customers-management': 'Users',
    'leads-management': 'TrendingUp',
    'anchors-management': 'Settings',
    'administration-management': 'Building2',
    'dashboard-management': 'Shield',
    'default': 'Settings'
  };

  console.log(`Getting icon for category: ${categoryId} -> ${iconMap[categoryId] || iconMap.default}`);
  return iconMap[categoryId] || iconMap.default;
};

// Permission category colors mapping
export const getCategoryColors = (categoryId) => {
  const colorMap = {
    'items-management': {
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    'customers-management': { // Fixed: customers (plural) not customer
      color: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    'anchors-management': {
      color: 'text-gray-600',
      bgColor: 'bg-gray-50'
    },
    'leads-management': {
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50'
    },
    'administration-management': {
      color: 'text-teal-600',
      bgColor: 'bg-teal-50'
    },
    'dashboard-management': {
      color: 'text-purple-600',
      bgColor: 'bg-purple-50'
    },
    'default': {
      color: 'text-gray-600',
      bgColor: 'bg-gray-50'
    }
  };

  return colorMap[categoryId] || colorMap.default;
};



export default permissionsService;
