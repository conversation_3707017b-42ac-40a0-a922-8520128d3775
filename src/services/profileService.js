import instance from '../axios/instance';

// Profile API endpoints
const ENDPOINTS = {
  PROFILE_ME: '/profile/me',
};

// Profile Service
export const profileService = {
  // Get current user profile
  getProfile: async () => {
    try {
      console.log('=== FETCHING USER PROFILE ===');
      console.log(`API Endpoint: GET ${ENDPOINTS.PROFILE_ME}`);
      
      const response = await instance.get(ENDPOINTS.PROFILE_ME);
      
      console.log('Profile response:', response.data);
      console.log('=============================');
      
      return response.data;
    } catch (error) {
      console.error('Error fetching profile:', error);
      throw error;
    }
  },

  // Update user profile
  updateProfile: async (formData) => {
    try {
      console.log('=== UPDATING USER PROFILE ===');
      console.log(`API Endpoint: PATCH ${ENDPOINTS.PROFILE_ME}`);
      console.log('FormData contents:', formData);
      
      const response = await instance.patch(ENDPOINTS.PROFILE_ME, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      
      console.log('Profile update response:', response.data);
      console.log('=============================');
      
      return response.data;
    } catch (error) {
      console.error('Error updating profile:', error);
      throw error;
    }
  },
};

// Helper function to format member_since date
export const formatMemberSince = (dateString) => {
  if (!dateString) return 'Not specified';
  
  try {
    const date = new Date(dateString);
    const day = date.getDate();
    const month = date.toLocaleString('default', { month: 'long' });
    const year = date.getFullYear();
    
    // Add ordinal suffix to day
    const getOrdinalSuffix = (day) => {
      if (day > 3 && day < 21) return 'th';
      switch (day % 10) {
        case 1: return 'st';
        case 2: return 'nd';
        case 3: return 'rd';
        default: return 'th';
      }
    };
    
    return `${day}${getOrdinalSuffix(day)} ${month} ${year}`;
  } catch (error) {
    console.error('Error formatting member_since date:', error);
    return 'Invalid date';
  }
};
