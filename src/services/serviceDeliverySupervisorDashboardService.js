import instance from "../axios/instance.jsx";

export const serviceDeliverySupervisorDashboardService = {
  // Fetch dashboard data for service delivery supervisor and customer experience officer
  getDashboardData: async () => {
    try {
      const response = await instance.get("/dashboard/customer-service?officer=cxo");

      // Validate response structure
      if (!response.data) {
        throw new Error("No data received from API");
      }

      return response.data;
    } catch (error) {
      console.error("Error fetching service delivery supervisor dashboard data:", error);

      // Return default structure if API fails
      if (error.response?.status === 404 || error.code === 'NETWORK_ERROR') {
        console.warn("API endpoint not available, returning mock data");
        return {
          summary_cards: {
            total_dormant_size: { total: 0, this_month: 0 },
            total_2by2by2_size: { total: 0, this_month: 0 },
            calls_this_month: 0,
            overdue_calls_this_month: 0
          },
          personal: {
            calls_today: 0,
            overdue_calls: 0,
            upcoming_calls: [],
            my_calls_this_month: 0,
            monthly_calls_vs_targets: {
              last_month: { target: 0, calls: 0 },
              this_month: { target: 0, calls: 0 }
            }
          },
          completion_by_phase: [
            { name: "First 2", total: 0, completed: 0 },
            { name: "Second 2", total: 0, completed: 0 },
            { name: "Third 2", total: 0, completed: 0 }
          ],
          "2by2by2_completion": {
            completed: 0,
            in_progress: 0
          },
          monthly_customer_feedback: [
            { name: "Positive", value: 0 },
            { name: "Negative", value: 0 }
          ],
          monthly_call_status: [
            { name: "Success", value: 0 },
            { name: "No answer", value: 0 }
          ],
          monthly_dormant_progress: {
            completed: 0,
            remaining: 0
          },
          overdue_calls_trend: [
            { month: "January 2025", value: null },
            { month: "February 2025", value: null },
            { month: "March 2025", value: null },
            { month: "April 2025", value: null },
            { month: "May 2025", value: null },
            { month: "June 2025", value: null }
          ],
          overdue_calls: []
        };
      }

      throw error;
    }
  },
};
