import instance from '../axios/instance.jsx';
import {formatDateTime} from '../utils/formatters.js'

// Branches API endpoints
const ENDPOINTS = {
  BRANCHES: '/branches',
  BRANCH_BY_ID: (id) => `/branches/${id}`,
};

// Branches Service
export const branchesService = {
  // Get all branches
  getAll: async (params = {}) => {
    try {
      const response = await instance.get(ENDPOINTS.BRANCHES, { params });
      return formatBranchesResponse(response.data);
    } catch (error) {
      console.error('Error fetching branches:', error);
      throw error;
    }
  },

  // Create a new branch
  create: async (branchData) => {
    try {
      const response = await instance.post(ENDPOINTS.BRANCHES, branchData);
      return response.data;
    } catch (error) {
      console.error('Error creating branch:', error);
      throw error;
    }
  },

  // Update an existing branch
  update: async (id, branchData) => {
    try {
      const response = await instance.patch(ENDPOINTS.BRANCH_BY_ID(id), branchData);
      return response.data;
    } catch (error) {
      console.error('Error updating branch:', error);
      throw error;
    }
  },

  // Delete a branch
  delete: async (id) => {
    try {
      const response = await instance.delete(ENDPOINTS.BRANCH_BY_ID(id));
      return response.status === 204;
    } catch (error) {
      console.error('Error deleting branch:', error);
      throw error;
    }
  },

  // Get a single branch by ID
  getById: async (id) => {
    try {
      const response = await instance.get(ENDPOINTS.BRANCH_BY_ID(id));
      return response.data;
    } catch (error) {
      console.error('Error fetching branch by ID:', error);
      throw error;
    }
  },
};

// Data formatter functions
export const formatBranchData = (branch) => {
  return {
    id: branch.id,
    name: branch.name,
    code: branch.code || 'N/A',
    location: branch.location || 'N/A',
    address: branch.address || 'N/A',
    phoneNumber: branch.phoneNumber || 'N/A',
    email: branch.email || 'N/A',
    manager: branch.manager || 'N/A',
    addedOn: formatDateTime(branch.created_at), // API field is created_at, not addedOnDate
    addedBy: branch.addedBy || 'N/A',
    region: branch.region, // Keep the region object for the table to access region.name
    // Format date for display
    formattedDate: formatDateTime(branch.created_at),
  };
};



export const formatBranchesResponse = (response) => {
  // Handle both paginated response format and direct array format
  if (!response) {
    return [];
  }
  
  // If response has data property (paginated format)
  if (response.data) {
    return {
      ...response,
      data: response.data.map(formatBranchData)
    };
  }
  
  // If response is already an array (direct format)
  if (Array.isArray(response)) {
    return response.map(formatBranchData);
  }
  
  return [];
};

export default branchesService;
