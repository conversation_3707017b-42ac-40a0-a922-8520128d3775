import instance from '../axios/instance.jsx';

// Customer Feedback Categories API endpoints
const ENDPOINTS = {
  CUSTOMER_FEEDBACK_CATEGORIES: '/customer-feedback-categories',
  CUSTOMER_FEEDBACK_CATEGORY_BY_ID: (id) => `/customer-feedback-categories/${id}`,
};

// Customer Feedback Categories Service
export const customerFeedbackCategoriesService = {
  // Get all customer feedback categories
  getAll: async () => {
    try {
      const response = await instance.get(ENDPOINTS.CUSTOMER_FEEDBACK_CATEGORIES);
      // console.log("Customer feedback categories:", response.data.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching customer feedback categories:', error);
      throw error;
    }
  },

  // Create a new customer feedback category
  create: async (categoryData) => {
    try {
      const response = await instance.post(ENDPOINTS.CUSTOMER_FEEDBACK_CATEGORIES, categoryData);
      return response.data;
    } catch (error) {
      console.error('Error creating customer feedback category:', error);
      throw error;
    }
  },

  // Update an existing customer feedback category
  update: async (id, categoryData) => {
    try {
      const response = await instance.patch(ENDPOINTS.CUSTOMER_FEEDBACK_CATEGORY_BY_ID(id), categoryData);
      return response.data;
    } catch (error) {
      console.error('Error updating customer feedback category:', error);
      throw error;
    }
  },

  // Delete a customer feedback category
  delete: async (id) => {
    try {
      const response = await instance.delete(ENDPOINTS.CUSTOMER_FEEDBACK_CATEGORY_BY_ID(id));
      return response.status === 204;
    } catch (error) {
      console.error('Error deleting customer feedback category:', error);
      throw error;
    }
  },

  // Get a single customer feedback category by ID
  getById: async (id) => {
    try {
      const response = await instance.get(ENDPOINTS.CUSTOMER_FEEDBACK_CATEGORY_BY_ID(id));
      return response.data;
    } catch (error) {
      console.error('Error fetching customer feedback category by ID:', error);
      throw error;
    }
  },
};

// Data formatter functions
export const formatCategoryData = (category) => {
  return {
    id: category.id,
    name: category.name,
    description: category.description || 'N/A',
    addedOnDate: category.addedOnDate,
    addedBy: category.addedBy || 'N/A',
    // Format date for display
    formattedDate: formatDateTime(category.addedOnDate),
  };
};

export const formatDateTime = (dateString) => {
  if (!dateString) return 'N/A';

  try {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    });
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid Date';
  }
};

export const formatCategoriesResponse = (response) => {
  if (!response || !response.data) {
    return [];
  }

  return response.data.map(formatCategoryData);
};

export default customerFeedbackCategoriesService;
