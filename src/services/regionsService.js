import instance from "../axios/instance";
import { toast } from "react-toastify";
import {formatDateTime} from '../utils/formatters.js'

export const regionsService = {
  /**
   * Get all regions
   * @returns {Promise<Array>} Array of regions
   */
  getAll: async () => {
    try {
      console.log("=== REGIONS SERVICE: Fetching all regions ===");
      const response = await instance.get("/regions");
      const responseData = response.data;

      console.log("Raw API response:", responseData);

      // Handle the backend response format: { data: [...], meta: {...} }
      let regions = [];
      if (responseData && Array.isArray(responseData.data)) {
        regions = responseData.data;
      } else if (Array.isArray(responseData)) {
        regions = responseData;
      } else {
        console.warn("API response is not in expected format:", responseData);
        return [];
      }

      // Map backend fields to frontend expected format
      const mappedRegions = regions.map(region => ({
        id: region.id,
        name: region.name,
        addedBy: "System", // Backend doesn't provide this field yet
        addedOn: formatDateTime(region.created_at),
        branchCount: region.branchCount || 0,
        created_at: region.created_at,
        updated_at: region.updated_at
      }));

      console.log("Mapped regions:", mappedRegions);
      return mappedRegions;
    } catch (error) {
      console.error("Error fetching regions:", error);
      
      // Return mock data if API is not available (for development)
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        console.warn("API not available, returning mock data");
        return [
          {
            id: "1",
            name: "NYANZA",
            addedBy: "RENOIR",
            addedOn: "16 Jul 2025",
            branchCount: 5
          },
          {
            id: "2",
            name: "CENTRAL",
            addedBy: "ADMIN",
            addedOn: "15 Jul 2025",
            branchCount: 8
          },
          {
            id: "3",
            name: "EASTERN",
            addedBy: "USER",
            addedOn: "14 Jul 2025",
            branchCount: 6
          },
          {
            id: "4",
            name: "WESTERN",
            addedBy: "ADMIN",
            addedOn: "13 Jul 2025",
            branchCount: 7
          },
          {
            id: "5",
            name: "NORTHERN",
            addedBy: "SYSTEM",
            addedOn: "12 Jul 2025",
            branchCount: 4
          },
          {
            id: "6",
            name: "COAST",
            addedBy: "ADMIN",
            addedOn: "11 Jul 2025",
            branchCount: 5
          },
          {
            id: "7",
            name: "RIFT VALLEY",
            addedBy: "SYSTEM",
            addedOn: "10 Jul 2025",
            branchCount: 9
          }
        ];
      }
      throw error;
    }
  },

  /**
   * Get region by ID
   * @param {string} id - Region ID
   * @returns {Promise<Object>} Region object
   */
  getById: async (id) => {
    try {
      console.log(`=== REGIONS SERVICE: Fetching region by ID: ${id} ===`);
      const response = await instance.get(`/regions/${id}`);
      
      const region = response.data;
      console.log("Region fetched:", region);
      
      // Map backend response to frontend format
      return {
        id: region.id,
        name: region.name,
        addedBy: "System",
        addedOn: formatDateTime(region.created_at),
        branchCount: region.branchCount || 0,
        created_at: region.created_at,
        updated_at: region.updated_at
      };
    } catch (error) {
      console.error(`Error fetching region ${id}:`, error);
      throw error;
    }
  },

  /**
   * Create new region
   * @param {Object} regionData - Region data to create
   * @returns {Promise<Object>} Created region object
   */
  create: async (regionData) => {
    try {
      console.log("=== REGIONS SERVICE: Creating new region ===");
      console.log("Region data:", regionData);
      
      const response = await instance.post("/regions", regionData);
      const createdRegion = response.data;
      
      console.log("Region created:", createdRegion);
      toast.success("Region created successfully!");

      // Map backend response to frontend format
      return {
        id: createdRegion.id,
        name: createdRegion.name,
        addedBy: "System",
        addedOn: formatDateTime(createdRegion.created_at),
        branchCount: createdRegion.branchCount || 0,
        created_at: createdRegion.created_at,
        updated_at: createdRegion.updated_at
      };
    } catch (error) {
      console.error("Error creating region:", error);
      
      // Return mock response if API is not available
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        console.warn("API not available, simulating create");
        const mockResponse = {
          id: Date.now().toString(),
          ...regionData,
          addedBy: "CURRENT_USER",
          addedOn: formatDateTime(new Date().toISOString()),
          branchCount: 0
        };
        toast.success("Region created successfully! (Mock)");
        return mockResponse;
      }
      
      // Handle validation errors
      if (error.response?.status === 400) {
        const errorMessage = error.response.data?.message || "Invalid region data";
        toast.error(errorMessage);
      } else if (error.response?.status === 409) {
        toast.error("Region with this name already exists");
      } else {
        toast.error("Failed to create region. Please try again.");
      }
      
      throw error;
    }
  },

  /**
   * Update region
   * @param {string} id - Region ID
   * @param {Object} regionData - Updated region data
   * @returns {Promise<Object>} Updated region object
   */
  update: async (id, regionData) => {
    try {
      console.log(`=== REGIONS SERVICE: Updating region ${id} ===`);
      console.log("Update data:", regionData);
      
      const response = await instance.patch(`/regions/${id}`, regionData);
      const updatedRegion = response.data;
      
      console.log("Region updated:", updatedRegion);
      toast.success("Region updated successfully!");

      // Map backend response to frontend format
      return {
        id: updatedRegion.id,
        name: updatedRegion.name,
        addedBy: "System",
        addedOn: formatDateTime(updatedRegion.created_at),
        branchCount: updatedRegion.branchCount || 0,
        created_at: updatedRegion.created_at,
        updated_at: updatedRegion.updated_at
      };
    } catch (error) {
      console.error(`Error updating region ${id}:`, error);
      
      // Return mock response if API is not available
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        console.warn("API not available, simulating update");
        const mockResponse = {
          id,
          ...regionData,
          addedBy: "CURRENT_USER",
          addedOn: formatDateTime(new Date().toISOString()),
          branchCount: 0
        };
        toast.success("Region updated successfully! (Mock)");
        return mockResponse;
      }
      
      // Handle validation errors
      if (error.response?.status === 400) {
        const errorMessage = error.response.data?.message || "Invalid region data";
        toast.error(errorMessage);
      } else if (error.response?.status === 404) {
        toast.error("Region not found");
      } else if (error.response?.status === 409) {
        toast.error("Region with this name already exists");
      } else {
        toast.error("Failed to update region. Please try again.");
      }
      
      throw error;
    }
  },

  /**
   * Delete region
   * @param {string} id - Region ID
   * @returns {Promise<Object>} Success response
   */
  delete: async (id) => {
    try {
      console.log(`=== REGIONS SERVICE: Deleting region ${id} ===`);
      
      const response = await instance.delete(`/regions/${id}`);
      
      console.log("Region deleted successfully");
      toast.success("Region deleted successfully!");
      
      return response.data;
    } catch (error) {
      console.error(`Error deleting region ${id}:`, error);
      
      // Return mock response if API is not available
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        console.warn("API not available, simulating delete");
        toast.success("Region deleted successfully! (Mock)");
        return { success: true };
      }
      
      // Handle specific errors
      if (error.response?.status === 404) {
        toast.error("Region not found");
      } else if (error.response?.status === 409) {
        toast.error("Cannot delete region. It may have associated branches.");
      } else {
        toast.error("Failed to delete region. Please try again.");
      }
      
      throw error;
    }
  },

  /**
   * Get regions with their branches
   * @returns {Promise<Array>} Array of regions with branches
   */
  getWithBranches: async () => {
    try {
      console.log("=== REGIONS SERVICE: Fetching regions with branches ===");
      const response = await instance.get("/regions?include=branches");
      
      const responseData = response.data;
      let regions = [];
      
      if (responseData && Array.isArray(responseData.data)) {
        regions = responseData.data;
      } else if (Array.isArray(responseData)) {
        regions = responseData;
      }
      
      console.log("Regions with branches:", regions);
      return regions;
    } catch (error) {
      console.error("Error fetching regions with branches:", error);
      throw error;
    }
  }
};

export default regionsService;
