import instance from "../axios/instance";
import { toast } from "react-toastify";

export const bankerDashboardService = {
  /**
   * Set user context for API calls
   * @param {string} userId - User UUID
   * @param {string} branchId - Branch UUID (optional)
   */
  setUserContext: (userId, branchId = null) => {
    if (userId) {
      localStorage.setItem('user_id', userId);
    }
    if (branchId) {
      localStorage.setItem('branch_id', branchId);
    }
    console.log("User context set:", { userId, branchId });
  },

  /**
   * Get current user context
   * @returns {Object} Current user context
   */
  getUserContext: () => {
    return {
      userId: localStorage.getItem('user_id') || localStorage.getItem('userId'),
      branchId: localStorage.getItem('branch_id') || localStorage.getItem('branchId')
    };
  },
  /**
   * Get banker dashboard analytics data
   * @param {Object} filters - Optional filters for the dashboard data
   * @returns {Promise<Object>} Dashboard analytics data
   */
  getDashboardAnalytics: async (filters = {}) => {
    try {
      console.log("=== BANKER DASHBOARD SERVICE: Fetching analytics ===");
      console.log("Filters:", filters);

      const params = {};

      // The JWT token contains user information, so we don't need to pass user_id
      // Only add optional filters if provided
      if (filters.branch_id) {
        params.branch_id = filters.branch_id;
      }
      if (filters.start_date) {
        params.start_date = filters.start_date;
      }
      if (filters.end_date) {
        params.end_date = filters.end_date;
      }

      console.log("API params:", params);

      const response = await instance.get('/leads/rbac-analytics', { params });
      const apiData = response.data;
      
      console.log("Raw API response:", apiData);
      
      // Transform API response to match dashboard data structure
      const transformedData = transformApiDataToDashboard(apiData);
      
      console.log("Transformed dashboard data:", transformedData);
      return transformedData;
    } catch (error) {
      console.error("Error fetching banker dashboard analytics:", error);
      
      // Return mock data if API is not available (for development)
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        console.warn("API not available, returning mock data");
        return getMockDashboardData();
      }
      
      // Handle specific errors
      if (error.response?.status === 400) {
        const errorMessage = error.response?.data?.message || "Bad request";
        console.error("400 Bad Request:", errorMessage);
        toast.error(`Invalid request: ${errorMessage}`);
      } else if (error.response?.status === 401) {
        console.error("401 Unauthorized - JWT token may be missing or invalid");
        toast.error("Session expired. Please login again.");
        // Could trigger logout here if needed
      } else if (error.response?.status === 403) {
        console.error("403 Forbidden - User lacks required permissions");
        toast.error("You don't have permission to view this data.");
      } else if (error.response?.status === 404) {
        toast.error("Dashboard data not found.");
      } else {
        toast.error("Failed to load dashboard data. Please try again.");
      }
      
      throw error;
    }
  },

  /**
   * Get lead status data for pie chart
   * @param {Object} filters - Optional filters
   * @returns {Promise<Object>} Lead status data
   */
  getLeadStatusData: async (filters = {}) => {
    try {
      console.log("=== BANKER DASHBOARD SERVICE: Fetching lead status data ===");
      
      const response = await instance.get('/leads/rbac-analytics', { params: filters });
      const apiData = response.data;
      
      // Extract lead status data from API response
      const leadStatusData = {
        leadsByCategory: {
          pending: apiData.leads_by_status?.pending || 0,
          warm: apiData.leads_by_status?.warm || 0,
          hot: apiData.leads_by_status?.hot || 0,
          cold: apiData.leads_by_status?.cold || 0
        }
      };
      
      console.log("Lead status data:", leadStatusData);
      return leadStatusData;
    } catch (error) {
      console.error("Error fetching lead status data:", error);
      throw error;
    }
  },

  /**
   * Get hitlist progress data for donut chart
   * @param {Object} filters - Optional filters
   * @returns {Promise<Object>} Hitlist progress data
   */
  getHitlistProgressData: async (filters = {}) => {
    try {
      console.log("=== BANKER DASHBOARD SERVICE: Fetching hitlist progress data ===");
      
      const response = await instance.get('/leads/rbac-analytics', { params: filters });
      const apiData = response.data;
      
      // Calculate hitlist progress from API data
      const totalLeads = apiData.total_leads || 0;
      const contactedLeads = apiData.contacted_leads || 0;
      const remainingLeads = totalLeads - contactedLeads;
      
      const hitlistProgressData = {
        hitlistProgress: {
          total: totalLeads,
          contacted: contactedLeads,
          remaining: remainingLeads
        }
      };
      
      console.log("Hitlist progress data:", hitlistProgressData);
      return hitlistProgressData;
    } catch (error) {
      console.error("Error fetching hitlist progress data:", error);
      throw error;
    }
  },

  /**
   * Test API connection with sample UUID
   * @param {string} testUserId - Test user UUID
   * @param {string} testBranchId - Test branch UUID (optional)
   * @returns {Promise<Object>} Test result
   */
  testApiConnection: async (testUserId, testBranchId = null) => {
    try {
      console.log("=== TESTING API CONNECTION ===");
      console.log("Test User ID:", testUserId);
      console.log("Test Branch ID:", testBranchId);

      const params = { user_id: testUserId };
      if (testBranchId) {
        params.branch_id = testBranchId;
      }

      const response = await instance.get('/leads/rbac-analytics', { params });
      console.log("API test successful:", response.data);
      return { success: true, data: response.data };
    } catch (error) {
      console.error("API test failed:", error);
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        status: error.response?.status
      };
    }
  },

  /**
   * Get activity status data for banker dashboard
   * @param {Object} params - Query parameters for filtering
   * @param {string} params.status - Filter by status (upcoming, overdue, scheduled)
   * @param {string} params.interaction_type - Filter by interaction type (call, visit)
   * @param {number} params.page - Page number for pagination
   * @param {number} params.limit - Number of items per page
   * @returns {Promise} API response with activities data
   */
  getActivityStatusData: async (params = {}) => {
    try {
      console.log('=== BANKER DASHBOARD SERVICE: Fetching activity status ===');
      console.log('Request params:', params);

      const response = await instance.get('/banker-dashboard/activity-status', {
        params
      });

      console.log('Activity status response:', response.data);
      console.log('Total activities:', response.data.total);
      console.log('=======================================');

      return response.data;
    } catch (error) {
      console.error('Error fetching activity status data:', error);

      // Handle different error types
      if (error.response?.status === 404) {
        throw new Error('Activity status endpoint not found.');
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to view activity status.');
      } else if (error.response?.status >= 500) {
        throw new Error('Server error while fetching activity status. Please try again later.');
      } else {
        throw new Error(error.response?.data?.message || 'Failed to fetch activity status.');
      }
    }
  }
};

/**
 * Transform API response data to match dashboard data structure
 * @param {Object} apiData - Raw API response data
 * @returns {Object} Transformed dashboard data
 */
const transformApiDataToDashboard = (apiData) => {
  // Extract data from API response
  const totalLeads = apiData.total_leads || 0;
  const contactedLeads = apiData.contacted_leads || 0;
  const userActivity = apiData.user_activity_mtd || {};
  const leadsStatus = apiData.leads_by_status || {};
  
  // Calculate remaining leads
  const remainingLeads = totalLeads - contactedLeads;
  
  // Transform to dashboard structure
  return {
    // Main metrics from API
    hitlistSize: totalLeads, // total_leads maps to hitlistSize
    contacted: contactedLeads,
    
    // Activity data from API
    calls: {
      made: userActivity.total_calls || 0,
      target: 100 // Mock target - to be replaced when API provides this
    },
    visits: {
      made: userActivity.total_visits || 0,
      target: 80 // Mock target - to be replaced when API provides this
    },
    
    // Lead status data for pie chart (using API status categories)
    leadsByCategory: {
      pending: leadsStatus.pending || 0,
      warm: leadsStatus.warm || 0,
      hot: leadsStatus.hot || 0,
      cold: leadsStatus.cold || 0
    },
    
    // Hitlist progress data for donut chart
    hitlistProgress: {
      total: totalLeads,
      contacted: contactedLeads,
      remaining: remainingLeads
    },
    
    // Additional metadata from API
    monthYear: userActivity.month_year || new Date().toISOString().slice(0, 7),
    userPermissions: apiData.user_permissions || {},
    filtersApplied: apiData.filters_applied || {}
  };
};

/**
 * Get mock dashboard data for development/fallback
 * @returns {Object} Mock dashboard data
 */
const getMockDashboardData = () => {
  return {
    hitlistSize: 150,
    calls: { made: 25, target: 100 },
    visits: { made: 12, target: 80 },
    contacted: 85,
    
    // Lead Status Data (using API status categories)
    leadsByCategory: {
      pending: 45,
      warm: 30,
      hot: 25,
      cold: 50
    },
    
    // Hitlist Progress Data
    hitlistProgress: {
      total: 150,
      contacted: 85,
      remaining: 65
    },
    
    // Additional metadata
    monthYear: "2025-08",
    userPermissions: {
      can_view_all_leads: true,
      applied_filter: "all_leads"
    },
    filtersApplied: {}
  };
};

export default bankerDashboardService;
