import instance from "../axios/instance";
import { toast } from "react-toastify";

export const financeAnalystService = {
  /**
   * Get lead statistics data for Finance Analyst Dashboard KPI cards
   * @returns {Promise<Object>} Lead statistics data
   */
  getLeadStatistics: async () => {
    try {
      console.log("=== FINANCE ANALYST SERVICE: Fetching lead statistics ===");

      const response = await instance.get('/finance-analyst-dashboard/lead-statistics');
      const apiData = response.data;

      console.log("Lead statistics API response:", apiData);

      // Transform API response to match dashboard structure
      const transformedData = {
        totalLeads: {
          mtd: apiData.statistics.leads_generated_mtd,
          ytd: 8934, // Mock data - to be replaced when API provides this
          conversionRate: `${apiData.statistics.conversion_rate}%`,
          convertedLeadsMtd: apiData.statistics.converted_leads_mtd,
        },
        hitlistSize: apiData.statistics.hitlist_size
      };

      console.log("Transformed lead statistics data:", transformedData);
      return transformedData;
    } catch (error) {
      console.error("Error fetching lead statistics data:", error);

      // Return mock data if API is not available (for development)
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        console.warn("API not available, returning mock lead statistics data");
        return getMockLeadStatisticsData();
      }

      // For other errors, show toast and return mock data
      if (error.response?.status === 404) {
        toast.error("Lead statistics endpoint not found");
      } else if (error.response?.status === 500) {
        toast.error("Server error while fetching lead statistics data");
      } else {
        toast.error("Failed to load lead statistics data. Please try again.");
      }

      return getMockLeadStatisticsData();
    }
  },

  /**
   * Get monthly conversion rates data for Finance Analyst Dashboard
   * @param {number} year - Year filter (optional, defaults to current year)
   * @returns {Promise<Object>} Monthly conversion rates data
   */
  getMonthlyConversionRates: async (year = null) => {
    try {
      console.log("=== FINANCE ANALYST SERVICE: Fetching monthly conversion rates ===");
      console.log("Year filter:", year);

      const params = {};
      if (year) {
        params.year = year;
      }

      const response = await instance.get('/finance-analyst-dashboard/monthly-conversion-rates', { params });
      const apiData = response.data;

      console.log("Monthly conversion rates API response:", apiData);

      // Transform API response to match dashboard structure
      const transformedData = {
        year: apiData.year,
        monthlyData: apiData.monthly_conversion_rates.map(item => ({
          month: item.month,
          monthNumber: item.month_number,
          leadsCreated: item.leads_created,
          leadsConverted: item.leads_converted,
          conversionRate: item.conversion_rate
        })),
        // Extract data for chart
        categories: apiData.monthly_conversion_rates.map(item => item.month.substring(0, 3)), // Short month names
        conversionRates: apiData.monthly_conversion_rates.map(item => item.conversion_rate)
      };

      console.log("Transformed monthly conversion rates data:", transformedData);
      return transformedData;
    } catch (error) {
      console.error("Error fetching monthly conversion rates data:", error);

      // Return mock data if API is not available (for development)
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        console.warn("API not available, returning mock monthly conversion rates data");
        return getMockMonthlyConversionRatesData(year);
      }

      // For other errors, show toast and return mock data
      if (error.response?.status === 404) {
        toast.error("Monthly conversion rates endpoint not found");
      } else if (error.response?.status === 500) {
        toast.error("Server error while fetching monthly conversion rates data");
      } else {
        toast.error("Failed to load monthly conversion rates data. Please try again.");
      }

      return getMockMonthlyConversionRatesData(year);
    }
  },

  /**
   * Get leads by region data for Finance Analyst Dashboard
   * @returns {Promise<Object>} Leads by region data
   */
  getLeadsByRegion: async () => {
    try {
      console.log("=== FINANCE ANALYST SERVICE: Fetching leads by region ===");

      const response = await instance.get('/finance-analyst-dashboard/leads-by-region');
      const apiData = response.data;

      console.log("Leads by region API response:", apiData);

      // Transform API response to match dashboard structure
      const transformedData = {
        regions: apiData.leads_by_region.map(item => ({
          name: item.region_name.replace(' Region', ''), // Remove 'Region' suffix
          fullName: item.region_name,
          totalLeads: item.total_leads,
          convertedLeads: item.converted_leads,
          conversionRate: item.conversion_rate
        })),
        // Extract data for chart
        categories: apiData.leads_by_region.map(item => item.region_name.replace(' Region', '')),
        totalLeadsData: apiData.leads_by_region.map(item => item.total_leads),
        convertedLeadsData: apiData.leads_by_region.map(item => item.converted_leads)
      };

      console.log("Transformed leads by region data:", transformedData);
      return transformedData;
    } catch (error) {
      console.error("Error fetching leads by region data:", error);

      // Return mock data if API is not available (for development)
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        console.warn("API not available, returning mock leads by region data");
        return getMockLeadsByRegionData();
      }

      // For other errors, show toast and return mock data
      if (error.response?.status === 404) {
        toast.error("Leads by region endpoint not found");
      } else if (error.response?.status === 500) {
        toast.error("Server error while fetching leads by region data");
      } else {
        toast.error("Failed to load leads by region data. Please try again.");
      }

      return getMockLeadsByRegionData();
    }
  },

  /**
   * Get leads by branch data for Finance Analyst Dashboard
   * @param {string} region - Region filter (defaults to "Nairobi Region")
   * @returns {Promise<Object>} Leads by branch data
   */
  getLeadsByBranch: async (region = "Nairobi Region") => {
    try {
      console.log("=== FINANCE ANALYST SERVICE: Fetching leads by branch ===");
      console.log("Region filter:", region);

      const params = { region };
      const response = await instance.get('/finance-analyst-dashboard/leads-by-branch', { params });
      const apiData = response.data;

      console.log("Leads by branch API response:", apiData);

      // Transform API response to match dashboard structure
      const transformedData = {
        regionName: apiData.region_name,
        branches: apiData.leads_by_branch.map(item => ({
          name: item.branch_name.trim(), // Remove leading/trailing spaces
          totalLeads: item.total_leads,
          convertedLeads: item.converted_leads,
          conversionRate: item.conversion_rate
        })),
        // Extract data for chart
        categories: apiData.leads_by_branch.map(item => item.branch_name.trim()),
        totalLeadsData: apiData.leads_by_branch.map(item => item.total_leads),
        convertedLeadsData: apiData.leads_by_branch.map(item => item.converted_leads)
      };

      console.log("Transformed leads by branch data:", transformedData);
      return transformedData;
    } catch (error) {
      console.error("Error fetching leads by branch data:", error);

      // Return mock data if API is not available (for development)
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        console.warn("API not available, returning mock leads by branch data");
        return getMockLeadsByBranchData(region);
      }

      // For other errors, show toast and return mock data
      if (error.response?.status === 404) {
        toast.error("Leads by branch endpoint not found");
      } else if (error.response?.status === 500) {
        toast.error("Server error while fetching leads by branch data");
      } else {
        toast.error("Failed to load leads by branch data. Please try again.");
      }

      return getMockLeadsByBranchData(region);
    }
  },

  /**
   * Get regions data for filter options
   * @returns {Promise<Object>} Regions data
   */
  getRegions: async () => {
    try {
      console.log("=== FINANCE ANALYST SERVICE: Fetching regions ===");

      const response = await instance.get('/regions');
      const apiData = response.data;

      console.log("Regions API response:", apiData);

      // Transform API response to match dashboard structure
      const transformedData = {
        regions: apiData.data.map(item => ({
          id: item.id,
          name: item.name,
          branchCount: item.branchCount
        }))
      };

      console.log("Transformed regions data:", transformedData);
      return transformedData;
    } catch (error) {
      console.error("Error fetching regions data:", error);

      // Return mock data if API is not available (for development)
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        console.warn("API not available, returning mock regions data");
        return getMockRegionsData();
      }

      // For other errors, show toast and return mock data
      if (error.response?.status === 404) {
        toast.error("Regions endpoint not found");
      } else if (error.response?.status === 500) {
        toast.error("Server error while fetching regions data");
      } else {
        toast.error("Failed to load regions data. Please try again.");
      }

      return getMockRegionsData();
    }
  },

  /**
   * Get leads grouped data for Finance Analyst Dashboard charts
   * @returns {Promise<Object>} Leads grouped data (by category and status)
   */
  getLeadsGrouped: async () => {
    try {
      console.log("=== FINANCE ANALYST SERVICE: Fetching leads grouped data ===");

      const response = await instance.get('/finance-analyst-dashboard/leads-grouped');
      const apiData = response.data;

      console.log("Leads grouped API response:", apiData);

      // Transform API response to match dashboard structure
      const transformedData = {
        leadsByCategory: {},
        leadsByStatus: {}
      };

      // Transform leads by category
      apiData.leads_by_category.forEach(item => {
        const categoryKey = item.category_name.toLowerCase();
        transformedData.leadsByCategory[categoryKey] = item.lead_count;
      });

      // Transform leads by status
      apiData.leads_by_status.forEach(item => {
        const statusKey = item.status.toLowerCase();
        transformedData.leadsByStatus[statusKey] = item.lead_count;
      });

      console.log("Transformed leads grouped data:", transformedData);
      return transformedData;
    } catch (error) {
      console.error("Error fetching leads grouped data:", error);

      // Return mock data if API is not available (for development)
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        console.warn("API not available, returning mock leads grouped data");
        return getMockLeadsGroupedData();
      }

      // For other errors, show toast and return mock data
      if (error.response?.status === 404) {
        toast.error("Leads grouped endpoint not found");
      } else if (error.response?.status === 500) {
        toast.error("Server error while fetching leads grouped data");
      } else {
        toast.error("Failed to load leads grouped data. Please try again.");
      }

      return getMockLeadsGroupedData();
    }
  },

  /**
   * Get call and visit statistics data for Finance Analyst Dashboard
   * @returns {Promise<Object>} Call and visit statistics data
   */
  getCallVisitStatistics: async () => {
    try {
      console.log("=== FINANCE ANALYST SERVICE: Fetching call visit statistics ===");

      const response = await instance.get('/finance-analyst-dashboard/call-visit-statistics');
      const apiData = response.data;

      console.log("Call visit statistics API response:", apiData);

      // Transform API response to match dashboard structure
      const transformedData = {
        callsVsTarget: {
          made: apiData.statistics.total_calls_mtd,
          target: apiData.statistics.total_call_targets_mtd,
          achievementRate: apiData.statistics.call_achievement_rate
        },
        visitsVsTarget: {
          made: apiData.statistics.total_visits_mtd,
          target: apiData.statistics.total_visit_targets_mtd,
          achievementRate: apiData.statistics.visit_achievement_rate
        }
      };

      console.log("Transformed call visit statistics data:", transformedData);
      return transformedData;
    } catch (error) {
      console.error("Error fetching call visit statistics data:", error);

      // Return mock data if API is not available (for development)
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        console.warn("API not available, returning mock call visit statistics data");
        return getMockCallVisitStatisticsData();
      }

      // For other errors, show toast and return mock data
      if (error.response?.status === 404) {
        toast.error("Call visit statistics endpoint not found");
      } else if (error.response?.status === 500) {
        toast.error("Server error while fetching call visit statistics data");
      } else {
        toast.error("Failed to load call visit statistics data. Please try again.");
      }

      return getMockCallVisitStatisticsData();
    }
  },

  /**
   * Get top anchors data for Finance Analyst Dashboard
   * @returns {Promise<Object>} Top anchors data
   */
  getTopAnchors: async () => {
    try {
      console.log("=== FINANCE ANALYST SERVICE: Fetching top anchors ===");

      const response = await instance.get('/finance-analyst-dashboard/top-anchors');
      const apiData = response.data;

      console.log("Top anchors API response:", apiData);

      // Transform API response to match dashboard structure
      const transformedData = {
        topAnchors: apiData.topAnchors.map(anchor => ({
          name: anchor.name,
          leads: anchor.lead_count
        }))
      };

      console.log("Transformed top anchors data:", transformedData);
      return transformedData;
    } catch (error) {
      console.error("Error fetching top anchors data:", error);

      // Return mock data if API is not available (for development)
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        console.warn("API not available, returning mock top anchors data");
        return getMockTopAnchorsData();
      }

      // For other errors, show toast and return mock data
      if (error.response?.status === 404) {
        toast.error("Top anchors endpoint not found");
      } else if (error.response?.status === 500) {
        toast.error("Server error while fetching top anchors data");
      } else {
        toast.error("Failed to load top anchors data. Please try again.");
      }

      return getMockTopAnchorsData();
    }
  },

  /**
   * Get regional hitlist data for Finance Analyst Dashboard
   * @param {Object} filters - Optional filters for the data
   * @returns {Promise<Object>} Regional hitlist data
   */
  getRegionalHitlistData: async (filters = {}) => {
    try {
      console.log("=== FINANCE ANALYST SERVICE: Fetching regional hitlist data ===");
      console.log("Filters:", filters);
      
      const response = await instance.get('/dashboard/finance-analyst/regional-hitlist', { params: filters });
      return response.data;
    } catch (error) {
      console.error('Error fetching regional hitlist data:', error);
      throw error;
    }
  },

  /**
   * Get department hitlist data for Finance Analyst Dashboard
   * @param {string} department - Department filter ('all' for all departments)
   * @returns {Promise<Object>} Department hitlist data
   */
  getDepartmentHitlistData: async (department = 'all') => {
    try {
      console.log("=== FINANCE ANALYST SERVICE: Fetching department hitlist data ===");
      console.log("Department:", department);
      
      const params = department !== 'all' ? { department } : {};
      const response = await instance.get('/dashboard/finance-analyst/department-hitlist', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching department hitlist data:', error);
      throw error;
    }
  },

  /**
   * Get finance analytics data for Finance Analyst Dashboard
   * @returns {Promise<Object>} Finance analytics data
   */
  getFinanceAnalyticsData: async () => {
    try {
      console.log("=== FINANCE ANALYST SERVICE: Fetching finance analytics data ===");
      
      const response = await instance.get('/dashboard/finance-analyst/analytics');
      return response.data;
    } catch (error) {
      console.error('Error fetching finance analytics data:', error);
      throw error;
    }
  },

  /**
   * Get converted leads data for Finance Analyst Dashboard
   * @param {string} department - Department filter ('all' for all departments)
   * @returns {Promise<Object>} Converted leads data
   */
  getConvertedLeadsData: async (department = 'all') => {
    try {
      console.log("=== FINANCE ANALYST SERVICE: Fetching converted leads data ===");
      console.log("Department:", department);
      
      const params = department !== 'all' ? { department } : {};
      const response = await instance.get('/dashboard/finance-analyst/converted-leads', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching converted leads data:', error);
      throw error;
    }
  },

  /**
   * Get branches hitlist data for Finance Analyst Dashboard
   * @param {string} region - Region filter
   * @param {string} department - Department filter ('all' for all departments)
   * @returns {Promise<Object>} Branches hitlist data
   */
  getBranchesHitlistData: async (region, department = 'all') => {
    try {
      console.log("=== FINANCE ANALYST SERVICE: Fetching branches hitlist data ===");
      console.log("Region:", region, "Department:", department);
      
      const params = { region };
      if (department !== 'all') {
        params.department = department;
      }
      const response = await instance.get('/dashboard/finance-analyst/branches-hitlist', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching branches hitlist data:', error);
      throw error;
    }
  },

  /**
   * Get branches converted leads data for Finance Analyst Dashboard
   * @param {string} region - Region filter
   * @param {string} department - Department filter ('all' for all departments)
   * @returns {Promise<Object>} Branches converted leads data
   */
  getBranchesConvertedLeadsData: async (region, department = 'all') => {
    try {
      console.log("=== FINANCE ANALYST SERVICE: Fetching branches converted leads data ===");
      console.log("Region:", region, "Department:", department);
      
      const params = { region };
      if (department !== 'all') {
        params.department = department;
      }
      const response = await instance.get('/dashboard/finance-analyst/branches-converted-leads', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching branches converted leads data:', error);
      throw error;
    }
  }
};

/**
 * Get mock lead statistics data for development/fallback
 * @returns {Object} Mock lead statistics data
 */
const getMockLeadStatisticsData = () => {
  return {
    totalLeads: {
      mtd: 1247,
      ytd: 8934,
      conversionRate: "23.5%",
      convertedLeadsMtd: 456,
    },
    hitlistSize: 3456
  };
};

/**
 * Get mock monthly conversion rates data for development/fallback
 * @param {number} year - Year for mock data
 * @returns {Object} Mock monthly conversion rates data
 */
const getMockMonthlyConversionRatesData = (year = null) => {
  const currentYear = year || new Date().getFullYear();
  const currentMonth = new Date().getMonth() + 1; // 1-based month

  const months = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  // Generate mock data up to current month for current year, full year for past years
  const endMonth = currentYear === new Date().getFullYear() ? currentMonth : 12;

  const monthlyData = [];
  const conversionRates = [18.5, 21.2, 19.8, 23.5, 22.1, 24.3, 20.8, 25.1, 22.7, 26.3, 23.9, 27.2];

  for (let i = 0; i < endMonth; i++) {
    monthlyData.push({
      month: months[i],
      monthNumber: i + 1,
      leadsCreated: Math.floor(Math.random() * 100) + 50,
      leadsConverted: Math.floor(Math.random() * 30) + 10,
      conversionRate: conversionRates[i] || 20.0
    });
  }

  return {
    year: currentYear,
    monthlyData,
    categories: monthlyData.map(item => item.month.substring(0, 3)),
    conversionRates: monthlyData.map(item => item.conversionRate)
  };
};

/**
 * Get mock leads by region data for development/fallback
 * @returns {Object} Mock leads by region data
 */
const getMockLeadsByRegionData = () => {
  return {
    regions: [
      { name: "Central", fullName: "Central Region", totalLeads: 456, convertedLeads: 89, conversionRate: 19.5 },
      { name: "Eastern", fullName: "Eastern Region", totalLeads: 298, convertedLeads: 67, conversionRate: 22.5 },
      { name: "Western", fullName: "Western Region", totalLeads: 234, convertedLeads: 54, conversionRate: 23.1 },
      { name: "Northern", fullName: "Northern Region", totalLeads: 189, convertedLeads: 43, conversionRate: 22.8 },
      { name: "Southern", fullName: "Southern Region", totalLeads: 267, convertedLeads: 62, conversionRate: 23.2 },
      { name: "Coast", fullName: "Coast Region", totalLeads: 198, convertedLeads: 38, conversionRate: 19.2 },
      { name: "Rift Valley", fullName: "Rift Valley Region", totalLeads: 289, convertedLeads: 71, conversionRate: 24.6 },
      { name: "Nyanza", fullName: "Nyanza Region", totalLeads: 225, convertedLeads: 48, conversionRate: 21.3 }
    ],
    categories: ["Central", "Eastern", "Western", "Northern", "Southern", "Coast", "Rift Valley", "Nyanza"],
    totalLeadsData: [456, 298, 234, 189, 267, 198, 289, 225],
    convertedLeadsData: [89, 67, 54, 43, 62, 38, 71, 48]
  };
};

/**
 * Get mock leads by branch data for development/fallback
 * @param {string} region - Region for mock data
 * @returns {Object} Mock leads by branch data
 */
const getMockLeadsByBranchData = (region = "Nairobi Region") => {
  const branchData = {
    "Nairobi Region": {
      branches: [
        { name: "Nairobi Main", totalLeads: 114, convertedLeads: 22, conversionRate: 19.3 },
        { name: "Westlands", totalLeads: 132, convertedLeads: 26, conversionRate: 19.7 },
        { name: "Karen", totalLeads: 89, convertedLeads: 17, conversionRate: 19.1 },
        { name: "Thika", totalLeads: 121, convertedLeads: 24, conversionRate: 19.8 }
      ]
    },
    "Central Region": {
      branches: [
        { name: "Kiambu Branch", totalLeads: 58, convertedLeads: 12, conversionRate: 20.7 },
        { name: "Kikuyu Branch", totalLeads: 67, convertedLeads: 14, conversionRate: 20.9 },
        { name: "Nyeri Branch", totalLeads: 45, convertedLeads: 9, conversionRate: 20.0 },
        { name: "Meru Branch", totalLeads: 64, convertedLeads: 10, conversionRate: 15.6 }
      ]
    }
  };

  const selectedData = branchData[region] || branchData["Nairobi Region"];

  return {
    regionName: region,
    branches: selectedData.branches,
    categories: selectedData.branches.map(b => b.name),
    totalLeadsData: selectedData.branches.map(b => b.totalLeads),
    convertedLeadsData: selectedData.branches.map(b => b.convertedLeads)
  };
};

/**
 * Get mock regions data for development/fallback
 * @returns {Object} Mock regions data
 */
const getMockRegionsData = () => {
  return {
    regions: [
      { id: "1", name: "Central Region", branchCount: 6 },
      { id: "2", name: "Coastal Region", branchCount: 2 },
      { id: "3", name: "Eastern Region", branchCount: 2 },
      { id: "4", name: "Nairobi Region", branchCount: 7 },
      { id: "5", name: "North Eastern Region", branchCount: 0 },
      { id: "6", name: "Nyanza Region", branchCount: 2 },
      { id: "7", name: "Rift Valley Region", branchCount: 4 },
      { id: "8", name: "Western Region", branchCount: 0 }
    ]
  };
};

/**
 * Get mock call visit statistics data for development/fallback
 * @returns {Object} Mock call visit statistics data
 */
const getMockCallVisitStatisticsData = () => {
  return {
    callsVsTarget: {
      made: 1847,
      target: 2100,
      achievementRate: 87.9
    },
    visitsVsTarget: {
      made: 567,
      target: 650,
      achievementRate: 87.2
    }
  };
};

/**
 * Get mock leads grouped data for development/fallback
 * @returns {Object} Mock leads grouped data
 */
const getMockLeadsGroupedData = () => {
  return {
    leadsByCategory: {
      employed: 456,
      corporate: 234,
      trader: 557,
      individual: 300
    },
    leadsByStatus: {
      warm: 2847,
      hot: 453,
      pending: 3300,
      cold: 3300
    }
  };
};

/**
 * Get mock top anchors data for development/fallback
 * @returns {Object} Mock top anchors data
 */
const getMockTopAnchorsData = () => {
  return {
    topAnchors: [
      { name: "ABC Manufacturing", leads: 89 },
      { name: "Tech Solutions Ltd", leads: 67 },
      { name: "Global Enterprises", leads: 54 }
    ]
  };
};

export default financeAnalystService;
