import instance from "../axios/instance";

/**
 * Service for SegmentHead Dashboard API calls
 */

/**
 * Get activities data for SegmentHead dashboard
 * @param {Object} params - Query parameters for filtering
 * @param {string} params.status - Filter by status (upcoming, overdue, scheduled)
 * @param {string} params.interaction_type - Filter by interaction type (call, visit)
 * @param {string} params.region - Filter by region name
 * @param {string} params.branch - Filter by branch name
 * @param {string} params.staff_name - Filter by staff name
 * @param {number} params.page - Page number for pagination
 * @param {number} params.limit - Number of items per page
 * @returns {Promise} API response with activities data
 */
export const getActivitiesData = async (params = {}) => {
  try {
    console.log('=== SEGMENT HEAD DASHBOARD SERVICE: Fetching activities ===');
    console.log('Request params:', params);

    const response = await instance.get('/segment-head-dashboard/activity-status', {
      params
    });

    console.log('Activities response:', response.data);
    console.log('Total activities:', response.data.total);
    console.log('=======================================');

    return response.data;
  } catch (error) {
    console.error('Error fetching activities data:', error);

    // Handle different error types
    if (error.response?.status === 404) {
      throw new Error('Activities endpoint not found.');
    } else if (error.response?.status === 403) {
      throw new Error('You do not have permission to view activities.');
    } else if (error.response?.status >= 500) {
      throw new Error('Server error while fetching activities. Please try again later.');
    } else {
      throw new Error(error.response?.data?.message || 'Failed to fetch activities.');
    }
  }
};

/**
 * Get all regions
 * @returns {Promise} API response with regions data
 */
export const getRegions = async () => {
  try {
    const response = await instance.get('/regions');
    return response.data;
  } catch (error) {
    console.error('Error fetching regions:', error);
    throw error;
  }
};

/**
 * Get top performing anchors data
 * @returns {Promise} API response with top anchors data
 */
export const getTopAnchors = async () => {
  try {
    console.log('=== SEGMENT HEAD DASHBOARD SERVICE: Fetching top anchors ===');

    const response = await instance.get('/finance-analyst-dashboard/top-anchors');

    console.log('Top anchors response:', response.data);
    console.log('=======================================');

    return response.data;
  } catch (error) {
    console.error('Error fetching top anchors data:', error);

    // Handle different error types
    if (error.response?.status === 404) {
      throw new Error('Top anchors endpoint not found.');
    } else if (error.response?.status === 403) {
      throw new Error('You do not have permission to view top anchors.');
    } else if (error.response?.status >= 500) {
      throw new Error('Server error while fetching top anchors. Please try again later.');
    } else {
      throw new Error(error.response?.data?.message || 'Failed to fetch top anchors.');
    }
  }
};

/**
 * Get branches by region ID
 * @param {string} regionId - The region ID
 * @returns {Promise} API response with branches data
 */
export const getBranchesByRegion = async (regionId) => {
  try {
    const response = await instance.get(`/regions/${regionId}/branches`);
    return response.data;
  } catch (error) {
    console.error('Error fetching branches by region:', error);
    throw error;
  }
};

/**
 * Get users by branch ID
 * @param {string} branchId - The branch ID
 * @returns {Promise} API response with users data
 */
export const getUsersByBranch = async (branchId) => {
  try {
    const response = await instance.get(`/branches/${branchId}/users`);
    return response.data;
  } catch (error) {
    console.error('Error fetching users by branch:', error);
    throw error;
  }
};

/**
 * Get lead statistics data for Segment Head Dashboard KPI cards
 * @returns {Promise<Object>} Lead statistics data
 */
export async function getLeadStatistics() {
  try {
    console.log("=== SEGMENT HEAD SERVICE: Fetching lead statistics ===");

    const response = await instance.get('/finance-analyst-dashboard/lead-statistics');
    const apiData = response.data;

    console.log("Lead statistics API response:", apiData);

    // Transform API response to match dashboard structure
    const transformedData = {
      totalLeads: {
        mtd: apiData.statistics.leads_generated_mtd,
        ytd: 8934, // Mock data - to be replaced when API provides this
        conversionRate: `${apiData.statistics.conversion_rate}%`,
        convertedLeadsMtd: apiData.statistics.converted_leads_mtd,
      },
      hitlistSize: apiData.statistics.hitlist_size
    };
    
    
    console.log("Transformed lead statistics data:", transformedData);
    return transformedData;
  } catch (error) {
    console.error("Error fetching lead statistics data:", error);
    throw error;
  }
}

/**
 * Get leads grouped data for Segment Head Dashboard charts
 * @returns {Promise<Object>} Leads grouped data (by category and status)
 */
export const getLeadsGrouped = async () => {
  try {
    console.log("=== SEGMENT HEAD SERVICE: Fetching leads grouped data ===");

    const response = await instance.get('/finance-analyst-dashboard/leads-grouped');
    const apiData = response.data;

    console.log("Leads grouped API response:", apiData);

    // Transform API response to match dashboard structure
    const transformedData = {
      leadsByCategory: {},
      leadsByStatus: {}
    };

    // Transform leads by category
    apiData.leads_by_category.forEach(item => {
      const categoryKey = item.category_name.toLowerCase();
      transformedData.leadsByCategory[categoryKey] = item.lead_count;
    });

    // Transform leads by status
    apiData.leads_by_status.forEach(item => {
      const statusKey = item.status.toLowerCase();
      transformedData.leadsByStatus[statusKey] = item.lead_count;
    });

    console.log("Transformed leads grouped data:", transformedData);
    return transformedData;
  } catch (error) {
    console.error("Error fetching leads grouped data:", error);

    // Handle different error types
    if (error.response?.status === 404) {
      throw new Error('Leads grouped endpoint not found.');
    } else if (error.response?.status === 403) {
      throw new Error('You do not have permission to view leads grouped data.');
    } else if (error.response?.status >= 500) {
      throw new Error('Server error while fetching leads grouped data. Please try again later.');
    } else {
      throw new Error(error.response?.data?.message || 'Failed to fetch leads grouped data.');
    }
  }
};



/**
 * Get call and visit statistics data for Segment Head Dashboard
 * @returns {Promise<Object>} Call and visit statistics data
 */
/**
 * Get monthly conversion rates data for Segment Head Dashboard
 * @param {number} year - Year filter (optional, defaults to current year)
 * @returns {Promise<Object>} Monthly conversion rates data
 */
export const getMonthlyConversionRates = async (year = null) => {
  try {
    console.log("=== SEGMENT HEAD SERVICE: Fetching monthly conversion rates ===");
    console.log("Year filter:", year);

    const params = {};
    if (year) {
      params.year = year;
    }

    const response = await instance.get('/finance-analyst-dashboard/monthly-conversion-rates', { params });
    const apiData = response.data;

    console.log("Monthly conversion rates API response:", apiData);

    // Transform API response to match dashboard structure
    const transformedData = {
      year: apiData.year,
      monthlyData: apiData.monthly_conversion_rates.map(item => ({
        month: item.month,
        monthNumber: item.month_number,
        leadsCreated: item.leads_created,
        leadsConverted: item.leads_converted,
        conversionRate: item.conversion_rate
      })),
      // Extract data for chart
      categories: apiData.monthly_conversion_rates.map(item => item.month.substring(0, 3)), // Short month names
      conversionRates: apiData.monthly_conversion_rates.map(item => item.conversion_rate)
    };

    console.log("Transformed monthly conversion rates data:", transformedData);
    return transformedData;
  } catch (error) {
    console.error("Error fetching monthly conversion rates data:", error);
    throw error;
  }
};

export const getCallVisitStatistics = async () => {
  try {
    console.log("=== SEGMENT HEAD SERVICE: Fetching call visit statistics ===");

    const response = await instance.get('/finance-analyst-dashboard/call-visit-statistics');
    const apiData = response.data;

    console.log("Call visit statistics API response:", apiData);

    // Transform API response to match dashboard structure
    const transformedData = {
      callsVsTarget: {
        made: apiData.statistics.total_calls_mtd,
        target: apiData.statistics.total_call_targets_mtd,
        achievementRate: apiData.statistics.call_achievement_rate
      },
      visitsVsTarget: {
        made: apiData.statistics.total_visits_mtd,
        target: apiData.statistics.total_visit_targets_mtd,
        achievementRate: apiData.statistics.visit_achievement_rate
      }
    };

    console.log("Transformed call visit statistics data:", transformedData);
    return transformedData;
  } catch (error) {
    console.error("Error fetching call visit statistics data:", error);

    // Handle different error types
    if (error.response?.status === 404) {
      throw new Error('Call visit statistics endpoint not found.');
    } else if (error.response?.status === 403) {
      throw new Error('You do not have permission to view call visit statistics.');
    } else if (error.response?.status >= 500) {
      throw new Error('Server error while fetching call visit statistics. Please try again later.');
    } else {
      throw new Error(error.response?.data?.message || 'Failed to fetch call visit statistics.');
    }
  }
};

/**
     * Get activity summary data for Segment Head Dashboard
     * @returns {Promise<Object>} Activity summary data
     */
    export const getActivitySummary = async () => {
      try {
        console.log("=== SEGMENT HEAD SERVICE: Fetching activity summary ===");
    
        const response = await instance.get('/segment-head-dashboard/activity-summary');
        const apiData = response.data;
    
        console.log("Activity summary API response:", apiData);
    
        // Transform API response to match dashboard structure
        const transformedData = {
          overdueActivities: apiData.summary.overdue_count,
          upcomingActivities: apiData.summary.upcoming_count,
          retrievedAt: apiData.retrieved_at
        };
    
        console.log("Transformed activity summary data:", transformedData);
        return transformedData;
      } catch (error) {
        console.error("Error fetching activity summary data:", error);
    
        // Handle different error types
        if (error.response?.status === 404) {
          throw new Error('Activity summary endpoint not found.');
        } else if (error.response?.status === 403) {
          throw new Error('You do not have permission to view activity summary.');
        } else if (error.response?.status >= 500) {
          throw new Error('Server error while fetching activity summary. Please try again later.');
        } else {
          throw new Error(error.response?.data?.message || 'Failed to fetch activity summary.');
        }
      }
    };

/**
 * Get activities by customer feedback data for Segment Head Dashboard
 * @returns {Promise<Object>} Activities by customer feedback data
 */
export const getActivitiesByFeedback = async () => {
  try {
    console.log("=== SEGMENT HEAD SERVICE: Fetching activities by feedback ===");

    const response = await instance.get('/segment-head-dashboard/activities-by-feedback');
    const apiData = response.data;

    console.log("Activities by feedback API response:", apiData);

    // Transform API response to match dashboard structure
    const transformedData = {
      activitiesByFeedback: {},
      feedbackTypes: [], // Store the dynamic feedback types
      totalActivities: apiData.total_activities,
      retrievedAt: apiData.retrieved_at
    };

    // Transform activities by feedback
    apiData.activities_by_feedback.forEach(item => {
      // Convert feedback name to lowercase for consistent key naming
      const feedbackKey = item.customer_feedback_name.toLowerCase();
      transformedData.activitiesByFeedback[feedbackKey] = item.activity_count;
    });

    // Store the feedback types for dynamic chart rendering
    transformedData.feedbackTypes = apiData.activities_by_feedback.map(item => ({
      name: item.customer_feedback_name,
      id: item.customer_feedback_id,
      count: item.activity_count
    }));

    console.log("Transformed activities by feedback data:", transformedData);
    return transformedData;
  } catch (error) {
    console.error("Error fetching activities by feedback data:", error);

    // Handle different error types
    if (error.response?.status === 404) {
      throw new Error('Activities by feedback endpoint not found.');
    } else if (error.response?.status === 403) {
      throw new Error('You do not have permission to view activities by feedback.');
    } else if (error.response?.status >= 500) {
      throw new Error('Server error while fetching activities by feedback. Please try again later.');
    } else {
      throw new Error(error.response?.data?.message || 'Failed to fetch activities by feedback.');
    }
  }
};

export default instance;