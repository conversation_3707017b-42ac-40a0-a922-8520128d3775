import instance from "../axios/instance";
import {
  setAccessToken,
  setRefreshToken,
  clearAllAuthData,
  setTempAccessToken,
  setTempRefreshToken,
  setUserEmail,
  removeUserEmail,
  setMFAMethods,
  setUserData
} from "../utils/cookieUtils";

export const authService = {
  login: async (credentials) => {
    try {
      const response = await instance.post("/auth/login", credentials);

      // Store user email in cookies for later use (e.g., MFA verification)
      setUserEmail(credentials.email);

      // Check if user has MFA methods configured
      if (response.data.mfaMethods && response.data.mfaMethods.length > 0) {
        // Store tokens temporarily for MFA flow
        setTempAccessToken(response.data.accessToken);
        setTempRefreshToken(response.data.refreshToken);

        // Store MFA methods and user data for later access
        setMFAMethods(response.data.mfaMethods);
        if (response.data.user) {
          setUserData(response.data.user);
        }

        // Return response with MFA flag
        return {
          ...response.data,
          requiresMFA: true
        };
      } else {
        // No MFA required, store tokens normally
        setAccessToken(response.data.accessToken);
        setRefreshToken(response.data.refreshToken);

        // Store user data
        if (response.data.user) {
          setUserData(response.data.user);
        }

        return {
          ...response.data,
          requiresMFA: false
        };
      }
    } catch (error) {
      console.error("Login error:", error);
      throw error;
    }
  },

  logout: () => {
    // Clear all authentication data from cookies
    clearAllAuthData();
  },

  // Password reset functionality
  requestPasswordReset: async (email) => {
    try {
      const response = await instance.post("/auth/forgot-password", { email });
      return response.data;
    } catch (error) {
      console.error("Request password reset error:", error);
      throw error;
    }
  },

  verifyResetOTP: async (email, otp, token) => {
    try {
      const response = await instance.post("auth/reset-verification", {
        email,
        otp
        // token
      });
      return response.data;
    } catch (error) {
      console.error("Verify reset OTP error:", error);
      throw error;
    }
  },

  resetPassword: async (email, token, password) => {
    try {
      const response = await instance.post("auth/reset-password", {
        email,
        token,
        password
      });
      return response.data;
    } catch (error) {
      console.error("Reset password error:", error);
      throw error;
    }
  },

  resendResetOTP: async (email) => {
    try {
      const response = await instance.post("/auth/resend-reset-otp", { email });
      return response.data;
    } catch (error) {
      console.error("Resend reset OTP error:", error);
      throw error;
    }
  },

  changePassword: async (currentPassword, newPassword) => {
    try {
      const response = await instance.post("/auth/change-password", {
        currentPassword,
        newPassword
      });
      return response.data;
    } catch (error) {
      console.error("Change password error:", error);
      throw error;
    }
  },

  // MFA related methods
  sendMFACode: async (methodId) => {
    try {
      const response = await instance.post("/auth/mfa/send-code", {
        methodId
      });
      return response.data;
    } catch (error) {
      console.error("Send MFA code error:", error);
      throw error;
    }
  },

  verifyMFACode: async (methodId, code) => {
    try {
      const response = await instance.post("/auth/mfa/verify-code", {
        methodId,
        code
      });
      return response.data;
    } catch (error) {
      console.error("Verify MFA code error:", error);
      throw error;
    }
  },

  // Get MFA methods
  getMFAMethods: async () => {
    try {
      const response = await instance.get("/auth/mfa");
      return response.data;
    } catch (error) {
      console.error("Get MFA methods error:", error);
      throw error;
    }
  },

  // Enable/Disable MFA method
  toggleMFAMethod: async (mfaMethodId, enabled) => {
    try {
      const response = await instance.post("/auth/mfa", {
        mfaMethodId,
        enabled
      });
      return response.data;
    } catch (error) {
      console.error("Toggle MFA method error:", error);
      throw error;
    }
  },

  // Add phone number for SMS MFA
  addPhoneNumber: async (phoneNumber, methodId) => {
    try {
      const response = await instance.post("/security/mfa/phone", {
        phoneNumber,
        methodId
      });
      return response.data;
    } catch (error) {
      console.error("Add phone number error:", error);
      throw error;
    }
  },

  // Verify phone number for SMS MFA
  verifyPhoneNumber: async (code) => {
    try {
      const response = await instance.post("/security/mfa/phone/verify", {
        code
      });
      return response.data;
    } catch (error) {
      console.error("Verify phone number error:", error);
      throw error;
    }
  },

  // Resend phone verification code
  resendPhoneCode: async () => {
    try {
      const response = await instance.post("/security/mfa/phone/resend");
      return response.data;
    } catch (error) {
      console.error("Resend phone code error:", error);
      throw error;
    }
  },

  // Refresh access token
  refreshToken: async (refreshToken) => {
    try {
      const response = await instance.post("/auth/token/refresh", {
        refreshToken
      });
      return response.data;
    } catch (error) {
      console.error("Refresh token error:", error);
      throw error;
    }
  },
};