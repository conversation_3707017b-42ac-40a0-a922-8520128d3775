import instance from '../axios/instance.jsx';

// Loan Activities API endpoints
const ENDPOINTS = {
  LOAN_ACTIVITIES: '/loan-activities',
  LOAN_ACTIVITY_BY_ID: (id) => `/loan-activities/${id}`,
  LOAN_ACTIVITIES_FOLLOWUPS: '/loan-activities/followups',
  UPDATE_FOLLOWUP_STATUS: (id) => `/loan-activities/${id}/followup-status`,
};

/**
 * Loan Activities Service
 * Handles all loan activity-related API operations including creation with file attachments
 */
export const loanActivitiesService = {
  /**
   * Create a new loan activity with optional file attachments
   * @param {Object} activityData - Loan activity data
   * @param {File[]} files - Array of files to upload (optional)
   * @returns {Promise<Object>} Created loan activity response
   */
  create: async (activityData, files = []) => {
    try {
      console.log('=== CREATING NEW LOAN ACTIVITY ===');
      console.log('Activity data:', activityData);
      console.log('Files to upload:', files?.length || 0);
      console.log(`API Endpoint: POST ${ENDPOINTS.LOAN_ACTIVITIES}`);

      // Validate required fields
      const validationErrors = validateLoanActivityData(activityData);
      if (validationErrors.length > 0) {
        throw new Error(`Validation failed: ${validationErrors.join(', ')}`);
      }

      // Create FormData for multipart/form-data submission
      const formData = new FormData();

      // Add required field
      formData.append('rm_user_id', activityData.rmUserId);

      // Add optional fields only if they have values
      if (activityData.loanClientId) {
        formData.append('loan_client_id', activityData.loanClientId);
      }
      if (activityData.loanAccountNumber) {
        formData.append('loan_account_number', activityData.loanAccountNumber);
      }
      if (activityData.purposeId) {
        formData.append('purpose_id', activityData.purposeId);
      }
      if (activityData.loanBalance) {
        formData.append('loan_balance', activityData.loanBalance.toString());
      }
      if (activityData.arrearsDays !== null && activityData.arrearsDays !== undefined) {
        formData.append('arrears_days', activityData.arrearsDays.toString());
      }
      if (activityData.comment) {
        formData.append('comment', activityData.comment);
      }
      if (activityData.viaApi !== undefined) {
        formData.append('via_api', activityData.viaApi.toString());
      }
      if (activityData.apiCallReference) {
        formData.append('api_call_reference', activityData.apiCallReference);
      }
      if (activityData.interactionType) {
        formData.append('interaction_type', activityData.interactionType);
      }
      if (activityData.callStatus) {
        formData.append('call_status', activityData.callStatus);
      }
      if (activityData.visitStatus) {
        formData.append('visit_status', activityData.visitStatus);
      }
      if (activityData.callDurationMinutes !== null && activityData.callDurationMinutes !== undefined) {
        formData.append('call_duration_minutes', activityData.callDurationMinutes.toString());
      }
      if (activityData.nextFollowupDate) {
        formData.append('next_followup_date', activityData.nextFollowupDate);
      }
      if (activityData.followupStatus) {
        formData.append('followup_status', activityData.followupStatus);
      }

      // Add file attachments
      if (files && files.length > 0) {
        files.forEach(file => {
          formData.append('attachments', file);
        });
      }

      // Log FormData contents for debugging
      console.log('FormData contents:');
      for (let [key, value] of formData.entries()) {
        if (value instanceof File) {
          console.log(`${key}: [File] ${value.name} (${value.size} bytes, ${value.type})`);
        } else {
          console.log(`${key}: ${value}`);
        }
      }
      console.log('=====================================');

      // Make API call
      const response = await instance.post(ENDPOINTS.LOAN_ACTIVITIES, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      console.log('Loan activity creation response:', response.data);
      console.log('====================================');

      return response.data;
    } catch (error) {
      console.error('Error creating loan activity:', error);
      
      // Handle different error types
      if (error.response?.status === 400) {
        throw new Error(error.response?.data?.message || 'Validation failed or invalid data provided');
      } else if (error.response?.status === 404) {
        throw new Error('RM user, loan client, or purpose not found');
      } else if (error.response?.status >= 500) {
        throw new Error('Server error while creating loan activity. Please try again later.');
      } else {
        throw new Error(error.response?.data?.message || error.message || 'Failed to create loan activity');
      }
    }
  },

  /**
   * Get all loan activities with filtering and pagination
   * @param {number} page - Page number (default: 1)
   * @param {number} limit - Items per page (default: 10)
   * @param {Object} filters - Filter options
   * @returns {Promise<Object>} Loan activities response
   */
  getAll: async (page = 1, limit = 10, filters = {}) => {
    try {
      console.log('=== FETCHING ALL LOAN ACTIVITIES ===');
      console.log('Parameters:', { page, limit, filters });

      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      });

      // Add filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value) {
          params.append(key, value);
        }
      });

      const url = `${ENDPOINTS.LOAN_ACTIVITIES}?${params}`;
      console.log(`API Endpoint: GET ${url}`);

      const response = await instance.get(url);

      console.log('Loan activities response status:', response.status);
      console.log('Loan activities response data:', response.data);
      console.log('====================================');

      return response.data;
    } catch (error) {
      console.error('Error fetching loan activities:', error);
      console.error('Error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        url: error.config?.url
      });
      throw error;
    }
  },

  /**
   * Get all loan activity follow-ups with filtering and pagination
   * @param {number} page - Page number (default: 1)
   * @param {number} limit - Items per page (default: 10)
   * @param {string} status - Filter by status (optional)
   * @returns {Promise<Object>} Follow-ups response
   */
  getFollowups: async (page = 1, limit = 10, status = null) => {
    try {
      console.log('=== FETCHING LOAN ACTIVITY FOLLOWUPS ===');

      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      });

      if (status) {
        params.append('status', status);
      }

      const url = `${ENDPOINTS.LOAN_ACTIVITIES_FOLLOWUPS}?${params}`;
      console.log(`API Endpoint: GET ${url}`);

      const response = await instance.get(url);

      console.log('Follow-ups response:', response.data);
      console.log('=====================================');

      return response.data;
    } catch (error) {
      console.error('Error fetching follow-ups:', error);
      console.error('Error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        url: error.config?.url
      });

      // If the followups endpoint doesn't exist, fall back to regular loan activities
      if (error.response?.status === 400 || error.response?.status === 404 || error.response?.status === 405) {
        console.log('Followups endpoint not available, falling back to loan activities with followup filter');
        try {
          const fallbackFilters = {};
          if (status) {
            fallbackFilters.followup_status = status;
          }
          // Add filter to only get activities with follow-up dates
          fallbackFilters.has_followup = 'true';

          return await this.getAll(page, limit, fallbackFilters);
        } catch (fallbackError) {
          console.error('Fallback to loan activities also failed:', fallbackError);
          // Return empty response structure if both fail
          return {
            data: [],
            total: 0,
            page: page,
            limit: limit,
            totalPages: 0
          };
        }
      }

      throw error;
    }
  },

  /**
   * Update follow-up status
   * @param {string} activityId - Loan activity ID
   * @param {string} newStatus - New follow-up status
   * @returns {Promise<Object>} Update response
   */
  updateFollowupStatus: async (activityId, newStatus) => {
    try {
      console.log('=== UPDATING FOLLOWUP STATUS ===');
      console.log(`Activity ID: ${activityId}`);
      console.log(`New Status: ${newStatus}`);
      console.log(`API Endpoint: PATCH ${ENDPOINTS.UPDATE_FOLLOWUP_STATUS(activityId)}`);

      const response = await instance.patch(
        ENDPOINTS.UPDATE_FOLLOWUP_STATUS(activityId),
        {
          followup_status: newStatus
        }
      );

      console.log('Status update response:', response.data);
      console.log('================================');

      return response.data;
    } catch (error) {
      console.error('Error updating followup status:', error);
      throw error;
    }
  },

  /**
   * Get loan activity by ID
   * @param {string} id - Loan activity ID
   * @returns {Promise<Object>} Loan activity response
   */
  getById: async (id) => {
    try {
      console.log('=== FETCHING LOAN ACTIVITY BY ID ===');
      console.log(`Activity ID: ${id}`);
      console.log(`API Endpoint: GET ${ENDPOINTS.LOAN_ACTIVITY_BY_ID(id)}`);
      
      const response = await instance.get(ENDPOINTS.LOAN_ACTIVITY_BY_ID(id));
      
      console.log('Loan activity response:', response.data);
      console.log('===================================');
      
      return response.data;
    } catch (error) {
      console.error('Error fetching loan activity by ID:', error);
      throw error;
    }
  },
};

/**
 * Validate loan activity data before submission
 * @param {Object} data - Loan activity data to validate
 * @returns {Array} Array of validation error messages
 */
export const validateLoanActivityData = (data) => {
  const errors = [];

  // Required field validation
  if (!data.rmUserId) {
    errors.push('RM User ID is required');
  } else if (!isValidUUID(data.rmUserId)) {
    errors.push('RM User ID must be a valid UUID');
  }

  // Optional UUID field validations
  if (data.loanClientId && !isValidUUID(data.loanClientId)) {
    errors.push('Loan Client ID must be a valid UUID');
  }
  if (data.purposeId && !isValidUUID(data.purposeId)) {
    errors.push('Purpose ID must be a valid UUID');
  }

  // String length validations
  if (data.loanAccountNumber && data.loanAccountNumber.length > 50) {
    errors.push('Loan account number must be 50 characters or less');
  }
  if (data.comment && data.comment.length > 1000) {
    errors.push('Comment must be 1000 characters or less');
  }
  if (data.apiCallReference && data.apiCallReference.length > 255) {
    errors.push('API call reference must be 255 characters or less');
  }
  if (data.interactionType && data.interactionType.length > 50) {
    errors.push('Interaction type must be 50 characters or less');
  }
  if (data.callStatus && data.callStatus.length > 50) {
    errors.push('Call status must be 50 characters or less');
  }
  if (data.visitStatus && data.visitStatus.length > 50) {
    errors.push('Visit status must be 50 characters or less');
  }
  if (data.followupStatus && data.followupStatus.length > 50) {
    errors.push('Followup status must be 50 characters or less');
  }

  // Number validations
  if (data.arrearsDays !== null && data.arrearsDays !== undefined) {
    if (!Number.isInteger(data.arrearsDays) || data.arrearsDays < 0) {
      errors.push('Arrears days must be a non-negative integer');
    }
  }
  if (data.callDurationMinutes !== null && data.callDurationMinutes !== undefined) {
    if (!Number.isInteger(data.callDurationMinutes) || data.callDurationMinutes < 0) {
      errors.push('Call duration minutes must be a non-negative integer');
    }
  }

  // Decimal validation for loan balance
  if (data.loanBalance) {
    if (!isValidDecimal(data.loanBalance)) {
      errors.push('Loan balance must be a valid decimal with up to 2 decimal places');
    }
  }

  // Date validation
  if (data.nextFollowupDate && !isValidISODate(data.nextFollowupDate)) {
    errors.push('Next followup date must be a valid ISO 8601 datetime string');
  }

  return errors;
};

/**
 * Validate UUID format
 * @param {string} uuid - UUID string to validate
 * @returns {boolean} True if valid UUID
 */
export const isValidUUID = (uuid) => {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
};

/**
 * Validate decimal format with up to 2 decimal places
 * @param {string|number} value - Value to validate
 * @returns {boolean} True if valid decimal
 */
export const isValidDecimal = (value) => {
  const decimalRegex = /^\d+(\.\d{1,2})?$/;
  return decimalRegex.test(value.toString());
};

/**
 * Validate ISO 8601 date format
 * @param {string} dateString - Date string to validate
 * @returns {boolean} True if valid ISO date
 */
export const isValidISODate = (dateString) => {
  try {
    const date = new Date(dateString);
    return date.toISOString() === dateString;
  } catch {
    return false;
  }
};

/**
 * Format loan activity data for table display
 * @param {Object} apiResponse - Response from loan activities API
 * @returns {Array} Formatted data for DataTable
 */
export const formatLoanActivitiesForTable = (apiResponse) => {
  if (!apiResponse || !apiResponse.data) {
    return [];
  }

  return apiResponse.data.map((activity, index) => ({
    id: activity.id || `loan-activity-${index + 1}`,
    // New 5-column structure matching leads calls table
    name: activity.loan_client?.customer_name || 'Unknown Customer',
    anchor: activity.rm_user?.name || 'Unknown RM',
    mobile: 'N/A', // Phone number not available in loan activities data
    status: activity.call_status || 'Unknown',
    date: formatDate(activity.created_at),

    // Keep additional fields for backward compatibility and detailed operations
    loanClientId: activity.loan_client_id,
    loanAccountNumber: activity.loan_account_number || 'N/A',
    customerName: activity.loan_client?.customer_name || 'Unknown Customer',
    rmUser: activity.rm_user?.name || 'Unknown RM',
    rmCode: activity.rm_user?.rm_code || 'N/A',
    purpose: activity.purpose?.name || 'N/A',
    loanBalance: activity.loan_balance ? formatCurrency(activity.loan_balance) : 'N/A',
    arrearsDays: activity.arrears_days || 0,
    interactionType: activity.interaction_type || 'N/A',
    callStatus: activity.call_status || 'N/A',
    visitStatus: activity.visit_status || 'N/A',
    callDuration: activity.call_duration_minutes ? `${activity.call_duration_minutes} min` : 'N/A',
    nextFollowup: activity.next_followup_date ? formatDate(activity.next_followup_date) : 'N/A',
    followupStatus: activity.followup_status || 'N/A',
    comment: activity.comment || 'No comment',
    createdAt: formatDate(activity.created_at),
    updatedAt: formatDate(activity.updated_at),
    viaApi: activity.via_api || false,
    apiCallReference: activity.api_call_reference || 'N/A',
    attachments: activity.attachments || [],
    // Store original data for detailed view
    originalData: activity,
  }));
};

/**
 * Format follow-ups data for table display
 * @param {Object} apiResponse - Response from follow-ups API
 * @returns {Array} Formatted data for DataTable
 */
export const formatFollowupsForTable = (apiResponse) => {
  if (!apiResponse || !apiResponse.data) {
    return [];
  }

  return apiResponse.data.map((followup, index) => ({
    id: followup.id || `followup-${index + 1}`,
    customerId: followup.customer_id,
    customerName: followup.customer_name || 'Unknown Customer',
    anchorName: followup.anchor_name || 'N/A',
    assignedOfficer: followup.assigned_officer || 'Unknown Officer',
    assignedOfficerId: followup.assigned_officer_id,
    followupDate: followup.followup_date ? formatDate(followup.followup_date) : 'N/A',
    status: followup.status || 'pending',
    createdDate: formatDate(followup.created_date),
    // Store original data for detailed view
    originalData: followup,
  }));
};

/**
 * Format loan activities data as follow-ups for table display (fallback)
 * @param {Object} apiResponse - Response from loan activities API
 * @returns {Array} Formatted data for DataTable
 */
export const formatLoanActivitiesAsFollowups = (apiResponse) => {
  if (!apiResponse || !apiResponse.data) {
    return [];
  }

  return apiResponse.data
    .filter(activity => activity.next_followup_date) // Only activities with follow-up dates
    .map((activity, index) => ({
      id: activity.id || `followup-${index + 1}`,
      customerId: activity.loan_client_id,
      customerName: activity.loan_client?.customer_name || 'Unknown Customer',
      anchorName: 'N/A', // Not available in loan activities
      assignedOfficer: activity.rm_user?.name || 'Unknown Officer',
      assignedOfficerId: activity.rm_user_id,
      followupDate: activity.next_followup_date ? formatDate(activity.next_followup_date) : 'N/A',
      scheduledDate: activity.next_followup_date ? formatDate(activity.next_followup_date) : 'N/A', // Add scheduledDate field
      date: activity.next_followup_date ? formatDate(activity.next_followup_date) : 'N/A', // Add date field for filtering
      status: activity.followup_status || 'pending',
      createdDate: formatDate(activity.created_at),
      // Store original data for detailed view
      originalData: activity,
    }));
};

/**
 * Format currency for display
 * @param {string|number} amount - Amount to format
 * @returns {string} Formatted currency
 */
const formatCurrency = (amount) => {
  if (!amount) return 'N/A';
  return new Intl.NumberFormat('en-KE', {
    style: 'currency',
    currency: 'KES',
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(amount);
};

/**
 * Format date for display
 * @param {string} dateString - ISO date string
 * @returns {string} Formatted date
 */
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    console.warn('Error formatting date:', dateString);
    return 'Invalid Date';
  }
};

export default loanActivitiesService;
