:root {
  --primary: #333333;
  --secondary: #A336C9;
  --primary-hover: #2ba14c;
  --primary-light: #afe9bf;
  --primary-dark: #165026;
  --rgba-primary-1: rgba(54, 201, 95, 0.1);
  --rgba-primary-2: rgba(54, 201, 95, 0.2);
  --rgba-primary-3: rgba(54, 201, 95, 0.3);
  --rgba-primary-4: rgba(54, 201, 95, 0.4);
  --rgba-primary-5: rgba(54, 201, 95, 0.5);
  --rgba-primary-6: rgba(54, 201, 95, 0.6);
  --rgba-primary-7: rgba(54, 201, 95, 0.7);
  --rgba-primary-8: rgba(54, 201, 95, 0.8);
  --rgba-primary-9: rgba(54, 201, 95, 0.9);
}

body {
  font-family: 'Poppins', sans-serif;
}


.hamburger {
  display: inline-block;
  left: 0px;
  position: relative;
  top: 3px;
  -webkit-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  width: 26px;
  z-index: 10;
}

.hamburger .line {
  background: var(--primary) !important;
  display: block;
  height: 3px;
  border-radius: 3px;
  margin-top: 6px;
  margin-bottom: 6px;
  margin-left: auto;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

/* Dark mode hamburger */
.dark .hamburger .line {
  background: #ffffff;
}

.hamburger .line:nth-child(1) {
  width: 20px;
}

.hamburger .line:nth-child(2) {
  width: 26px;
}

.hamburger .line:nth-child(3) {
  width: 22px;
}

.hamburger:hover {
  cursor: pointer;
}

.hamburger:hover .line {
  width: 26px;
}

.hamburger.is-active .line:nth-child(1),
.hamburger.is-active .line:nth-child(3) {
  width: 10px;
  height: 2px;
}

.hamburger.is-active .line:nth-child(2) {
  -webkit-transform: translateX(0px);
  transform: translateX(0px);
  width: 22px;
  height: 2px;
}

.hamburger.is-active .line:nth-child(1) {
  -webkit-transform: translateY(4px) rotate(45deg);
  transform: translateY(4px) rotate(45deg);
}

.hamburger.is-active .line:nth-child(3) {
  -webkit-transform: translateY(-4px) rotate(-45deg);
  transform: translateY(-4px) rotate(-45deg);
}

button {
  cursor: pointer;
}



.custom-otp-input {
  width: 48px;
  height: 48px;
  font-size: 24px;
  appearance: none;
  text-align: center;
  transition: all 0.2s;
  border-radius: 8px;
  border: 1px solid rgb(209 213 219);
  background: transparent;
  outline-offset: -2px;
  outline-color: transparent;
  transition: outline-color 0.3s;
  color: rgb(17 24 39);
  margin: 0 2px;
}

.dark .custom-otp-input {
  border-color: rgb(75 85 99);
  color: rgb(243 244 246);
  background: rgb(55 65 81);
}

.custom-otp-input:focus {
  outline: 2px solid rgb(34 197 94);
  border-color: rgb(34 197 94);
}

/* Container for OTP inputs */
.p-inputotp {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 4px !important;
  flex-wrap: nowrap !important;
}

/* IntlTelInput styling to match theme */
.iti {
  width: 100% !important;
}

.iti__input {
  width: 100% !important;
  border: 1px solid rgb(209 213 219) !important;
  border-radius: 0.5rem !important;
  padding: 0.75rem 1rem !important;
  background-color: rgb(255 255 255) !important;
  color: rgb(17 24 39) !important;
  transition: border-color 0.2s ease-in-out !important;
}

.iti__input:focus {
  outline: none !important;
  border-color: rgb(34 197 94) !important;
}

.iti__input::placeholder {
  color: rgb(156 163 175) !important;
}

/* Dark mode for IntlTelInput */
.dark .iti__input {
  background-color: rgb(55 65 81) !important;
  border-color: rgb(75 85 99) !important;
  color: rgb(243 244 246) !important;
}

.dark .iti__input:focus {
  border-color: rgb(34 197 94) !important;
}

.dark .iti__input::placeholder {
  color: rgb(156 163 175) !important;
}

.iti__dropdown {
  background-color: rgb(255 255 255) !important;
  border: 1px solid rgb(209 213 219) !important;
  border-radius: 0.5rem !important;
}

.dark .iti__dropdown {
  background-color: rgb(55 65 81) !important;
  border-color: rgb(75 85 99) !important;
}

.iti__country:hover {
  background-color: rgb(243 244 246) !important;
}

.dark .iti__country:hover {
  background-color: rgb(75 85 99) !important;
}

.iti__country.iti__highlight {
  background-color: rgb(34 197 94) !important;
  color: white !important;
}

/* Make sure the dash separator is inline */
.p-inputotp .px-3 {
  display: inline-flex;
  align-items: center;
  margin: 0 8px;
}