# Multi-stage Dockerfile for React development and production

# Base stage with common dependencies
FROM node:20-alpine AS base

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --silent

# Copy source code
COPY . .

# Development stage
FROM base AS development

# Install development dependencies if not already installed
RUN npm ci --silent

# Expose Vite dev server port
EXPOSE 5173

# Set environment to development
ENV NODE_ENV=development

# Start development server with host binding for Docker
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0", "--port", "5173"]

# Build stage
FROM base AS builder

# Set Node.js memory limit and build with optimizations
ENV NODE_OPTIONS="--max-old-space-size=4096"

# Build the React app for production
RUN npm run build

# Production stage
FROM nginx:alpine AS production

# Copy built React app from builder stage
COPY --from=builder /app/dist /usr/share/nginx/html

# Create nginx configuration that works with non-root user
<PERSON>UN mkdir -p /tmp/nginx/client_temp /tmp/nginx/proxy_temp \
    /tmp/nginx/fastcgi_temp /tmp/nginx/uwsgi_temp /tmp/nginx/scgi_temp && \
    chown -R nginx:nginx /tmp/nginx /usr/share/nginx/html && \
    chmod -R 755 /usr/share/nginx/html /tmp/nginx

# Create custom nginx configuration
RUN cat > /etc/nginx/nginx.conf << 'EOF'
worker_processes auto;
error_log /tmp/nginx/error.log warn;
pid /tmp/nginx/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    access_log /tmp/nginx/access.log;

    # Set cache directories to writable locations
    client_body_temp_path /tmp/nginx/client_temp;
    proxy_temp_path /tmp/nginx/proxy_temp;
    fastcgi_temp_path /tmp/nginx/fastcgi_temp;
    uwsgi_temp_path /tmp/nginx/uwsgi_temp;
    scgi_temp_path /tmp/nginx/scgi_temp;

    sendfile on;
    keepalive_timeout 65;
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    server {
        listen 8080;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        location / {
            try_files $uri $uri/ /index.html;
        }

        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}
EOF

# Create startup script to ensure directories exist
RUN cat > /docker-entrypoint.d/40-create-temp-dirs.sh << 'EOF'
#!/bin/sh
set -e

# Ensure temp directories exist with correct permissions
mkdir -p /tmp/nginx/client_temp /tmp/nginx/proxy_temp \
    /tmp/nginx/fastcgi_temp /tmp/nginx/uwsgi_temp /tmp/nginx/scgi_temp
chown -R nginx:nginx /tmp/nginx
chmod -R 755 /tmp/nginx

echo "Nginx temp directories created successfully"
EOF

RUN chmod +x /docker-entrypoint.d/40-create-temp-dirs.sh

# Switch to nginx user
USER nginx

# Expose port 8080 (non-privileged port)
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

# Start Nginx
CMD ["nginx", "-g", "daemon off;"]