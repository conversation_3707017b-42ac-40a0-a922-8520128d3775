# version: "3.8"

services:
  kb-tracker-frontend:
    image: kb-tracker-frontend:latest
    container_name: kb-tracker-frontend-prod
    ports:
      - "${HOST_PORT:-3000}:8080" # Use environment variable with default
    restart: unless-stopped
    environment:
      - NODE_ENV=production
    healthcheck:
      test:
        [
          "CMD",
          "wget",
          "--no-verbose",
          "--tries=1",
          "--spider",
          "http://localhost:8080/health",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    # Resource limits for production
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: "0.5"
        reservations:
          memory: 256M
          cpus: "0.25"
    # Security options
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
      - /var/cache/nginx
    networks:
      - kb-tracker-network
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  kb-tracker-network:
    driver: bridge
